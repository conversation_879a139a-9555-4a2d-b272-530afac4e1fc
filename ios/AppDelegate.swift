// ios/AppDelegate.swift
import UIKit
import CarPlay
import React
import Firebase
import UserNotifications
#if DEBUG
#if FB_SONARKIT_ENABLED
//import FlipperKit
#endif
#endif
@main
class AppDelegate: UIResponder, UIApplicationDelegate, RCTBridgeDelegate, UNUserNotificationCenterDelegate {
  var window: UIWindow?
  var bridge: RCTBridge?;
  var rootView: RCTRootView?;
  static var shared: AppDelegate { return UIApplication.shared.delegate as! AppDelegate }
  func sourceURL(for bridge: RCTBridge!) -> URL! {
    #if DEBUG
    return RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index");
    #else
    return Bundle.main.url(forResource:"main", withExtension:"jsbundle")
    #endif
  }
  func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
//    initializeFlipper(with: application)
    FirebaseApp.configure()
    UNUserNotificationCenter.current().requestAuthorization(
      options: [.alert, .badge, .sound, .carPlay]
            ) { granted, error in
                if let error = error {
                    print("Error requesting notification permissions: \(error.localizedDescription)")
                } else {
                    print("Notification permissions granted: \(granted)")
                }
            }
    UNUserNotificationCenter.current().delegate = self
    
    self.bridge = RCTBridge.init(delegate: self, launchOptions: launchOptions)
    self.rootView = RCTRootView.init(bridge: self.bridge!, moduleName: "maxicosi", initialProperties: nil)
    return true
  }
  func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
    if (connectingSceneSession.role == UISceneSession.Role.carTemplateApplication) {
      let scene =  UISceneConfiguration(name: "CarPlay", sessionRole: connectingSceneSession.role)
      scene.delegateClass = CarSceneDelegate.self
      return scene
    } else {
      let scene =  UISceneConfiguration(name: "Phone", sessionRole: connectingSceneSession.role)
      scene.delegateClass = PhoneSceneDelegate.self
      return scene
    }
  }
  func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
  }
//  private func initializeFlipper(with application: UIApplication) {
//    #if DEBUG
//    #if FB_SONARKIT_ENABLED
//    let client = FlipperClient.shared()
//    let layoutDescriptorMapper = SKDescriptorMapper(defaults: ())
//    client?.add(FlipperKitLayoutPlugin(rootNode: application, with: layoutDescriptorMapper!))
//    client?.add(FKUserDefaultsPlugin(suiteName: nil))
//    client?.add(FlipperKitReactPlugin())
//    client?.add(FlipperKitNetworkPlugin(networkAdapter: SKIOSNetworkAdapter()))
//    client?.start()
//    #endif
//    #endif
//  }
}
