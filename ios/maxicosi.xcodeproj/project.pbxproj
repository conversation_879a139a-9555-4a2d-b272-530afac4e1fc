// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* maxicosiTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* maxicosiTests.m */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		2D02E4BD1E0B4A84006451C7 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		2DCD954D1E0B4F2C00145EB5 /* maxicosiTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* maxicosiTests.m */; };
		3137991BFDAECFB214893029 /* libPods-maxicosi-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = EA0BAA258BCE27C29CD7B060 /* libPods-maxicosi-tvOS.a */; };
		4DF5AFEBEAC7EA7DCDDD3403 /* libPods-maxicosi-maxicosiTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 936DCA966C3CEDB6C1DF4E15 /* libPods-maxicosi-maxicosiTests.a */; };
		5BC695EB2E4E1BDC00714710 /* CarPlayEventEmitter.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BC695E92E4E1BDC00714710 /* CarPlayEventEmitter.m */; };
		5BC695EC2E4E1BDC00714710 /* CarPlayEventEmitter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5BC695EA2E4E1BDC00714710 /* CarPlayEventEmitter.swift */; };
		65A19DECA879FE046DFC566B /* libPods-maxicosi-tvOSTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 706F1C7D8C10A003BF1FC333 /* libPods-maxicosi-tvOSTests.a */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		BB0D34021FD80C160901338D /* libPods-maxicosi.a in Frameworks */ = {isa = PBXBuildFile; fileRef = E3F0A158CA44840D15930365 /* libPods-maxicosi.a */; };
		BF7845CB2CECA9A000DA5F25 /* MaxRCTCarPlayNotificationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BF7845C92CECA9A000DA5F25 /* MaxRCTCarPlayNotificationManager.swift */; };
		BF7845CD2CECA9AF00DA5F25 /* MaxRCTCarPlayNotificationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BF7845CC2CECA9AF00DA5F25 /* MaxRCTCarPlayNotificationManager.m */; };
		CA819105260371FA00367930 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = CA819104260371FA00367930 /* GoogleService-Info.plist */; };
		CA95300B26049F3E001E20A7 /* Rubik-VariableFont_wght.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CA95300A26049F3E001E20A7 /* Rubik-VariableFont_wght.ttf */; };
		CA95301126049F58001E20A7 /* Rubik-Italic-VariableFont_wght.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CA95301026049F58001E20A7 /* Rubik-Italic-VariableFont_wght.ttf */; };
		CAADE8C526171F5C00787AC3 /* icomoon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CAADE8C426171F5C00787AC3 /* icomoon.ttf */; };
		DD5FD81E2AB1958500E9F038 /* CarScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD5FD81D2AB1958500E9F038 /* CarScene.swift */; };
		DDA74CAB2AB172E90076754B /* Entitlements.plist in Resources */ = {isa = PBXBuildFile; fileRef = DDA74CAA2AB172E90076754B /* Entitlements.plist */; };
		DDA74CAE2AB173000076754B /* PhoneScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = DDA74CAD2AB173000076754B /* PhoneScene.swift */; };
		DDCB79602AC5204700A985F2 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DDCB795F2AC5204700A985F2 /* AppDelegate.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = maxicosi;
		};
		2D02E4911E0B4A5D006451C7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2D02E47A1E0B4A5D006451C7;
			remoteInfo = "maxicosi-tvOS";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* maxicosiTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = maxicosiTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* maxicosiTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = maxicosiTests.m; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* maxicosi.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = maxicosi.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = maxicosi/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = maxicosi/Info.plist; sourceTree = "<group>"; };
		28BC3D9C1218BD353CA69BC6 /* Pods-maxicosi.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-maxicosi.release.xcconfig"; path = "Target Support Files/Pods-maxicosi/Pods-maxicosi.release.xcconfig"; sourceTree = "<group>"; };
		2D02E47B1E0B4A5D006451C7 /* maxicosi-tvOS.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "maxicosi-tvOS.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		2D02E4901E0B4A5D006451C7 /* maxicosi-tvOSTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "maxicosi-tvOSTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		301D08C321A11A3ECF48B199 /* Pods-maxicosi.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-maxicosi.debug.xcconfig"; path = "Target Support Files/Pods-maxicosi/Pods-maxicosi.debug.xcconfig"; sourceTree = "<group>"; };
		5BC695E92E4E1BDC00714710 /* CarPlayEventEmitter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CarPlayEventEmitter.m; sourceTree = "<group>"; };
		5BC695EA2E4E1BDC00714710 /* CarPlayEventEmitter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CarPlayEventEmitter.swift; sourceTree = "<group>"; };
		6FFFAA77735A8FD7C6CAE708 /* Pods-maxicosi-maxicosiTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-maxicosi-maxicosiTests.debug.xcconfig"; path = "Target Support Files/Pods-maxicosi-maxicosiTests/Pods-maxicosi-maxicosiTests.debug.xcconfig"; sourceTree = "<group>"; };
		706F1C7D8C10A003BF1FC333 /* libPods-maxicosi-tvOSTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-maxicosi-tvOSTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = maxicosi/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8A0ECC90F20C3E9858D4810C /* Pods-maxicosi-tvOSTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-maxicosi-tvOSTests.release.xcconfig"; path = "Target Support Files/Pods-maxicosi-tvOSTests/Pods-maxicosi-tvOSTests.release.xcconfig"; sourceTree = "<group>"; };
		936DCA966C3CEDB6C1DF4E15 /* libPods-maxicosi-maxicosiTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-maxicosi-maxicosiTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		AD6A27FEF739A762611AD916 /* Pods-maxicosi-tvOS.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-maxicosi-tvOS.release.xcconfig"; path = "Target Support Files/Pods-maxicosi-tvOS/Pods-maxicosi-tvOS.release.xcconfig"; sourceTree = "<group>"; };
		B05B5532D0045320B94EC0EC /* Pods-maxicosi-tvOSTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-maxicosi-tvOSTests.debug.xcconfig"; path = "Target Support Files/Pods-maxicosi-tvOSTests/Pods-maxicosi-tvOSTests.debug.xcconfig"; sourceTree = "<group>"; };
		BF7845C92CECA9A000DA5F25 /* MaxRCTCarPlayNotificationManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MaxRCTCarPlayNotificationManager.swift; sourceTree = "<group>"; };
		BF7845CC2CECA9AF00DA5F25 /* MaxRCTCarPlayNotificationManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MaxRCTCarPlayNotificationManager.m; sourceTree = "<group>"; };
		CA7D0EF2260387E400E9A331 /* Ionicons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = Ionicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf"; sourceTree = "<group>"; };
		CA7D0EF3260387E400E9A331 /* Entypo.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = Entypo.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Entypo.ttf"; sourceTree = "<group>"; };
		CA7D0EF4260387E400E9A331 /* FontAwesome.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = FontAwesome.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf"; sourceTree = "<group>"; };
		CA7D0EF5260387E400E9A331 /* Octicons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = Octicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Octicons.ttf"; sourceTree = "<group>"; };
		CA7D0EF6260387E400E9A331 /* Zocial.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = Zocial.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Zocial.ttf"; sourceTree = "<group>"; };
		CA7D0EF7260387E500E9A331 /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = MaterialCommunityIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf"; sourceTree = "<group>"; };
		CA7D0EF8260387E500E9A331 /* AntDesign.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = AntDesign.ttf; path = "../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf"; sourceTree = "<group>"; };
		CA7D0EF9260387E500E9A331 /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = FontAwesome5_Brands.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf"; sourceTree = "<group>"; };
		CA7D0EFA260387E500E9A331 /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SimpleLineIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf"; sourceTree = "<group>"; };
		CA7D0EFB260387E500E9A331 /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = FontAwesome5_Solid.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf"; sourceTree = "<group>"; };
		CA7D0EFC260387E500E9A331 /* Foundation.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = Foundation.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Foundation.ttf"; sourceTree = "<group>"; };
		CA7D0EFD260387E500E9A331 /* Feather.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = Feather.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Feather.ttf"; sourceTree = "<group>"; };
		CA7D0EFE260387E500E9A331 /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = FontAwesome5_Regular.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf"; sourceTree = "<group>"; };
		CA7D0EFF260387E500E9A331 /* EvilIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = EvilIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf"; sourceTree = "<group>"; };
		CA7D0F00260387E500E9A331 /* Fontisto.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = Fontisto.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Fontisto.ttf"; sourceTree = "<group>"; };
		CA7D0F01260387E500E9A331 /* MaterialIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = MaterialIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf"; sourceTree = "<group>"; };
		CA819104260371FA00367930 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		CA95300A26049F3E001E20A7 /* Rubik-VariableFont_wght.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Rubik-VariableFont_wght.ttf"; path = "../android/app/src/main/assets/fonts/Rubik-VariableFont_wght.ttf"; sourceTree = "<group>"; };
		CA95301026049F58001E20A7 /* Rubik-Italic-VariableFont_wght.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Rubik-Italic-VariableFont_wght.ttf"; path = "../android/app/src/main/assets/fonts/Rubik-Italic-VariableFont_wght.ttf"; sourceTree = "<group>"; };
		CA9530752604A7F6001E20A7 /* maxicosi.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = maxicosi.entitlements; path = maxicosi/maxicosi.entitlements; sourceTree = "<group>"; };
		CAADE8C426171F5C00787AC3 /* icomoon.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = icomoon.ttf; path = ../android/app/src/main/assets/fonts/icomoon.ttf; sourceTree = "<group>"; };
		DD5FD81D2AB1958500E9F038 /* CarScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CarScene.swift; sourceTree = "<group>"; };
		DDA74CAA2AB172E90076754B /* Entitlements.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Entitlements.plist; sourceTree = "<group>"; };
		DDA74CAC2AB172FF0076754B /* maxicosi-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "maxicosi-Bridging-Header.h"; sourceTree = "<group>"; };
		DDA74CAD2AB173000076754B /* PhoneScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhoneScene.swift; sourceTree = "<group>"; };
		DDCB795F2AC5204700A985F2 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		E2EF5130227179DC51FE51F3 /* Pods-maxicosi-maxicosiTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-maxicosi-maxicosiTests.release.xcconfig"; path = "Target Support Files/Pods-maxicosi-maxicosiTests/Pods-maxicosi-maxicosiTests.release.xcconfig"; sourceTree = "<group>"; };
		E3F0A158CA44840D15930365 /* libPods-maxicosi.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-maxicosi.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		E68861AF0C6C716739053ACA /* Pods-maxicosi-tvOS.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-maxicosi-tvOS.debug.xcconfig"; path = "Target Support Files/Pods-maxicosi-tvOS/Pods-maxicosi-tvOS.debug.xcconfig"; sourceTree = "<group>"; };
		EA0BAA258BCE27C29CD7B060 /* libPods-maxicosi-tvOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-maxicosi-tvOS.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		ED2971642150620600B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS12.0.sdk/System/Library/Frameworks/JavaScriptCore.framework; sourceTree = DEVELOPER_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				4DF5AFEBEAC7EA7DCDDD3403 /* libPods-maxicosi-maxicosiTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				BB0D34021FD80C160901338D /* libPods-maxicosi.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4781E0B4A5D006451C7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				3137991BFDAECFB214893029 /* libPods-maxicosi-tvOS.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48D1E0B4A5D006451C7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				65A19DECA879FE046DFC566B /* libPods-maxicosi-tvOSTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* maxicosiTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* maxicosiTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = maxicosiTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* maxicosi */ = {
			isa = PBXGroup;
			children = (
				BF7845CA2CECA9A000DA5F25 /* MaxRCTCarPlayNotificationManager */,
				DDA74CAA2AB172E90076754B /* Entitlements.plist */,
				CA9530752604A7F6001E20A7 /* maxicosi.entitlements */,
				CA819104260371FA00367930 /* GoogleService-Info.plist */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				5BC695E92E4E1BDC00714710 /* CarPlayEventEmitter.m */,
				5BC695EA2E4E1BDC00714710 /* CarPlayEventEmitter.swift */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				DDA74CAD2AB173000076754B /* PhoneScene.swift */,
				DDA74CAC2AB172FF0076754B /* maxicosi-Bridging-Header.h */,
				DD5FD81D2AB1958500E9F038 /* CarScene.swift */,
				DDCB795F2AC5204700A985F2 /* AppDelegate.swift */,
			);
			name = maxicosi;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				ED2971642150620600B7C4FE /* JavaScriptCore.framework */,
				E3F0A158CA44840D15930365 /* libPods-maxicosi.a */,
				936DCA966C3CEDB6C1DF4E15 /* libPods-maxicosi-maxicosiTests.a */,
				EA0BAA258BCE27C29CD7B060 /* libPods-maxicosi-tvOS.a */,
				706F1C7D8C10A003BF1FC333 /* libPods-maxicosi-tvOSTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				CAADE8C426171F5C00787AC3 /* icomoon.ttf */,
				CA95301026049F58001E20A7 /* Rubik-Italic-VariableFont_wght.ttf */,
				CA95300A26049F3E001E20A7 /* Rubik-VariableFont_wght.ttf */,
				CA7D0EF8260387E500E9A331 /* AntDesign.ttf */,
				CA7D0EF3260387E400E9A331 /* Entypo.ttf */,
				CA7D0EFF260387E500E9A331 /* EvilIcons.ttf */,
				CA7D0EFD260387E500E9A331 /* Feather.ttf */,
				CA7D0EF4260387E400E9A331 /* FontAwesome.ttf */,
				CA7D0EF9260387E500E9A331 /* FontAwesome5_Brands.ttf */,
				CA7D0EFE260387E500E9A331 /* FontAwesome5_Regular.ttf */,
				CA7D0EFB260387E500E9A331 /* FontAwesome5_Solid.ttf */,
				CA7D0F00260387E500E9A331 /* Fontisto.ttf */,
				CA7D0EFC260387E500E9A331 /* Foundation.ttf */,
				CA7D0EF2260387E400E9A331 /* Ionicons.ttf */,
				CA7D0EF7260387E500E9A331 /* MaterialCommunityIcons.ttf */,
				CA7D0F01260387E500E9A331 /* MaterialIcons.ttf */,
				CA7D0EF5260387E400E9A331 /* Octicons.ttf */,
				CA7D0EFA260387E500E9A331 /* SimpleLineIcons.ttf */,
				CA7D0EF6260387E400E9A331 /* Zocial.ttf */,
				13B07FAE1A68108700A75B9A /* maxicosi */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* maxicosiTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				F46CDB18728775F3190CC6C7 /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* maxicosi.app */,
				00E356EE1AD99517003FC87E /* maxicosiTests.xctest */,
				2D02E47B1E0B4A5D006451C7 /* maxicosi-tvOS.app */,
				2D02E4901E0B4A5D006451C7 /* maxicosi-tvOSTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BF7845CA2CECA9A000DA5F25 /* MaxRCTCarPlayNotificationManager */ = {
			isa = PBXGroup;
			children = (
				BF7845C92CECA9A000DA5F25 /* MaxRCTCarPlayNotificationManager.swift */,
				BF7845CC2CECA9AF00DA5F25 /* MaxRCTCarPlayNotificationManager.m */,
			);
			name = MaxRCTCarPlayNotificationManager;
			path = maxicosi/MaxRCTCarPlayNotificationManager;
			sourceTree = "<group>";
		};
		F46CDB18728775F3190CC6C7 /* Pods */ = {
			isa = PBXGroup;
			children = (
				301D08C321A11A3ECF48B199 /* Pods-maxicosi.debug.xcconfig */,
				28BC3D9C1218BD353CA69BC6 /* Pods-maxicosi.release.xcconfig */,
				6FFFAA77735A8FD7C6CAE708 /* Pods-maxicosi-maxicosiTests.debug.xcconfig */,
				E2EF5130227179DC51FE51F3 /* Pods-maxicosi-maxicosiTests.release.xcconfig */,
				E68861AF0C6C716739053ACA /* Pods-maxicosi-tvOS.debug.xcconfig */,
				AD6A27FEF739A762611AD916 /* Pods-maxicosi-tvOS.release.xcconfig */,
				B05B5532D0045320B94EC0EC /* Pods-maxicosi-tvOSTests.debug.xcconfig */,
				8A0ECC90F20C3E9858D4810C /* Pods-maxicosi-tvOSTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* maxicosiTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "maxicosiTests" */;
			buildPhases = (
				3A4F872F27CCCE08127EDB06 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				7A0320C0808124F2482DE24D /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = maxicosiTests;
			productName = maxicosiTests;
			productReference = 00E356EE1AD99517003FC87E /* maxicosiTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* maxicosi */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "maxicosi" */;
			buildPhases = (
				64BDBE113165786D73B3710F /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				18F3A552265E6C59009DC027 /* Set Build Number */,
				88A371C38E148E8EB05D9D14 /* [CP] Copy Pods Resources */,
				151130AA049A9032D4CE4ADC /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = maxicosi;
			productName = maxicosi;
			productReference = 13B07F961A680F5B00A75B9A /* maxicosi.app */;
			productType = "com.apple.product-type.application";
		};
		2D02E47A1E0B4A5D006451C7 /* maxicosi-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D02E4BA1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "maxicosi-tvOS" */;
			buildPhases = (
				958BFF1055979B20783CFDE6 /* [CP] Check Pods Manifest.lock */,
				FD10A7F122414F3F0027D42C /* Start Packager */,
				2D02E4771E0B4A5D006451C7 /* Sources */,
				2D02E4781E0B4A5D006451C7 /* Frameworks */,
				2D02E4791E0B4A5D006451C7 /* Resources */,
				2D02E4CB1E0B4B27006451C7 /* Bundle React Native Code And Images */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "maxicosi-tvOS";
			productName = "maxicosi-tvOS";
			productReference = 2D02E47B1E0B4A5D006451C7 /* maxicosi-tvOS.app */;
			productType = "com.apple.product-type.application";
		};
		2D02E48F1E0B4A5D006451C7 /* maxicosi-tvOSTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D02E4BB1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "maxicosi-tvOSTests" */;
			buildPhases = (
				148AD3C28D556A97A0BD08F8 /* [CP] Check Pods Manifest.lock */,
				2D02E48C1E0B4A5D006451C7 /* Sources */,
				2D02E48D1E0B4A5D006451C7 /* Frameworks */,
				2D02E48E1E0B4A5D006451C7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2D02E4921E0B4A5D006451C7 /* PBXTargetDependency */,
			);
			name = "maxicosi-tvOSTests";
			productName = "maxicosi-tvOSTests";
			productReference = 2D02E4901E0B4A5D006451C7 /* maxicosi-tvOSTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1130;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						DevelopmentTeam = 8S9R52T2SS;
						LastSwiftMigration = 1410;
						ProvisioningStyle = Automatic;
					};
					2D02E47A1E0B4A5D006451C7 = {
						CreatedOnToolsVersion = 8.2.1;
						ProvisioningStyle = Automatic;
					};
					2D02E48F1E0B4A5D006451C7 = {
						CreatedOnToolsVersion = 8.2.1;
						ProvisioningStyle = Automatic;
						TestTargetID = 2D02E47A1E0B4A5D006451C7;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "maxicosi" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* maxicosi */,
				00E356ED1AD99517003FC87E /* maxicosiTests */,
				2D02E47A1E0B4A5D006451C7 /* maxicosi-tvOS */,
				2D02E48F1E0B4A5D006451C7 /* maxicosi-tvOSTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				CAADE8C526171F5C00787AC3 /* icomoon.ttf in Resources */,
				CA95301126049F58001E20A7 /* Rubik-Italic-VariableFont_wght.ttf in Resources */,
				CA95300B26049F3E001E20A7 /* Rubik-VariableFont_wght.ttf in Resources */,
				CA819105260371FA00367930 /* GoogleService-Info.plist in Resources */,
				DDA74CAB2AB172E90076754B /* Entitlements.plist in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4791E0B4A5D006451C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				2D02E4BD1E0B4A84006451C7 /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48E1E0B4A5D006451C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh\n";
		};
		148AD3C28D556A97A0BD08F8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-maxicosi-tvOSTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		151130AA049A9032D4CE4ADC /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		18F3A552265E6C59009DC027 /* Set Build Number */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Set Build Number";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "## Generate the build number using current date and time\nbuildNumber=$(date \"+%Y%m.%d.%H%M\")\n## Set the build number in plist file\n/usr/libexec/PlistBuddy -c \"Set :CFBundleVersion $buildNumber\" \"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}\"\n";
		};
		2D02E4CB1E0B4B27006451C7 /* Bundle React Native Code And Images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native Code And Images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh";
		};
		3A4F872F27CCCE08127EDB06 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-maxicosi-maxicosiTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		64BDBE113165786D73B3710F /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-maxicosi-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		7A0320C0808124F2482DE24D /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-maxicosi-maxicosiTests/Pods-maxicosi-maxicosiTests-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/BugsnagReactNative/Bugsnag.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage_resources.bundle",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Entypo.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Feather.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Fontisto.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Foundation.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Octicons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Zocial.ttf",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/AccessibilityResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker/RNImagePickerPrivacyInfo.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Bugsnag.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCoreInternal_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseInstallations_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleDataTransport_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleUtilities_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FBLPromises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNCAsyncStorage_resources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AntDesign.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Entypo.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EvilIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Feather.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Brands.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Regular.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Solid.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Fontisto.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Foundation.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MaterialCommunityIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MaterialIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Octicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/SimpleLineIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Zocial.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AccessibilityResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNImagePickerPrivacyInfo.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-maxicosi-maxicosiTests/Pods-maxicosi-maxicosiTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		88A371C38E148E8EB05D9D14 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-maxicosi/Pods-maxicosi-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/BugsnagReactNative/Bugsnag.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage_resources.bundle",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Entypo.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Feather.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Fontisto.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Foundation.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Octicons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Zocial.ttf",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/AccessibilityResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker/RNImagePickerPrivacyInfo.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Bugsnag.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCoreInternal_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseInstallations_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleDataTransport_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleUtilities_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FBLPromises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNCAsyncStorage_resources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AntDesign.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Entypo.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EvilIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Feather.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Brands.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Regular.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Solid.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Fontisto.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Foundation.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MaterialCommunityIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MaterialIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Octicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/SimpleLineIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Zocial.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AccessibilityResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNImagePickerPrivacyInfo.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-maxicosi/Pods-maxicosi-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		958BFF1055979B20783CFDE6 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-maxicosi-tvOS-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F122414F3F0027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* maxicosiTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				DDCB79602AC5204700A985F2 /* AppDelegate.swift in Sources */,
				DD5FD81E2AB1958500E9F038 /* CarScene.swift in Sources */,
				DDA74CAE2AB173000076754B /* PhoneScene.swift in Sources */,
				BF7845CD2CECA9AF00DA5F25 /* MaxRCTCarPlayNotificationManager.m in Sources */,
				5BC695EB2E4E1BDC00714710 /* CarPlayEventEmitter.m in Sources */,
				5BC695EC2E4E1BDC00714710 /* CarPlayEventEmitter.swift in Sources */,
				BF7845CB2CECA9A000DA5F25 /* MaxRCTCarPlayNotificationManager.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4771E0B4A5D006451C7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48C1E0B4A5D006451C7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				2DCD954D1E0B4F2C00145EB5 /* maxicosiTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* maxicosi */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
		2D02E4921E0B4A5D006451C7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2D02E47A1E0B4A5D006451C7 /* maxicosi-tvOS */;
			targetProxy = 2D02E4911E0B4A5D006451C7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6FFFAA77735A8FD7C6CAE708 /* Pods-maxicosi-maxicosiTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = maxicosiTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/maxicosi.app/maxicosi";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E2EF5130227179DC51FE51F3 /* Pods-maxicosi-maxicosiTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = maxicosiTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/maxicosi.app/maxicosi";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 301D08C321A11A3ECF48B199 /* Pods-maxicosi.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Entitlements.plist;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8S9R52T2SS;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/BVLinearGradient\"",
					"\"${PODS_ROOT}/Headers/Public/Base64\"",
					"\"${PODS_ROOT}/Headers/Public/BugsnagReactNative\"",
					"\"${PODS_ROOT}/Headers/Public/CodePush\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCoreExtension\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleDataTransport\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/JWT\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Camera\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-PhotoLibrary\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-PhotoLibraryAddOnly\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNBluetoothStateManager\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCClipboard\"",
					"\"${PODS_ROOT}/Headers/Public/RNCMaskedView\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPushNotificationIOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNDateTimePicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNInAppBrowser\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNShare\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Codegen\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeGetLocation\"",
					"\"${PODS_ROOT}/Headers/Public/SSZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/Toast\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-android-auto\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-appearance\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-background-timer\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-ble-manager\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blur\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-camera\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-carplay\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-config\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-document-picker\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-picker\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-restart\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-splash-screen\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-video\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_ROOT}/Headers/Public/rn-fetch-blob\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(SRCROOT)/../node_modules/react-native/React/**",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\"",
					"$(SRCROOT)/../node_modules/react-native/React-RCTImage/**",
					"$(SRCROOT)/../node_modules/react-native/React-RCTText/**",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"/**",
					"$(SRCROOT)/../node_modules/react-native/React-Core",
				);
				INFOPLIST_FILE = maxicosi/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 0.0.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.cbtsmartcar;
				PRODUCT_NAME = maxicosi;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG FB_SONARKIT_ENABLED";
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/maxicosi-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 28BC3D9C1218BD353CA69BC6 /* Pods-maxicosi.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Entitlements.plist;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8S9R52T2SS;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/BVLinearGradient\"",
					"\"${PODS_ROOT}/Headers/Public/Base64\"",
					"\"${PODS_ROOT}/Headers/Public/BugsnagReactNative\"",
					"\"${PODS_ROOT}/Headers/Public/CodePush\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCoreExtension\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleDataTransport\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/JWT\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Camera\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-PhotoLibrary\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-PhotoLibraryAddOnly\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNBluetoothStateManager\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCClipboard\"",
					"\"${PODS_ROOT}/Headers/Public/RNCMaskedView\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPushNotificationIOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNDateTimePicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNInAppBrowser\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNShare\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Codegen\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeGetLocation\"",
					"\"${PODS_ROOT}/Headers/Public/SSZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/Toast\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-android-auto\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-appearance\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-background-timer\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-ble-manager\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blur\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-camera\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-carplay\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-config\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-document-picker\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-picker\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-restart\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-splash-screen\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-video\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_ROOT}/Headers/Public/rn-fetch-blob\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(SRCROOT)/../node_modules/react-native/React/**",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\"",
					"$(SRCROOT)/../node_modules/react-native/React-RCTImage/**",
					"$(SRCROOT)/../node_modules/react-native/React-RCTText/**",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"/**",
					"$(SRCROOT)/../node_modules/react-native/React-Core",
				);
				INFOPLIST_FILE = maxicosi/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 0.0.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.cbtsmartcar;
				PRODUCT_NAME = maxicosi;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "";
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/maxicosi-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		2D02E4971E0B4A5E006451C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E68861AF0C6C716739053ACA /* Pods-maxicosi-tvOS.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "maxicosi-tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.maxicosi-tvOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		2D02E4981E0B4A5E006451C7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AD6A27FEF739A762611AD916 /* Pods-maxicosi-tvOS.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "maxicosi-tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.maxicosi-tvOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
		2D02E4991E0B4A5E006451C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B05B5532D0045320B94EC0EC /* Pods-maxicosi-tvOSTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "maxicosi-tvOSTests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.maxicosi-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/maxicosi-tvOS.app/maxicosi-tvOS";
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Debug;
		};
		2D02E49A1E0B4A5E006451C7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8A0ECC90F20C3E9858D4810C /* Pods-maxicosi-tvOSTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "maxicosi-tvOSTests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.maxicosi-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/maxicosi-tvOS.app/maxicosi-tvOS";
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "/usr/lib/swift $(inherited)";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "";
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "/usr/lib/swift $(inherited)";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "maxicosiTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "maxicosi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D02E4BA1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "maxicosi-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D02E4971E0B4A5E006451C7 /* Debug */,
				2D02E4981E0B4A5E006451C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D02E4BB1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "maxicosi-tvOSTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D02E4991E0B4A5E006451C7 /* Debug */,
				2D02E49A1E0B4A5E006451C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "maxicosi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
