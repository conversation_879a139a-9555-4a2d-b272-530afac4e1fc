import React
import UserNotifications

@objc(MaxRCTCarPlayNotificationManager)
class MaxRCTCarPlayNotificationManager: NSObject, RCTBridgeModule {
  static func moduleName() -> String! {
      return "MaxRCTCarPlayNotificationManager"
  }
  
  override init() {
        super.init()
        configureCarPlayNotifications()
    }
  
  private func configureCarPlayNotifications() {
      // Define actions
      let acceptAction = UNNotificationAction(
          identifier: "ACCEPT_ACTION",
          title: "Accept",
          options: [.foreground]
      )
      let declineAction = UNNotificationAction(
          identifier: "DECLINE_ACTION",
          title: "Decline",
          options: [.destructive]
      )
      // Define category
     let carPlayCategory = UNNotificationCategory(
        identifier: "CARPLAY_CATEGORY_CBT",
        actions: [acceptAction, declineAction],
        intentIdentifiers: [],
        options: [.allowInCarPlay] // ✅ Ensures CarPlay eligibility
    )

      UNUserNotificationCenter.current().setNotificationCategories([carPlayCategory])
  }
  
    
    @objc func sendCarPlayNotification(_ title: String, body: String,resolver: @escaping RCTPromiseResolveBlock,
    rejecter: @escaping RCTPromiseRejectBlock) {
        print("Sending CarPlay notification")
        // Set up the notification content
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.categoryIdentifier = "CARPLAY_CATEGORY_CBT"
        
        // Detect CarPlay foreground/background
        if let scene = UIApplication.shared.connectedScenes.first(where: { $0.session.role == .carTemplateApplication }),
          scene.activationState == .foregroundActive {
            print("✅ CarPlay is FOREGROUND — Use templates (React side should show Alert/ActionSheet)")
        } else {
            print("ℹ️ CarPlay is BACKGROUND — Showing iPhone local notification banner")
        }

        // Define a unique identifier
        let request = UNNotificationRequest(identifier: UUID().uuidString, content: content, trigger: nil)
        
        // Schedule the notification
        UNUserNotificationCenter.current().add(request) { error in
        if let error = error {
            print("Error adding CarPlay notification: \(error)")
            rejecter("CARPLAY_ERROR", "Failed to send notification", error)
        } else {
            print("CarPlay notification sent!")
            resolver("success") // ✅ send back result
        }
    }
    }
  

   
  @objc func logMethod() {
    print("logMethod was called from React Native!")
  }
   
}
