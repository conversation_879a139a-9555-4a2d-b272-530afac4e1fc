import React
import UserNotifications

@objc(MaxRCTCarPlayNotificationManager)
class MaxRCTCarPlayNotificationManager: NSObject, RCTBridgeModule {
  static func moduleName() -> String! {
      return "MaxRCTCarPlayNotificationManager"
  }
  
  override init() {
        super.init()
        configureCarPlayNotifications()
    }
  
  private func configureCarPlayNotifications() {
      // Define actions
      let acceptAction = UNNotificationAction(
          identifier: "ACCEPT_ACTION",
          title: "Accept",
          options: [.foreground]
      )
      let declineAction = UNNotificationAction(
          identifier: "DECLINE_ACTION",
          title: "Decline",
          options: [.destructive]
      )
      // Define category
      let carPlayCategory = UNNotificationCategory(
          identifier: "CARPLAY_CATEGORY_CBT",
          actions: [acceptAction, declineAction],
          intentIdentifiers: [],
          options: []
      )
      UNUserNotificationCenter.current().setNotificationCategories([carPlayCategory])
  }
  
    
    @objc func sendCarPlayNotification(_ title: String, body: String) {
        print("Sending CarPlay notification")
        // Set up the notification content
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.categoryIdentifier = "CARPLAY_CATEGORY_CBT"
        
    
        // Define a unique identifier
        let request = UNNotificationRequest(identifier: UUID().uuidString, content: content, trigger: nil)
        
        // Schedule the notification
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error adding CarPlay notification: \(error)")
            } else {
                print("CarPlay notification sent!")
            }
        }
    }
  

   
  @objc func logMethod() {
    print("logMethod was called from React Native!")
  }
   
}
