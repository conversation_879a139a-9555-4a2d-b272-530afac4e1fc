
require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'


platform :ios, '13.0'

target 'maxicosi' do
  config = use_native_modules!

  use_react_native!(:path => config["reactNativePath"])

  permissions_path = '../node_modules/react-native-permissions/ios'
  pod 'Permission-Camera', :path => "#{permissions_path}/Camera"
  pod 'Permission-PhotoLibrary', :path => "#{permissions_path}/PhotoLibrary"
  pod 'Permission-PhotoLibraryAddOnly', :path => "#{permissions_path}/PhotoLibraryAddOnly"
  pod 'react-native-background-timer', :path => '../node_modules/react-native-background-timer'

  pod 'CodePush', :path => '../node_modules/react-native-code-push'
  pod "Firebase", :modular_headers => true
  pod "Firebase/Messaging", :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod "FirebaseCoreInternal", :modular_headers => true
  pod "GoogleUtilities", :modular_headers => true


  pod 'RNNotifee', :path => '../node_modules/@notifee/react-native'

  target 'maxicosiTests' do
    inherit! :complete
    # Pods for testing
  end
 post_install do |installer|
    react_native_post_install(installer)
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
    installer.pods_project.build_configurations.each do |config|
      xcode_version = %x{xcrun xcodebuild -version | grep Xcode | cut -d' ' -f2}
      if "15.0" <= xcode_version
        puts "Adding _LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION for xcode 15+, current version #{xcode_version} for #{config.name}"
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION'
      end
    end
    # NOTE: Change IPHONEOS_DEPLOYMENT_TARGET to 13.0 ?
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
        config.build_settings["DEBUG_INFORMATION_FORMAT"] = "dwarf-with-dsym"
        config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"

        # "DT_TOOLCHAIN_DIR replace to TOOLCHAIN_DIR for xcode 15"
        xcconfig_path = config.base_configuration_reference.real_path
        xcconfig = File.read(xcconfig_path)
        xcconfig_mod = xcconfig.gsub(/DT_TOOLCHAIN_DIR/, "TOOLCHAIN_DIR")
        File.open(xcconfig_path, "w") { |file| file << xcconfig_mod }
      end
    end
    installer.pods_project.targets.each do |target|
      # Make it build with XCode 14
      if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.bundle"
        target.build_configurations.each do |config|
            config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
        end
      end
      # Make it work with GoogleDataTransport
      if target.name.start_with? "GoogleDataTransport"
        target.build_configurations.each do |config|
          config.build_settings['CLANG_WARN_STRICT_PROTOTYPES'] = 'NO' 
        end
      end
    end
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable these next few lines.
  # use_flipper!({ 'Flipper' => '0.74.0' })
  #   post_install do |installer|
  #   flipper_post_install(installer)
  # end
end


target 'maxicosi-tvOS' do
  # Pods for maxicosi-tvOS

  target 'maxicosi-tvOSTests' do
    inherit! :search_paths
    # Pods for testing
  end
end
