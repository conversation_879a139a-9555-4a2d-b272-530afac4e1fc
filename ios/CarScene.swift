// ios/CarScene.swift
import Foundation
import CarPlay
import React
class CarSceneDelegate: UIResponder, CPTemplateApplicationSceneDelegate {
  
//  var interfacController: CPInterfaceController?
  func templateApplicationScene(_ templateApplicationScene: CPTemplateApplicationScene,
                                  didConnect interfaceController: CPInterfaceController) {
    RNCarPlay.connect(with: interfaceController, window: templateApplicationScene.carWindow);
//    self.interfacController = interfaceController;
//    if #available(iOS 14.0, *) {
//      let nowPlay = CPContactTemplate(contact: CPContact(name: "Child name", image: UII<PERSON>(named: "warning")!))
//      self.interfacController?.setRootTemplate(nowPlay, animated: true)
//    } else {
//      // Fallback on earlier versions
//    }

  
    
    
  }
  func templateApplicationScene(_ templateApplicationScene: CPTemplateApplicationScene, didDisconnectInterfaceController interfaceController: CPInterfaceController) {
    RNCarPlay.disconnect()
  }

  // MARK: Scene Foreground/Background
    func sceneWillEnterForeground(_ scene: UIScene) {
        sendCarPlayEvent(name: "carPlayForeground")
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        sendCarPlayEvent(name: "carPlayBackground")
    }

   private func sendCarPlayEvent(name: String) {
    if let bridge = (UIApplication.shared.delegate as? AppDelegate)?.bridge,
       let emitter = bridge.module(for: CarPlayEventEmitter.self) as? CarPlayEventEmitter {
        emitter.sendEvent(withName: name, body: nil as Any?)
    }
  }

}
