{"name": "maxicos<PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios --udid 00008130-000E744C3883401C", "start": "react-native start", "test": "jest", "lint": "eslint .", "ios:packBundle": "react-native bundle --entry-file index.js --platform ios --dev false --bundle-output ios/main.jsbundle", "release": "cd ./android && ./gradlew clean && ./gradlew assemblerelease", "debug": "cd ./android && ./gradlew clean && ./gradlew assembledebug", "appdebug": "ENV=maxicosi && cd android && ENVFILE=.maxicosi.env ./gradlew assembleDebug", "apprelease": "ENV=maxicosi && cd android && ENVFILE=.maxicosi.env ./gradlew assembleRelease", "linkNotifee": "npx react-native link @notifee/react-native"}, "dependencies": {"@bugsnag/react-native": "^7.10.5", "@notifee/react-native": "^9.1.2", "@react-native-async-storage/async-storage": "^1.14.1", "@react-native-community/blur": "^4.3.2", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/datetimepicker": "^3.4.1", "@react-native-community/masked-view": "^0.1.10", "@react-native-community/netinfo": "^6.0.0", "@react-native-community/push-notification-ios": "^1.8.0", "@react-native-firebase/app": "^18.1.0", "@react-native-firebase/messaging": "^18.1.0", "@react-native-picker/picker": "^1.9.11", "@react-navigation/bottom-tabs": "^5.11.8", "@react-navigation/drawer": "^5.12.4", "@react-navigation/native": "^5.9.3", "@react-navigation/stack": "^5.14.3", "@twotalltotems/react-native-otp-input": "^1.3.11", "convert-string": "^0.1.0", "i18n-js": "^3.8.0", "lodash": "^4.17.21", "metro-config": "^0.76.7", "moment": "^2.29.4", "react": "16.13.1", "react-native": "0.68.5", "react-native-action-button": "^2.8.5", "react-native-android-auto": "^1.0.0", "react-native-animatable": "^1.3.3", "react-native-app-intro-slider": "^4.0.4", "react-native-appearance": "^0.3.4", "react-native-background-timer": "^2.4.1", "react-native-ble-manager": "^7.5.0", "react-native-ble-plx": "^3.1.2", "react-native-bluetooth-state-manager": "^1.3.1", "react-native-camera": "3.40.0", "react-native-card-stack-swiper": "^1.2.5", "react-native-carplay": "^2.3.0", "react-native-code-push": "^7.0.1", "react-native-config": "^1.4.2", "react-native-country-picker-modal": "^2.0.0", "react-native-datepicker": "^1.7.2", "react-native-deck-swiper": "^2.0.5", "react-native-device-info": "^8.1.3", "react-native-document-picker": "^5.0.3", "react-native-event-listeners": "^1.0.7", "react-native-gesture-handler": "^1.10.3", "react-native-get-location": "^2.1.0", "react-native-image-picker": "^7.1.2", "react-native-image-slider-box": "^1.0.12", "react-native-in-app-notification": "^3.1.0", "react-native-inappbrowser-reborn": "^3.5.1", "react-native-iphone-x-helper": "^1.3.1", "react-native-linear-gradient": "^2.5.6", "react-native-permissions": "3.6.1", "react-native-progress": "^4.1.2", "react-native-push-notification": "^7.3.1", "react-native-qrcode-scanner": "^1.5.3", "react-native-reanimated": "^2.5.0", "react-native-render-html": "^5.1.0", "react-native-restart": "^0.0.22", "react-native-safe-area-context": "^3.1.9", "react-native-screens": "^2.18.0", "react-native-share": "^7.0.0", "react-native-simple-toast": "^1.1.3", "react-native-splash-screen": "^3.2.0", "react-native-svg": "^12.3.0", "react-native-switch": "^2.0.0", "react-native-vector-icons": "^8.1.0", "react-native-video": "^5.2.1", "react-native-webview": "^11.4.0", "react-redux": "^7.2.2", "redux": "^4.0.5", "redux-persist": "^6.0.0", "redux-thunk": "^2.3.0", "rn-fetch-blob": "^0.12.0", "sails.io.js": "^1.2.1", "socket.io-client": "^2.0.3", "toggle-switch-react-native": "^3.2.0"}, "devDependencies": {"@babel/core": "^7.13.8", "@babel/runtime": "^7.13.8", "@react-native-community/eslint-config": "^2.0.0", "babel-jest": "^26.6.3", "eslint": "^7.21.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.20.5", "jest": "^26.6.3", "metro-react-native-babel-preset": "^0.65.1", "react-native-svg-transformer": "^0.14.3", "react-test-renderer": "16.13.1"}, "jest": {"preset": "react-native"}}