// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
       buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        kotlinVersion = "1.7.20"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.4.2")
        classpath 'com.google.gms:google-services:4.3.15'
        classpath "com.google.firebase:firebase-crashlytics-gradle:2.9.7"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        mavenLocal()
        maven {
            url("$rootDir/../node_modules/react-native/android")
        }
        google()
        mavenCentral()
        maven { url "https://maven.google.com" }
        maven { url "https://www.jitpack.io" }
        maven { url("$rootDir/../node_modules/hermes-engine/android") }
    }
    configurations.all {
       resolutionStrategy {
        force "com.facebook.react:react-native:0.68.5"
        force "androidx.work:work-runtime:2.8.1"
        force "androidx.annotation:annotation:1.6.0"
        force "androidx.core:core:1.9.0"
        force "androidx.browser:browser:1.5.0"
        force "org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"
        force "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlinVersion"
        force "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlinVersion"
        force "com.facebook.flipper:flipper:0.125.0"
        // Comment these out to avoid resolution failures:
        // force "com.facebook.flipper:flipper-databases-plugin:0.125.0"
        // force "com.facebook.flipper:flipper-crashreporter-plugin:0.125.0"
       }
    }
}


subprojects { subproject ->
    afterEvaluate {
        if (subproject.hasProperty("android")) {
            subproject.android {
                configurations.all {
                    resolutionStrategy {
                        force "com.facebook.react:react-native:0.68.5"
                    }
                }
            }
        }
    }
}