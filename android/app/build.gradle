import com.android.build.OutputFile
import groovy.json.JsonSlurper;

apply plugin: "com.android.application"
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

project.ext.react = [
    enableHermes: true,  // clean and rebuild if changing
]

apply from: "../../node_modules/react-native/react.gradle"

def enableSeparateBuildPerCPUArchitecture = false
def enableProguardInReleaseBuilds = false

def enableHermes = project.ext.react.get("enableHermes", false);
def jscFlavor = 'org.webkit:android-jsc:+'

    android {
        compileSdkVersion rootProject.ext.compileSdkVersion
        buildToolsVersion rootProject.ext.buildToolsVersion
        compileSdk rootProject.ext.compileSdkVersion
        namespace "com.cbtsmartcar" // Your package name
        defaultConfig {
            applicationId "com.cbtsmartcar"
            minSdkVersion rootProject.ext.minSdkVersion
            targetSdkVersion rootProject.ext.targetSdkVersion
            versionCode 1
            versionName "1.0"
            missingDimensionStrategy 'react-native-camera', 'general'
            multiDexEnabled true
            vectorDrawables.useSupportLibrary = true
            resValue "string", "build_config_package", "com.cbtsmartcar"
        }
        buildFeatures {
            buildConfig true
        }
        packagingOptions {
            // AGP 7 compatible syntax
            jniLibs {
                pickFirsts += [
                        '**/libc++_shared.so',
                        '**/libfbjni.so'
                ]
            }
            // Legacy fallback (harmless alongside jniLibs above)
            pickFirst 'lib/armeabi-v7a/libc++_shared.so'
            pickFirst 'lib/arm64-v8a/libc++_shared.so'
            pickFirst 'lib/x86/libc++_shared.so'
            pickFirst 'lib/x86_64/libc++_shared.so'
            pickFirst 'lib/armeabi-v7a/libfbjni.so'
            pickFirst 'lib/arm64-v8a/libfbjni.so'
            pickFirst 'lib/x86/libfbjni.so'
            pickFirst 'lib/x86_64/libfbjni.so'
        }
        
        // Signing config for release using values from android/gradle.properties
        signingConfigs {
            release {
                if (project.hasProperty('MYAPP_UPLOAD_STORE_FILE')) {
                    storeFile file(MYAPP_UPLOAD_STORE_FILE)
                    storePassword MYAPP_UPLOAD_STORE_PASSWORD
                    keyAlias MYAPP_UPLOAD_KEY_ALIAS
                    keyPassword MYAPP_UPLOAD_KEY_PASSWORD
                }
            }
        }

        buildTypes {
            release {
                signingConfig signingConfigs.release
                minifyEnabled enableProguardInReleaseBuilds
                shrinkResources enableProguardInReleaseBuilds
                proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            }
            debug {
                // keep default debug config
            }
        }
    }

   // Comment out while debug app. 
  configurations.all {
    exclude group: 'com.facebook.fbjni', module: 'fbjni-java-only'
  }
dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    if (enableHermes) {
        // RN 0.68 uses local hermes-engine AARs
        def hermesPath = "../../node_modules/hermes-engine/android/"
        debugImplementation files(hermesPath + "hermes-debug.aar")
        releaseImplementation files(hermesPath + "hermes-release.aar")
    } else {
        implementation jscFlavor
    }

    // Google Car App library (aligns with DHU host interfaces)
    // Use the same artifact family as the library module (see react-native-android-auto/android/build.gradle)
    implementation "androidx.car.app:app:1.2.0"
    implementation "androidx.car.app:app-projected:1.2.0"
    // Project/module deps
    implementation project(':react-native-vector-icons')
    implementation project(':react-native-linear-gradient')
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation project(':react-native-svg')
    implementation project(':react-native-inappbrowser-reborn')
    implementation project(':react-native-push-notification')
    implementation "androidx.lifecycle:lifecycle-runtime:2.6.2"
    implementation "androidx.lifecycle:lifecycle-common:2.6.2"
    implementation 'org.jetbrains:annotations:16.0.2'
    implementation project(':react-native-android-auto')

    // Flipper (debug only)
    debugImplementation("com.facebook.flipper:flipper:0.125.0") {
        exclude group: 'com.facebook.fbjni'
    }
    debugImplementation("com.facebook.flipper:flipper-network-plugin:0.125.0")
    debugImplementation("com.facebook.flipper:flipper-fresco-plugin:0.125.0")
}

tasks.whenTaskAdded { task ->
    if (task.name == "checkDebugAarMetadata") {
        // task.dependsOn(":@notifee_react-native:writeDebugAarMetadata")
        task.dependsOn(":@react-native-firebase_messaging:writeDebugAarMetadata")
        task.dependsOn(":@react-native-picker_picker:writeDebugAarMetadata")
    }
    tasks.matching { it.name.contains("packageDebugResources") }.all {
      it.dependsOn(tasks.findByName("generateDebugResValues"))
  }
}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"