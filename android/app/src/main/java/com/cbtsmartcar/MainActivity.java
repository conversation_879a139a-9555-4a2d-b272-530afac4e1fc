package com.cbtsmartcar;

import android.os.Bundle;
import android.content.Intent; // <--- darkmode 
import android.content.res.Configuration; // <-- darkmode

import com.facebook.react.ReactActivity;
import android.location.LocationManager;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.ContentResolver;
import android.media.AudioAttributes;
import android.net.Uri;
import androidx.core.app.NotificationCompat;
import android.os.Build;
import android.widget.Toast;


import android.util.Log;
import android.app.AlertDialog;
import android.content.DialogInterface;
import org.devio.rn.splashscreen.SplashScreen; // here

public class MainActivity extends ReactActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel notificationChannel = new NotificationChannel("chillBaby_channel_id", "chillBaby", NotificationManager.IMPORTANCE_HIGH);
            notificationChannel.setShowBadge(true);
            notificationChannel.setDescription("");
            AudioAttributes att = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                    .build();
            notificationChannel.setSound(Uri.parse(ContentResolver.SCHEME_ANDROID_RESOURCE + "://" + getPackageName() + "/raw/siren"), att);
            notificationChannel.enableVibration(true);
            notificationChannel.setVibrationPattern(new long[]{400, 1000, 400});
            notificationChannel.setLockscreenVisibility(NotificationCompat.VISIBILITY_PUBLIC);
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(notificationChannel);

            NotificationChannel notificationChannel2 = new NotificationChannel("chillBaby_channel_id_default", "chillBaby", NotificationManager.IMPORTANCE_HIGH);
            notificationChannel2.setShowBadge(true);
            notificationChannel2.setDescription("");
            AudioAttributes att2 = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                    .build();
            // notificationChannel.setSound(Uri.parse(ContentResolver.SCHEME_ANDROID_RESOURCE + "://" + getPackageName() + "/raw/siren"), att2);
            notificationChannel2.enableVibration(true);
            notificationChannel2.setVibrationPattern(new long[]{400, 1000, 400});
            notificationChannel2.setLockscreenVisibility(NotificationCompat.VISIBILITY_PUBLIC);
            // NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(notificationChannel2);
        }
        SplashScreen.show(this);
    }
    
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
      super.onConfigurationChanged(newConfig);
      Intent intent = new Intent("onConfigurationChanged");
      intent.putExtra("newConfig", newConfig);
      sendBroadcast(intent);
    }

    /**
     * Returns the name of the main component registered from JavaScript. This is used to schedule
     * rendering of the component.
     */
    @Override
    protected String getMainComponentName() {
        return "cbtsmartcar";
    }
}
