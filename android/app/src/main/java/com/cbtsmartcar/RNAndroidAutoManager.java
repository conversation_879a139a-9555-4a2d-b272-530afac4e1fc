package com.cbtsmartcar;

import android.util.Log;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;

public class RNAndroidAutoManager extends ReactContextBaseJavaModule {

    private static final String TAG = "RNAndroidAutoManager";
    private final ReactApplicationContext reactContext;
    private final BLEDataStore store;

    public RNAndroidAutoManager(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
        this.store = BLEDataStore.getInstance();
    }

    @Override
    public String getName() {
        return "RNAndroidAutoManager";
    }

    // Update BLE + alert data
    @ReactMethod
    public void updateBLEData(
            int s6, int s7, int s8, int s10, String childName,
            boolean alertActive, String alertTitle, String alertMessage
    ) {
        try {
            BLEDataStore store = BLEDataStore.getInstance();
            if (store != null) {
                 getReactApplicationContext().runOnUiQueueThread(() -> {
                    store.setData(s6, s7, s8, s10, childName, alertActive, alertTitle, alertMessage);
                    Log.d(TAG, "BLEData updated: alertActive=" + alertActive);
                });
            } else {
                Log.e(TAG, "BLEDataStore instance is null!");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating BLEData: ", e);
        }
    }
}
