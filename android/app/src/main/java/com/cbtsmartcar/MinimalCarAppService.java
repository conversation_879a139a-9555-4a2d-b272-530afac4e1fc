package com.cbtsmartcar;

import android.content.Intent;
import androidx.car.app.CarAppService;
import androidx.car.app.Session;
import androidx.car.app.validation.HostValidator;
import androidx.car.app.model.Template;
import androidx.car.app.model.MessageTemplate;
import androidx.car.app.Screen;

public class MinimalCarAppService extends CarAppService {

    @Override
    public HostValidator createHostValidator() {
        return HostValidator.ALLOW_ALL_HOSTS_VALIDATOR;
    }

    @Override
    public Session onCreateSession() {
        return new MinimalSession();
    }

    private static class MinimalSession extends Session {
        @Override
        public Screen onCreateScreen(Intent intent) {
            try {
                return new MinimalScreen(getCarContext());
            } catch (Exception e) {
                return new ErrorScreen(getCarContext());
            }
        }
    }

    private static class MinimalScreen extends Screen {
        MinimalScreen(androidx.car.app.CarContext carContext) {
            super(carContext);
        }

        @Override
        public Template onGetTemplate() {
            try {
                return new MessageTemplate.Builder("CarPlay App - Working!")
                        .setTitle("Success")
                        .build();
            } catch (Exception e) {
                return new MessageTemplate.Builder("CarPlay App")
                        .build();
            }
        }
    }

    private static class ErrorScreen extends Screen {
        ErrorScreen(androidx.car.app.CarContext carContext) {
            super(carContext);
        }

        @Override
        public Template onGetTemplate() {
            return new MessageTemplate.Builder("CarPlay App - Error Screen")
                    .setTitle("Error")
                    .build();
        }
    }
}
