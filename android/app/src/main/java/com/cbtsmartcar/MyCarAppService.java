package com.cbtsmartcar;

import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.os.Build;
import android.util.Log;

import androidx.car.app.CarAppService;
import androidx.car.app.CarContext;
import androidx.car.app.Screen;
import androidx.car.app.Session;
import androidx.car.app.validation.HostValidator;
import androidx.car.app.model.ListTemplate;
import androidx.car.app.model.ItemList;
import androidx.car.app.model.Row;
import androidx.car.app.model.Template;
import androidx.car.app.model.MessageTemplate;
import androidx.car.app.model.Action;
import androidx.car.app.model.CarColor;
import androidx.annotation.NonNull;
import androidx.car.app.notification.CarAppExtender;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

public class MyCarAppService extends CarAppService {

    private static final String TAG = "MyCarAppService";
    private static final String CHANNEL_ID = "android_auto_channel";

    @NonNull
    @Override
    public HostValidator createHostValidator() {
        return HostValidator.ALLOW_ALL_HOSTS_VALIDATOR;
    }

    @NonNull
    @Override
    public Session onCreateSession() {
        return new CarPlaySession();
    }

    private static class CarPlaySession extends Session {
        @NonNull
        @Override
        public Screen onCreateScreen(Intent intent) {
            return new CarPlayScreen(getCarContext());
        }
    }

    private static class CarPlayScreen extends Screen implements BLEDataStore.BLEDataChangeListener {

        private final BLEDataStore store;
        private final Handler handler = new Handler(Looper.getMainLooper());
       private final Runnable resetActive;

        CarPlayScreen(CarContext carContext) {
            super(carContext);
            store = BLEDataStore.getInstance();
            if (store != null) store.addListener(this);
            resetActive = () -> {
                if (store != null) store.setActive(false);
            };
        }

        @NonNull
        @Override
        public Template onGetTemplate() {
            if (store != null) {
                // App is visible
                store.setActive(true);

                // Reset previous timeout and start new
                handler.removeCallbacks(resetActive);
                handler.postDelayed(resetActive, 2000);
            }

            if (store == null) {
                ItemList.Builder errorList = new ItemList.Builder();
                errorList.addItem(new Row.Builder()
                        .setTitle("Error")
                        .addText("BLE Data not available")
                        .build());
                return new ListTemplate.Builder()
                        .setSingleList(errorList.build())
                        .setTitle("Error")
                        .build();
            }

            // Alert modal
            if (store.isAlertActive()) {
                return new MessageTemplate.Builder(store.getAlertMessage())
                        .setTitle(store.getAlertTitle())
                        .addAction(
                                new Action.Builder()
                                        .setTitle("OK")
                                        .setOnClickListener(() -> {
                                            store.dismissAlert();
                                            invalidate();
                                        })
                                        .setBackgroundColor(CarColor.createCustom(0xFF63D8FE, 0xFF63D8FE))
                                        .build()
                        )
                        .setHeaderAction(Action.BACK)
                        .build();
            }

            // Normal BLE data list
            ItemList.Builder itemListBuilder = new ItemList.Builder();
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("Welcome")
                    .addText(store.getChildName() != null ? store.getChildName() : "Guest")
                    .build());
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("Standing Leg")
                    .addText(store.getS6() == 1 ? "✅ Standing leg" : "❌ Standing leg")
                    .build());
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("ISO Fix")
                    .addText(store.getS7() == 0 && store.getS8() == 0 ? "✅ ISO fix" : "❌ ISO fix")
                    .build());
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("Harness Buckled")
                    .addText("✅ Harness buckled")
                    .build());
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("Orientation")
                    .addText(store.getS10() == 1 ? "✅ Orientation" : "❌ Orientation")
                    .build());

            return new ListTemplate.Builder()
                    .setSingleList(itemListBuilder.build())
                    .setTitle("SMART 360 IQ" + (store.getChildName() != null ? " - " + store.getChildName() : ""))
                    .build();
        }

        @Override
        public void onDataChanged() {
            invalidate();
        }

        @Override
        public void onAlertTriggered(String title, String message) {
            if (store != null && !store.isActive()) {
                // Pass CarContext explicitly
                sendCarOnlyNotification(getCarContext(), title, message);
            } else {
                Log.d(TAG, "Skipping car notification because screen is active");
            }
        }



        @Override
        public void onActiveStateChanged(boolean isActive) {
            // Optional logging or behavior when screen becomes active/inactive
        }

        /**
         * Sends a notification that is intended for the car display.
         * This code runs inside CarAppService context, so it will be routed to Android Auto.
         */
        private void sendCarOnlyNotification(CarContext carContext, String title, String message) {
            Log.d(TAG, "🚗 [sendCarOnlyNotification] Started...");
            Log.d(TAG, "🔹 Title: " + title);
            Log.d(TAG, "🔹 Message: " + message);

            if (carContext == null) {
                Log.w(TAG, "⚠️ CarContext is null — cannot send car-only notification.");
                return;
            }

            try {
                Log.d(TAG, "✅ CarContext is not null. Package: " + carContext.getPackageName());

                // STEP 1: Get NotificationManager
                NotificationManager manager =
                        (NotificationManager) carContext.getSystemService(android.content.Context.NOTIFICATION_SERVICE);

                if (manager == null) {
                    Log.e(TAG, "❌ NotificationManager is null — cannot send notification");
                    return;
                } else {
                    Log.d(TAG, "✅ NotificationManager retrieved successfully");
                }

                // STEP 2: Create notification channel (once, for API 26+)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    Log.d(TAG, "📦 Creating NotificationChannel (API " + Build.VERSION.SDK_INT + ")");
                    NotificationChannel channel = new NotificationChannel(
                            CHANNEL_ID,
                            "Android Auto Alerts",
                            NotificationManager.IMPORTANCE_HIGH // High importance for heads-up
                    );
                    channel.setDescription("Alerts shown only in Android Auto (not on phone)");
                    channel.setShowBadge(false);
                    channel.setSound(null, null); // If you want silent, otherwise set sound here
                    manager.createNotificationChannel(channel);
                    Log.d(TAG, "✅ NotificationChannel created successfully");
                } else {
                    Log.d(TAG, "ℹ️ Skipping channel creation (SDK < 26)");
                }

                // STEP 3: Resolve small icon (fallback included)
                int icon = getSmallIconResource(carContext);
                Log.d(TAG, "🧩 Small icon resource resolved: " + icon);
                if (icon == 0) {
                    icon = android.R.drawable.ic_dialog_alert; // Fallback
                    Log.w(TAG, "⚠️ Using fallback icon (ic_dialog_alert)");
                }

                // STEP 4: Prepare PendingIntent for tap action - open an Activity
                Intent intent = new Intent(carContext, CarPlayScreen.class); // Change to your activity class
                PendingIntent pendingIntent = PendingIntent.getActivity(
                        carContext,
                        0,
                        intent,
                        PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT
                );
                Log.d(TAG, "✅ PendingIntent created successfully");

                // STEP 5: Build notification with correct category for heads-up
                NotificationCompat.Builder builder = new NotificationCompat.Builder(carContext, CHANNEL_ID)
                        .setSmallIcon(icon)
                        .setContentTitle(title)
                        .setContentText(message)
                        .setPriority(NotificationCompat.PRIORITY_HIGH) // Priority for pre-26 devices
                        .setCategory(NotificationCompat.CATEGORY_MESSAGE) // Use MESSAGE or CALL or NAVIGATION depending on content
                        .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                        .setLocalOnly(true)
                        .extend(new CarAppExtender.Builder()
                                .setImportance(NotificationManager.IMPORTANCE_HIGH) // Ensure high importance for Android Auto
                                .build()
                        )
                        .setContentIntent(pendingIntent)
                        .setAutoCancel(true);

                Log.d(TAG, "✅ NotificationCompat.Builder created");

                // STEP 6: Show notification with unique ID
                int notificationId = (int) System.currentTimeMillis();
                manager.notify(notificationId, builder.build());
                Log.d(TAG, "🎉 Car-only notification sent successfully! (ID = " + notificationId + ")");

            } catch (Exception e) {
                Log.e(TAG, "💥 Exception sending car-only notification", e);
            } finally {
                Log.d(TAG, "🏁 [sendCarOnlyNotification] Completed.");
            }
        }

        private int getSmallIconResource(CarContext carContext) {
            return carContext.getApplicationContext().getApplicationInfo().icon;
        }
    }
}
