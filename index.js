/**
 * @format
 */
import React from "react";
import { AppRegistry } from "react-native";
import messaging from "@react-native-firebase/messaging";
import App from "./App";
import { name as appName } from "./app.json";

// messaging().setBackgroundMessageHandler(async (remoteMessage) => {
//   console.log("Message handled in the background!", remoteMessage);
// });

messaging().onNotificationOpenedApp((remoteMessage) => {
  console.log(
    "Notification caused app to open from background state:",
    remoteMessage
  );
  // navigation.navigate(remoteMessage.data.type);
});

function HeadlessCheck({ isHeadless }) {
  if (isHeadless) {
    // App has been launched in the background by iOS, ignore
    return null;
  }

  console.log("App ==> ", App);
  return <App />;
}

// AppRegistry.registerRunnable("androidAuto", () => {
//   render(React.createElement(AndroidAuto));
// });

AppRegistry.registerComponent(appName, () => HeadlessCheck);
