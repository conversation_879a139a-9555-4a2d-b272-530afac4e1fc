const fs = require("fs");
const path = require("path");

const libs = [
  "react-native-background-timer",
  "react-native-ble-manager",
  "react-native-bluetooth-state-manager",
  "react-native-camera",
  "react-native-code-push",
  "react-native-community-blur",
  "react-native-community-clipboard",
  "react-native-community-datetimepicker",
  "react-native-community-masked-view",
  "react-native-community-netinfo",
  "react-native-device-info",
  "react-native-document-picker",
  "react-native-gesture-handler",
  "react-native-get-location",
  "react-native-image-picker",
  "react-native-inappbrowser-reborn",
  "react-native-linear-gradient",
  "react-native-permissions",
  "react-native-push-notification",
  "react-native-restart",
  "react-native-safe-area-context",
  "react-native-screens",
  "react-native-share",
  "react-native-splash-screen",
  "react-native-svg",
  "react-native-switch",
  "react-native-vector-icons",
  "react-native-video",
  "react-native-webview",
  "rn-fetch-blob",
];

libs.forEach((lib) => {
  const buildGradlePath = path.join(
    "node_modules",
    lib,
    "android",
    "build.gradle"
  );
  if (fs.existsSync(buildGradlePath)) {
    let gradle = fs.readFileSync(buildGradlePath, "utf8");
    if (!gradle.includes("namespace")) {
      const manifestPath = path.join(
        "node_modules",
        lib,
        "android",
        "src",
        "main",
        "AndroidManifest.xml"
      );
      if (fs.existsSync(manifestPath)) {
        const manifest = fs.readFileSync(manifestPath, "utf8");
        const match = manifest.match(/package="([^"]+)"/);
        if (match) {
          const namespaceValue = match[1];
          gradle = gradle.replace(
            /android\s*{/,
            `android {\n    namespace "${namespaceValue}"`
          );
          fs.writeFileSync(buildGradlePath, gradle, "utf8");
          console.log(`[PATCHED] ${lib} → namespace "${namespaceValue}"`);
        }
      }
    }
  }
});
