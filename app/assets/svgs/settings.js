import * as React from "react";
import Svg, { Path } from "react-native-svg";

export const Settings = (props) => {
  const color = props.color ?? "#B5B5B5";
  return (
    <Svg
      width={26}
      height={26}
      viewBox='0 0 26 26'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <Path
        d='M16.7916 13C16.7916 15.0941 15.094 16.7916 12.9999 16.7916C10.9058 16.7916 9.20825 15.0941 9.20825 13C9.20825 10.9059 10.9058 9.20831 12.9999 9.20831C15.094 9.20831 16.7916 10.9059 16.7916 13Z'
        stroke={color}
        strokeWidth={1.5}
      />
      <Path
        d='M22.8423 15.2504C23.3613 15.1219 23.6208 15.0576 23.7271 14.9218C23.8333 14.7861 23.8333 14.5676 23.8333 14.1307V11.8693C23.8333 11.4324 23.8333 11.214 23.7271 11.0782C23.6208 10.9424 23.3613 10.8782 22.8423 10.7497C20.6551 10.208 19.285 7.91919 19.8939 5.76967C20.0423 5.24594 20.1164 4.98407 20.0509 4.82426C19.9854 4.66446 19.7938 4.5557 19.4107 4.33818L17.3973 3.19505C17.0218 2.98181 16.834 2.87518 16.6644 2.89978C16.4948 2.92437 16.3121 3.11407 15.9469 3.49345C14.3537 5.1485 11.648 5.14843 10.0547 3.49335C9.68949 3.11396 9.50688 2.92427 9.33728 2.89967C9.16768 2.87508 8.97988 2.9817 8.6043 3.19495L6.59092 4.33808C6.20784 4.55558 6.0163 4.66434 5.95076 4.82412C5.88521 4.98391 5.95937 5.2458 6.10768 5.76959C6.71632 7.91917 5.34513 10.2081 3.15761 10.7497C2.63862 10.8782 2.37912 10.9424 2.27287 11.0782C2.16663 11.2139 2.16663 11.4324 2.16663 11.8693V14.1307C2.16663 14.5676 2.16663 14.7861 2.27287 14.9219C2.37911 15.0576 2.63861 15.1219 3.15761 15.2504C5.3448 15.792 6.7149 18.0809 6.10601 20.2304C5.95766 20.7541 5.88348 21.016 5.94902 21.1758C6.01455 21.3356 6.20611 21.4444 6.58921 21.6619L8.60258 22.805C8.97819 23.0183 9.16599 23.1249 9.33561 23.1003C9.50523 23.0757 9.6878 22.886 10.0529 22.5066C11.647 20.8502 14.3546 20.8502 15.9487 22.5065C16.3138 22.8859 16.4964 23.0756 16.666 23.1002C16.8356 23.1248 17.0234 23.0182 17.3991 22.8049L19.4124 21.6618C19.7955 21.4442 19.9871 21.3355 20.0526 21.1757C20.1182 21.0158 20.044 20.754 19.8956 20.2303C19.2864 18.0808 20.6555 15.7921 22.8423 15.2504Z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
      />
    </Svg>
  );
};
