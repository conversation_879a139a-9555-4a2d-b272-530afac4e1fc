import * as React from "react";
import Svg, { Path } from "react-native-svg";

export const ChartIncrease = (props) => {
  const color = props.color ?? "#B5B5B5";
  return (
    <Svg
      width={24}
      height={24}
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <Path
        d='M20 21H9C5.70017 21 4.05025 21 3.02513 19.9749C2 18.9497 2 17.2998 2 14V3'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
      />
      <Path
        d='M16.0086 7C16.0086 6.58579 16.3444 6.25 16.7586 6.25C17.1728 6.25 17.5086 6.58579 17.5086 7L16.0086 7ZM6 17.75C5.58579 17.75 5.25 17.4142 5.25 17C5.25 16.5858 5.58579 16.25 6 16.25L6 17.75ZM15.0651 8.51223C14.7822 8.81479 14.3076 8.83073 14.005 8.54783C13.7025 8.26494 13.6865 7.79033 13.9694 7.48777L15.0651 8.51223ZM15.4907 6.95887L14.9429 6.44664L15.4907 6.95887ZM18.0265 6.95887L18.5744 6.44664L18.0265 6.95887ZM19.5478 7.48777C19.8307 7.79033 19.8148 8.26494 19.5122 8.54783C19.2097 8.83073 18.7351 8.81479 18.4522 8.51223L19.5478 7.48777ZM16.7586 6L16.7586 5.25L16.7586 6ZM17.5086 7C17.5086 10.5107 16.2248 13.2267 14.0961 15.0543C11.9829 16.8686 9.1058 17.75 6 17.75L6 16.25C8.83602 16.25 11.3383 15.4451 13.1189 13.9162C14.8841 12.4007 16.0086 10.1168 16.0086 7L17.5086 7ZM13.9694 7.48777L14.9429 6.44664L16.0385 7.4711L15.0651 8.51223L13.9694 7.48777ZM18.5744 6.44664L19.5478 7.48777L18.4522 8.51223L17.4787 7.4711L18.5744 6.44664ZM14.9429 6.44664C15.2269 6.14283 15.491 5.85804 15.7354 5.65863C15.9947 5.44704 16.3271 5.25 16.7586 5.25L16.7586 6.75C16.8188 6.75 16.8161 6.71277 16.6837 6.82081C16.5364 6.94102 16.3522 7.13566 16.0385 7.4711L14.9429 6.44664ZM17.4787 7.4711C17.1651 7.13567 16.9808 6.94102 16.8335 6.82081C16.7011 6.71277 16.6984 6.75 16.7586 6.75L16.7586 5.25C17.1902 5.25 17.5226 5.44704 17.7819 5.65863C18.0263 5.85805 18.2903 6.14283 18.5744 6.44664L17.4787 7.4711Z'
        fill={color}
      />
    </Svg>
  );
};
