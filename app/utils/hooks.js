import { useCallback } from "react";
import { Platform } from "react-native";
import DeviceInfo from "react-native-device-info";
import {
  check,
  checkMultiple,
  PERMISSIONS,
  PermissionStatus,
  RESULTS,
  request,
} from "react-native-permissions";

export const usePermissions = () => {
  const requestCameraAccess = useCallback(async () => {
    let result = null;
    const permission = await checkMultiple([
      PERMISSIONS?.IOS?.CAMERA,
      PERMISSIONS?.ANDROID?.CAMERA,
    ]);
    if (
      permission[PERMISSIONS?.IOS?.CAMERA] !== RESULTS.GRANTED ||
      permission[PERMISSIONS?.ANDROID?.CAMERA] !== RESULTS.GRANTED
    ) {
      result = await request(
        Platform.select({
          android: PERMISSIONS?.ANDROID?.CAMERA,
          ios: PERMISSIONS?.IOS?.CAMERA,
        })
      );
    } else {
      result =
        Platform.select({
          android: permission[PERMISSIONS?.ANDROID?.CAMERA],
          ios: permission[PERMISSIONS?.IOS?.CAMERA],
        }) ?? null;
    }
    return result;
  }, []);

  const requestGalleryAccess = useCallback(async () => {
    let result = null;
    const apiLevel = await DeviceInfo.getApiLevel();
    const permission = await checkMultiple([
      PERMISSIONS.IOS.PHOTO_LIBRARY,
      PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
      PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
    ]);
    console.log("permission ", JSON.stringify(permission));
    const permission1 = await checkMultiple([PERMISSIONS.IOS.PHOTO_LIBRARY]);
    console.log("permission1 ", JSON.stringify(permission1));
    if (
      permission[PERMISSIONS.IOS.PHOTO_LIBRARY] !== RESULTS.GRANTED ||
      permission[PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE] !==
        RESULTS.GRANTED ||
      permission[PERMISSIONS.ANDROID.READ_MEDIA_IMAGES] !== RESULTS.GRANTED
    ) {
      result = await request(
        Platform.select({
          android:
            apiLevel && apiLevel >= 33
              ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
              : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
          ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
        })
      );
      console.log("result 1", result);
    } else {
      result =
        Platform.select({
          android:
            apiLevel && apiLevel >= 33
              ? permission[PERMISSIONS.ANDROID.READ_MEDIA_IMAGES]
              : permission[PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE],
          ios: permission[PERMISSIONS.IOS.PHOTO_LIBRARY],
        }) ?? null;

      console.log("result 2", result);
    }
    return result;
  }, []);

  return { requestCameraAccess, requestGalleryAccess };
};
