/* eslint-disable quotes */
import { LayoutAnimation, Linking, Platform, UIManager } from "react-native";
import InAppBrowser from "react-native-inappbrowser-reborn";
import BaseColor from "../config/colors";
import BaseSetting from "../config/setting";
import { store } from "../redux/store/configureStore";
import GetLocation from "react-native-get-location";
import { getApiData } from "./apiHelper";
import { isEmpty } from "lodash";
import AuthActions from "../redux/reducers/auth/actions";

export const enableAnimateInEaseOut = () => {
  if (Platform.OS === "android") {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
};

export const enableAnimateLinear = () => {
  if (Platform.OS === "android") {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
};

export const enableAnimateSpring = () => {
  if (Platform.OS === "android") {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.spring);
};

// this function for add campaign actions
export async function addAction(item, type, token) {
  const {
    auth: { accessToken },
  } = store.getState();

  const headers = {
    "Content-Type": "application/json",
    authorization: accessToken ? `Bearer ${accessToken}` : "",
  };
  try {
    const response = await getApiData(
      BaseSetting.endpoints.addAction,
      "POST",
      {
        campaign_id: item.id,
        type,
        platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
      },
      headers
    );
    console.log(
      "🚀 ~ file: index.js ~ line 198 ~ addAction ~ response",
      response
    );
  } catch (error) {
    console.log("add action error ===", error);
    sendErrorReport(error, "add_action");
  }
}

// this function for handle user log
export async function setUserLog(id, token) {
  const headers = {
    "Content-Type": "application/json",
    authorization: token ? `Bearer ${token}` : "",
  };

  try {
    const response = await getApiData(
      BaseSetting.endpoints.setUserLogActiveTime,
      "POST",
      { socket_id: id },
      headers
    );
  } catch (error) {
    console.log("user log error ===", error);
    sendErrorReport(error, "setUserLogActiveTime");
  }
}

export const openInAppBrowser = async (url) => {
  try {
    if (await InAppBrowser.isAvailable()) {
      const result = await InAppBrowser.open(url, {
        // iOS Properties
        dismissButtonStyle: "cancel",
        preferredBarTintColor: BaseColor.blueLight,
        preferredControlTintColor: "white",
        readerMode: false,
        animated: true,
        modalPresentationStyle: "fullScreen",
        modalTransitionStyle: "coverVertical",
        modalEnabled: true,
        enableBarCollapsing: false,
        // Android Properties
        showTitle: true,
        toolbarColor: BaseColor.blueLight,
        secondaryToolbarColor: "black",
        enableUrlBarHiding: true,
        enableDefaultShare: true,
        forceCloseOnRedirection: false,
        // Specify full animation resource identifier(package:anim/name)
        // or only resource name(in case of animation bundled with app).
        animations: {
          startEnter: "slide_in_right",
          startExit: "slide_out_left",
          endEnter: "slide_in_left",
          endExit: "slide_out_right",
        },
        headers: {
          "my-custom-header": "my custom header value",
        },
      });
      // Alert.alert(JSON.stringify(result));
    } else Linking.openURL(url);
  } catch (error) {
    sendErrorReport(error, "openInAppBrowser");
    console.log("onPress={ -> error", error);
    // Alert.alert(error.message);
  }
};

export const getLocationAndStore = () => {
  const myApiKey = "AIzaSyCFPe5S9WU6oDJtC6RgM1iVcZQKakGRJkA";
  const {
    auth: { userLocation },
  } = store.getState();

  if (!isEmpty(userLocation)) {
    return;
  }

  GetLocation.getCurrentPosition({
    enableHighAccuracy: true,
  })
    .then((location) => {
      fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${location.latitude},${location.longitude}&key=${myApiKey}`
      )
        .then((response) => response.json())
        .then((responseJson) => {
          store.dispatch(AuthActions.setUserLocation(responseJson));
        });
    })
    .catch((error) => {
      const { code, message } = error;
      console.warn(code, message);
    });
};

//! This Function send Errors Data to API
export const sendErrorReport = async (errorObj, type) => {
  const {
    auth: { userData, userLocation },
  } = store.getState();

  const headers = {
    "Content-Type": "application/json",
  };

  const data = {
    type,
    error: errorObj,
    location: userLocation,
    user_id: !isEmpty(userData) && userData?.id ? userData.id : 0,
    platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
    app: "cbtsmartcar",
  };

  // console.log("+++++++++++++ Sending Error REPORT USERDATA", userData);
  // console.log("+++++++++++++ Sending Error REPORT userLocation", userLocation);
  // console.log("+++++++++++++ Sending Error REPORT", data);

  try {
    let res = await getApiData(
      BaseSetting.endpoints.saveErrorLog,
      "POST",
      data,
      headers
    );
    // console.log("+++++++++++++ Sending Error === SUCCESS", res);
  } catch (err) {
    // console.log("+++++++++++++ Sending Error === FAILED", err);
  }
};

export const sensorColorfnc = (data = {}) => {
  const { s1, s2, s3, s4, s5, s6, s7, s8, s18 } = data;
  let facing = 1;
  if (s18 >= 40 && s18 <= 80) {
    facing = 1; // rear
  } else if (s18 >= 81 && s18 <= 105) {
    facing = 2; // front
  } else if (s18 >= 106 && s18 <= 150) {
    facing = 3; //booster
  }

  let sensors = {
    s1: BaseColor.green,
    s2: BaseColor.turquise,
    s3: BaseColor.turquise,
    s4: BaseColor.turquise,
    s5: BaseColor.turquise,
    s6: BaseColor.turquise,
    s7: BaseColor.turquise,
    s8: BaseColor.turquise,
  };
  if (facing === 1 || facing === 2) {
    if (!(s1 >= 2)) {
      sensors.s1 = BaseColor.alertRed;
    }
    if (!(s7 + s8 === 1)) {
      sensors.s7 = BaseColor.alertRed;
      sensors.s8 = BaseColor.alertRed;
    }
    if (!(s6 === 1)) {
      sensors.s6 = BaseColor.alertRed;
    }
    if (!(s2 && s3 && s4 && s5 === 0)) {
      sensors = {
        s2: BaseColor.alertRed,
        s3: BaseColor.alertRed,
        s4: BaseColor.alertRed,
        s5: BaseColor.alertRed,
      };
    }
  }
  if (facing === 3) {
    if (!(s1 >= 2)) {
      sensors.s1 = BaseColor.alertRed;
    }
    if (!(s7 + s8 === 0)) {
      sensors.s7 = BaseColor.alertRed;
      sensors.s8 = BaseColor.alertRed;
    }
    if (!(s6 === 0)) {
      sensors.s6 = BaseColor.alertRed;
    }
    if (!(s2 + s5 === 1 && (s4 === 1 || s5 === 1))) {
      sensors = {
        s2: BaseColor.alertRed,
        s5: BaseColor.alertRed,
        s4: BaseColor.alertRed,
        s5: BaseColor.alertRed,
      };
    }
  }
  return sensors;
};

// Testig Value Update in Simulator.
export function generateRandomSensors() {
  const sen2 = Math.floor(Math.random() * 2); // 0 or 1
  const sen3 = Math.floor(Math.random() * 2);
  const sen4 = Math.floor(Math.random() * 2);
  const sen5 = Math.floor(Math.random() * 2);
  const sen6 = Math.floor(Math.random() * 2);
  const sen7 = Math.floor(Math.random() * 2);
  const sen8 = Math.floor(Math.random() * 2);
  const sen9 = Math.floor(Math.random() * 2);

  const sen10 = (Math.floor(Math.random() * (400 - 250 + 1)) + 250) / 10.0; // 25.0–40.0
  const sen11 = (Math.floor(Math.random() * (800 - 400 + 1)) + 400) / 10.0; // 40.0–80.0
  const bv = (Math.floor(Math.random() * (430 - 360 + 1)) + 360) / 100.0; // 3.60–4.30

  const sen12 = Math.floor(Math.random() * (100 - 80 + 1)) + 80; // 80–100
  const sen13 = Math.floor(Math.random() * 10); // 0–9
  const sen14 = Math.floor(Math.random() * 5); // 0–4
  const sen18 = Math.floor(Math.random() * (50 - 30 + 1)) + 30; // 30–50

  const temp = sen10;
  const hum = sen11;

  return {
    s2: sen2,
    s3: sen3,
    s4: sen4,
    s5: sen5,
    s6: sen6,
    s7: sen7,
    s8: sen8,
    s9: sen9,
    s10: sen10,
    s11: sen11,
    s12: sen12,
    s13: sen13,
    s14: sen14,
    s18: sen18,
    TEMP: temp,
    HUMIDITY: hum,
    bv: bv,
  };
}
