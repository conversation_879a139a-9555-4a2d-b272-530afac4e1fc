const actions = {
  SET_FCM_TOKEN: "notification/SET_FCM_TOKEN",
  SET_UPDATE_NOTIFICATION: "notification/SET_UPDATE_NOTIFICATION",
  SET_ON_NOTIFICATION_OPEN: "notification/SET_ON_NOTIFICATION_OPEN",
  SET_UPDATE_NOTIFICATION_COUNT: "notification/SET_UPDATE_NOTIFICATION_COUNT",
  SET_NOTIFICATION_LIST: "notification/SET_NOTIFICATION_LIST",
  SET_NOTIFICATION_TYPE: "notification/SET_NOTIFICATION_TYPE",
  SET_BADGE_COUNT: "notification/SET_BADGE_COUNT",
  CLEAR_DATA: "home/CLEAR_DATA",

  setFcmToken: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_FCM_TOKEN,
        fcmToken: data,
      });
  },
  updateNotification: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_UPDATE_NOTIFICATION,
        notification: data,
      });
  },
  onNotificationOpen: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_ON_NOTIFICATION_OPEN,
        openedNotification: data,
      });
  },
  updateCountOfNotifications: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_UPDATE_NOTIFICATION_COUNT,
        countOfNotification: data,
      });
  },
  setBadgeCount: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_BADGE_COUNT,
        badgeCount: data,
      });
  },
  clearData: () => (dispatch: any) =>
    dispatch({
      type: actions.CLEAR_DATA,
    }),
  setNotificationList: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_NOTIFICATION_LIST,
        notificationList: data,
      });
  },
  setNotificationType: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_NOTIFICATION_TYPE,
        notificationType: data,
      });
  },
};

export default actions;
