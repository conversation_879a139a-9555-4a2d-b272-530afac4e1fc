/* eslint-disable no-console */
import types from "./actions";

const initialState = {
  deviceID: "",
  lastDeviceId: "",
  characteristicID: "",
  serviceID: "",
  lowAlert: "5",
  highAlert: "26",
  alertTime: {
    temp: 0,
    air: 0,
    voc: 0,
  },
  bleData: {},
  isBleConnected: false,
  emergencyAlert: false,
  activeChildDetail: {},
  connectedDeviceDetail: {},
  tempModal: false,
  isAlert: false,
  alertData: {},
  isCancelAlert: false,
  connectedDeviceList: [],
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case types.SET_DEVICE_ID:
      return {
        ...state,
        deviceID: action.deviceID,
      };
    case types.SET_LAST_DEVICE_ID:
      return {
        ...state,
        lastDeviceId: action.lastDeviceId,
      };
    case types.SET_BLEDATA:
      return {
        ...state,
        bleData: action.bleData,
      };
    case types.SET_ISBLECONNECTED:
      return {
        ...state,
        isBleConnected: action.isBleConnected,
      };
    case types.SET_LOWALERT:
      return {
        ...state,
        lowAlert: action.lowAlert,
      };
    case types.SET_ALERTTIME:
      return {
        ...state,
        alertTime: action.alertTime,
      };
    case types.SET_HIGHALERT:
      console.log("action.highAlert", action.highAlert);
      return {
        ...state,
        highAlert: action.highAlert,
      };
    case types.SET_CHARACTERISTIC_ID:
      return {
        ...state,
        characteristicID: action.characteristicID,
      };
    case types.SET_SERVICE_ID:
      return {
        ...state,
        serviceID: action.serviceID,
      };
    case types.SET_EMERGENCY_ALERT:
      return {
        ...state,
        emergencyAlert: action.emergencyAlert,
      };
    case types.SET_CONNECTED_DEVICE_DETAIL:
      return {
        ...state,
        connectedDeviceDetail: action.connectedDeviceDetail,
      };
    case types.SET_ACTIVE_CHILD_DETAIL:
      return {
        ...state,
        activeChildDetail: action.activeChildDetail,
      };
    case types.SET_TEMP_MODAL:
      return {
        ...state,
        tempModal: action.tempModal,
      };
    case types.SET_IS_ALERT:
      return {
        ...state,
        isAlert: action.isAlert,
      };
    case types.SET_ALERT_DATA_REDUX:
      return {
        ...state,
        alertData: action.alertData,
      };
    case types.SET_IS_CANCEL_ALERT:
      return {
        ...state,
        isCancelAlert: action.isCancelAlert,
      };
    case types.SET_CONNECTED_DEVICE_LIST:
      return {
        ...state,
        connectedDeviceList: action.connectedDeviceList,
      };

    default:
      return state;
  }
}
