/* eslint-disable quotes */
const actions = {
  SET_DEVICE_ID: "auth/SET_DEVICE_ID",
  SET_LAST_DEVICE_ID: "auth/SET_LAST_DEVICE_ID",
  SET_CHARACTERISTIC_ID: "auth/SET_CHARACTERISTIC_ID",
  SET_SERVICE_ID: "auth/SET_SERVICE_ID",
  SET_LOWALERT: "auth/SET_LOWALERT",
  SET_HIGHALERT: "auth/SET_HIGHALERT",
  SET_ALERTTIME: "auth/SET_ALERTTIME",
  SET_BLEDATA: "auth/SET_BLEDATA",
  SET_ISBLECONNECTED: "auth/SET_ISBLECONNECTED",
  SET_EMERGENCY_ALERT: "alert/SET_EMERGENCY_ALERT",
  SET_CONNECTED_DEVICE_ID: "device/SET_CONNECTED_DEVICE_ID",
  SET_CONNECTED_DEVICE_NAME: "device/SET_CONNECTED_DEVICE_NAME",
  SET_ACTIVE_CHILD_DETAIL: "device/SET_ACTIVE_CHILD_DETAIL",
  SET_CONNECTED_DEVICE_DETAIL: "device/SET_CONNECTED_DEVICE_DETAIL",
  SET_TEMP_MODAL: "device/SET_TEMP_MODAL",
  SET_IS_ALERT: "device/SET_IS_ALERT",
  SET_ALERT_DATA_REDUX: "device/SET_ALERT_DATA_REDUX",
  SET_IS_CANCEL_ALERT: "device/SET_IS_CANCEL_ALERT",
  SET_CONNECTED_DEVICE_LIST: "device/SET_CONNECTED_DEVICE_LIST",

  setDeviceID: (deviceID) => (dispatch) => {
    console.log("CHECK Device ID Dispatched = " + deviceID);
    return dispatch({
      type: actions.SET_DEVICE_ID,
      deviceID,
    });
  },

  setLastDeviceId: (lastDeviceId) => (dispatch) =>
    dispatch({
      type: actions.SET_LAST_DEVICE_ID,
      lastDeviceId,
    }),

  setIsBleConnected: (isBleConnected) => (dispatch) =>
    dispatch({
      type: actions.SET_ISBLECONNECTED,
      isBleConnected,
    }),

  setBleData: (bleData) => (dispatch) =>
    dispatch({
      type: actions.SET_BLEDATA,
      bleData,
    }),

  setAlertTime: (alertTime) => (dispatch) =>
    dispatch({
      type: actions.SET_ALERTTIME,
      alertTime,
    }),
  setIsAlert: (isAlert) => (dispatch) =>
    dispatch({
      type: actions.SET_IS_ALERT,
      isAlert,
    }),
  setAlertDataRedux: (alertData) => (dispatch) =>
    dispatch({
      type: actions.SET_ALERT_DATA_REDUX,
      alertData,
    }),
  setLowAlert: (lowAlert) => (dispatch) =>
    dispatch({
      type: actions.SET_LOWALERT,
      lowAlert,
    }),

  setHighAlert: (highAlert) => (dispatch) =>
    dispatch({
      type: actions.SET_HIGHALERT,
      highAlert,
    }),

  setcharacteristicID: (characteristicID) => (dispatch) =>
    dispatch({
      type: actions.SET_CHARACTERISTIC_ID,
      characteristicID,
    }),

  setServiceID: (serviceID) => (dispatch) =>
    dispatch({
      type: actions.SET_SERVICE_ID,
      serviceID,
    }),

  setEmergencyAlert: (emergencyAlert) => (dispatch) =>
    dispatch({
      type: actions.SET_EMERGENCY_ALERT,
      emergencyAlert,
    }),

  setConnectedDeviceDetail: (connectedDeviceDetail) => (dispatch) =>
    dispatch({
      type: actions.SET_CONNECTED_DEVICE_DETAIL,
      connectedDeviceDetail,
    }),

  setActiveChildDetail: (activeChildDetail) => (dispatch) =>
    dispatch({
      type: actions.SET_ACTIVE_CHILD_DETAIL,
      activeChildDetail,
    }),

  setTempModal: (tempModal) => (dispatch) =>
    dispatch({
      type: actions.SET_TEMP_MODAL,
      tempModal,
    }),
  setIsCancelAlert: (isCancelAlert) => (dispatch) =>
    dispatch({
      type: actions.SET_IS_CANCEL_ALERT,
      isCancelAlert,
    }),
  setConnectedDeviceList: (connectedDeviceList) => (dispatch) =>
    dispatch({
      type: actions.SET_CONNECTED_DEVICE_LIST,
      connectedDeviceList,
    }),
};

export default actions;
