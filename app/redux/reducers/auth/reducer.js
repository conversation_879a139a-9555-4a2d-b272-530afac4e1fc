/* eslint-disable no-console */
import types from "./actions";

const initialState = {
  userData: {},
  user_id: null,
  accessToken: "",
  walkthrough: true,
  darkmode: false,
  uuid: "",
  baseColor: {},
  brandToken: "",
  leadId: null,
  notificationShow: false,
  notificationData: {},
  langList: [],
  isFarenheit: false,
  userLocation: {},
  isCRASH: false,
  notificationCount: {},
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case types.SET_DATA:
      console.log(`${types.SET_DATA} => `);
      return {
        ...state,
        userData: action.userData,
      };
    case types.SET_LANGUAGELIST:
      console.log(`${types.SET_LANGUAGELIST} => `);
      return {
        ...state,
        langList: action.langList,
      };
    case types.SET_LEAD:
      return {
        ...state,
        leadId: action.leadId,
      };
    case types.SET_NOTIFICATIONSHOW:
      return {
        ...state,
        notificationShow: action.notificationShow,
      };
    case types.SET_NOTIFICATIONDATA:
      return {
        ...state,
        notificationData: action.notificationData,
      };
    case types.SET_BASECOLOR:
      return {
        ...state,
        baseColor: action.baseColor,
      };
    case types.SET_BRANDTOKEN:
      return {
        ...state,
        brandToken: action.brandToken,
      };
    case types.SET_WALKTHROUGH:
      return {
        ...state,
        walkthrough: action.walkthrough,
      };
    case types.SET_DARKMODE:
      return {
        ...state,
        darkmode: action.darkmode,
      };
    case types.SET_IS_FARENHEIT:
      return {
        ...state,
        isFarenheit: action.isFarenheit,
      };
    case types.SET_ACCESSSTOKEN:
      return {
        ...state,
        accessToken: action.accessToken,
      };
    case types.SET_USERID:
      return {
        ...state,
        user_id: action.user_id,
      };
    case types.SET_UUID:
      return {
        ...state,
        uuid: action.uuid,
      };
    case types.SET_USER_LOCATION:
      return {
        ...state,
        userLocation: action.userLocation,
      };
    case types.SET_NOTI_COUNT:
      return {
        ...state,
        notificationCount: action.notificationCount,
      };
    case types.SET_ISCRASH:
      return {
        ...state,
        isCRASH: action.isCRASH,
      };
    case types.LOGOUT:
      return {
        ...state,
        userData: {},
        userLocation: {},
      };
    default:
      return state;
  }
}
