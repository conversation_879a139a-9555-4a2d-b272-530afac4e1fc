const actions = {
  SET_DATA: "auth/SET_DATA",
  LOGOUT: "auth/LOGOUT",
  SET_WALKTHROUGH: "auth/SET_WALKTHROUGH",
  SET_DARKMODE: "auth/SET_DARKMODE",
  SET_ACCESSSTOKEN: "auth/SET_ACCESSSTOKEN",
  SET_USERID: "auth/SET_USERID",
  SET_UUID: "auth/SET_UUID",
  SET_BASECOLOR: "auth/SET_BASECOLOR",
  SET_BRANDTOKEN: "auth/SET_BRANDTOKEN",
  SET_LEAD: "auth/SET_LEAD",
  SET_NOTIFICATIONSHOW: "auth/SET_NOTIFICATIONSHOW",
  SET_NOTIFICATIONDATA: "auth/SET_NOTIFICATIONDATA",
  SET_LANGUAGELIST: "auth/SET_LANGUAGELIST",
  SET_IS_FARENHEIT: "auth/SET_IS_FARENHEIT",
  SET_USER_LOCATION: "auth/SET_USER_LOCATION",
  SET_ISCRASH: "auth/SET_ISCRASH",
  SET_NOTI_COUNT: "SET_NOTI_COUNT",

  setWalkthrough: (walkthrough) => (dispatch) =>
    dispatch({
      type: actions.SET_WALKTHROUGH,
      walkthrough,
    }),

  setLanguageList: (langList) => (dispatch) =>
    dispatch({
      type: actions.SET_LANGUAGELIST,
      langList,
    }),

  setNotificationShow: (notificationShow) => (dispatch) =>
    dispatch({
      type: actions.SET_NOTIFICATIONSHOW,
      notificationShow,
    }),

  setNotificationData: (notificationData) => (dispatch) =>
    dispatch({
      type: actions.SET_NOTIFICATIONSHOW,
      notificationData,
    }),

  setLeadId: (leadId) => (dispatch) =>
    dispatch({
      type: actions.SET_LEAD,
      leadId,
    }),

  setBrandToken: (brandToken) => (dispatch) =>
    dispatch({
      type: actions.SET_BRANDTOKEN,
      brandToken,
    }),

  setBaseColor: (baseColor) => (dispatch) =>
    dispatch({
      type: actions.SET_BASECOLOR,
      baseColor,
    }),

  setDarkmode: (darkmode) => (dispatch) =>
    dispatch({
      type: actions.SET_DARKMODE,
      darkmode,
    }),

  setIsFarenheit: (isFarenheit) => (dispatch) =>
    dispatch({
      type: actions.SET_IS_FARENHEIT,
      isFarenheit,
    }),

  setAccessToken: (accessToken) => (dispatch) =>
    dispatch({
      type: actions.SET_ACCESSSTOKEN,
      accessToken,
    }),

  setUserId: (user_id) => (dispatch) =>
    dispatch({
      type: actions.SET_USERID,
      user_id,
    }),

  setUUid: (uuid) => (dispatch) =>
    dispatch({
      type: actions.SET_UUID,
      uuid,
    }),

  setUserData: (data) => {
    let uData = {};
    if (data !== undefined && data !== null && Object.keys(data).length > 0) {
      uData = data;
    }

    return (dispatch) =>
      dispatch({
        type: actions.SET_DATA,
        userData: uData,
      });
  },

  setUserLocation: (userLocation) => (dispatch) =>
    dispatch({
      type: actions.SET_USER_LOCATION,
      userLocation,
    }),

  setIsCRASH: (isCRASH) => (dispatch) =>
    dispatch({
      type: actions.SET_ISCRASH,
      isCRASH,
    }),

  setNotiCount: (notificationCount) => (dispatch) =>
    dispatch({
      type: actions.SET_NOTI_COUNT,
      notificationCount,
    }),

  logOut: () => (dispatch) =>
    dispatch({
      type: actions.LOGOUT,
    }),
};

export default actions;
