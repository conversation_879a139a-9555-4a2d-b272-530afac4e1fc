import React, { Component } from "react";
import PropTypes from "prop-types";
import _ from "lodash";
import {
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  View,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Text,
  SafeAreaView,
} from "react-native";
// import Colors from '../config/Styles';
// import{AlertIOS,AppRegistry,Platform,StyleSheet,Text,TouchableOpacity,Button,View,} from 'react-native';
import Video from "react-native-video";
import LinearGradient from "react-native-linear-gradient";
import Icon from "react-native-vector-icons/FontAwesome";
import ProgressBar from "react-native-progress/Bar";
import { isIphoneX } from "react-native-iphone-x-helper";
import BaseColor from "../config/colors";

const styles = StyleSheet.create({
  mainCon: {
    width: Dimensions.get("window").width - 20,
    height: Dimensions.get("window").height,
    backgroundColor: "#0000",
    alignSelf: "center",
  },
  loaderView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  backgroundVideo: {
    // position: 'absolute',
    // left: 0,
    // bottom: 0,
    // right: 0,
    height: "100%",
  },
  playIconSty: {
    fontSize: 16,
    color: BaseColor.whiteColor,
    marginLeft: 3,
  },
  gradientView: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    top: 0,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#0000",
  },
  gredientViewSty: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: "center",
    justifyContent: "center",
  },
  controls: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    height: 48,
    left: 0,
    bottom: isIphoneX() ? 20 : 0,
    right: 0,
    position: "absolute",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around",
    paddingHorizontal: 10,
    // top: isIphoneX() ? 10 : 0,
  },
  mainButton: {
    marginRight: 15,
  },
  duration: {
    color: "#FFF",
    marginLeft: 15,
    fontSize: 16,
  },
  mainContain: {
    paddingVertical: isIphoneX() ? 50 : 0,
  },
});
export default class CVideoPlayer extends Component {
  constructor(props) {
    super(props);
    this.carousel = null;
    this.state = {
      paused: false,
      BtnView: false,
      rate: 1,
      volume: 1,
      muted: false,
      controls: false,
      progress: 0,
      duration: 0,
      setDuration: 0,
      loader: true,
    };
  }
  secondsToTime = (time) => {
    return ~~(time / 60) + ":" + (time % 60 < 10 ? "0" : "") + (time % 60);
  };
  onEnd = () => {
    const { setDuration } = this.state;
    // const  { source}
    this.setState(
      {
        BtnView: true,
        repeat: true,
        // progress: 0,
        // duration: setDuration,
      },
      () => {
        if (!this.state.paused) {
          setTimeout(() => {
            this.setState({
              BtnView: false,
            });
          }, 3000);
        }
      }
    );
  };
  handleProgress = (progress) => {
    this.setState({
      progress: progress.currentTime / this.state.duration,
    });
  };
  handleProgressPress = (e) => {
    const position = e.nativeEvent.locationX;
    const progress = (position / 250) * this.state.duration;
    // const isPlaying = !this.state.paused;

    this.player.seek(progress);
  };
  render() {
    const { paused, repeat, BtnView, loader } = this.state;
    const {
      VideoStyle,
      source,
      //   paused,
      // repeat,
      onError,
      MainViewStyle,
    } = this.props;

    return (
      <SafeAreaView style={[{ flex: 1 }, MainViewStyle]}>
        <Video
          repeat={true}
          paused={paused}
          source={{
            uri: source,
          }} // Can be a URL or a local file.
          ref={(ref) => {
            this.player = ref;
          }} // Store reference
          // onBuffer={this.onBuffer} // Callback when remote video is buffering
          onError={(errorDeatil) => {
            this.setState({
              error: true,
            });
            onError(errorDeatil);
          }}
          onProgress={(data) => {
            this.handleProgress(data);
          }}
          onEnd={() => {
            this.onEnd();
          }}
          resizeMode="contain"
          onLoad={(value) => {
            this.setState(
              {
                paused: true,
                BtnView: true,
                progress: value.currentTime,
                duration: value.duration,
                setDuration: value.duration,
                loader: false,
              },
              () => {
                if (!this.state.paused) {
                  setTimeout(() => {
                    this.setState({
                      BtnView: false,
                    });
                  }, 3000);
                }
              }
            );
          }}
          style={[styles.backgroundVideo, VideoStyle]}
          // controls={true}
        />
        <TouchableOpacity
          onPress={() => {
            this.setState(
              {
                paused: !paused,
                BtnView: true,
              },
              () => {
                if (this.state.paused === false) {
                  setTimeout(() => {
                    this.setState({
                      BtnView: false,
                    });
                  }, 1000);
                }
              }
            );
          }}
          activeOpacity={0.5}
          style={styles.gradientView}
        >
          {BtnView ? (
            <LinearGradient
              colors={[BaseColor.black70, BaseColor.blue]}
              style={styles.gredientViewSty}
              location={[0.5, 0.75, 0.9]}
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 1.0, y: 0.0 }}
            >
              <Icon
                name={paused ? "play" : "stop"}
                style={styles.playIconSty}
              />
            </LinearGradient>
          ) : null}
        </TouchableOpacity>
        {BtnView ? (
          <View style={styles.controls}>
            <TouchableWithoutFeedback onPress={this.handleProgressPress}>
              <View>
                <ProgressBar
                  progress={this.state.progress}
                  color="#FFF"
                  unfilledColor="rgba(255,255,255,.5)"
                  borderColor="#FFF"
                  width={300}
                  height={5}
                />
              </View>
            </TouchableWithoutFeedback>

            <Text allowFontScaling={false} style={styles.duration}>
              {this.secondsToTime(
                Math.floor(this.state.progress * this.state.duration)
              )}
            </Text>
          </View>
        ) : null}
        {loader ? (
          <View style={{ position: "absolute", top: "50%", left: "50%" }}>
            <ActivityIndicator
              size={"small"}
              color={BaseColor.whiteColor}
              animating
            />
          </View>
        ) : null}
      </SafeAreaView>
    );
  }
}

CVideoPlayer.propTypes = {
  data: PropTypes.objectOf(PropTypes.any),
  source: PropTypes.string,
  paused: PropTypes.bool,
  repeat: PropTypes.bool,
  onError: PropTypes.func,
  MainViewStyle: PropTypes.any,
};

CVideoPlayer.defaultProps = {
  data: {},
  source:
    "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
  paused: true,
  repeat: true,
  onError: () => {},
  MainViewStyle: { height: "100%" },
};
