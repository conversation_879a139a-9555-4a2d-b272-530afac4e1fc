/* eslint-disable no-nested-ternary */
import React, { useRef, useEffect } from "react";
import { ActivityIndicator, Text, TouchableOpacity, View } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";
import FAIcon from "react-native-vector-icons/FontAwesome";
import { useTheme } from "@react-navigation/native";
import styles from "./styles";
import { CustomIcon } from "../../config/LoadIcons";

import LinearGradient from "react-native-linear-gradient";
const CButton = (props) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    loader,
    title = "Title",
    onPress = () => {},
    style,
    titleStyle,
    btnColor = ["rgba(20, 135, 255, 12)", "rgba(41, 239, 196, 90)"],
    smallBtn,
    done,
    anim = false,
    playAnimation,
    iconname,
    iconsize = 24,
    iconColor = BaseColor.blueLight,
    backAnim = false,
  } = props;

  const animation = useSharedValue({ width: "100%", borderRadius: 25 });

  const animationStyle = useAnimatedStyle(() => ({
    width: withTiming(animation.value.width, {
      duration: 1000,
    }),

    borderRadius: withTiming(animation.value.borderRadius, {
      duration: 1000,
    }),
  }));

  useEffect(() => {
    if (playAnimation) animation.value = { width: "16%", borderRadius: 18 };
  }, [playAnimation]);

  useEffect(() => {
    if (backAnim) animation.value = { width: "100%", borderRadius: 25 };
  }, [backAnim]);

  const koibhi = () => (
    <>
      {iconname ? (
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={onPress}
          style={{
            height: "100%",
            width: "100%",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <CustomIcon name={iconname} size={iconsize} color={iconColor} />
        </TouchableOpacity>
      ) : loader ? (
        <View>
          <ActivityIndicator size={22} color={BaseColor.blueLight} />
        </View>
      ) : done ? (
        <View>
          <CustomIcon name="check" size={18} color={BaseColor.blueLight} />
        </View>
      ) : (
        <TouchableOpacity
          activeOpacity={0.7}
          style={{
            height: "100%",
            width: "100%",
            justifyContent: "center",
            alignItems: "center",
          }}
          onPress={() => {
            if (playAnimation === true) {
              animation.value = { width: "16%", borderRadius: 18 };
            }
            onPress();
          }}
        >
          <Text
            style={[
              styles.txtStyle,
              { color: BaseColor.blueLight },
              titleStyle,
            ]}
          >
            {title}
          </Text>
        </TouchableOpacity>
      )}
    </>
  );

  return (
    <>
      {anim ? (
        <Animated.View
          style={[
            styles.btnStyle,
            { backgroundColor: BaseColor.whiteColor },
            animationStyle,
            style,
          ]}
        >
          {koibhi()}
        </Animated.View>
      ) : (
        <LinearGradient
          colors={btnColor}
          useAngle={true}
          angle={138}
          // colors={["#4c669f", "#3b5998", "#192f6a"]}
          // start={{ y: 0.0, x: 0.0 }}
          // end={{ y: 0.0, x: 1.0 }}
          style={[
            styles.btnStyle,
            {
              borderRadius: loader ? 18 : 25,
            },
            style,
          ]}
        >
          {koibhi()}
        </LinearGradient>
      )}
    </>
  );
};

export default CButton;
