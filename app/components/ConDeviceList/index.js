/* eslint-disable quotes */
import React from "react";
import { Text, View, TouchableOpacity, Image } from "react-native";
import { useTheme } from "@react-navigation/native";
import { CustomIcon } from "../../config/LoadIcons";
import styles from "./styles";

const DeviceList = (props) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const { img, title, text, onEditPress } = props;
  return (
    <View style={styles.root}>
      <TouchableOpacity
        activeOpacity={0.7}
        style={[
          styles.editIcon,
          {
            backgroundColor: BaseColor.whiteColor,
            shadowColor: BaseColor.blackColor,
          },
        ]}
        onPress={onEditPress}
      >
        <CustomIcon
          name="edit"
          size={22}
          color={BaseColor.blackColor}
          style={{ textAlign: "center" }}
        />
      </TouchableOpacity>
      <Image style={styles.img} source={{ uri: img }} />
      <View style={styles.textView}>
        <Text style={[styles.titleText, { color: BaseColor.blackColor }]}>
          {title}
        </Text>
        <Text style={[styles.otherText, { color: BaseColor.textGrey }]}>
          {text}
        </Text>
      </View>
    </View>
  );
};

export default DeviceList;
