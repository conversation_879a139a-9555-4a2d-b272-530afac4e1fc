/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useRef, useState } from "react";
import {
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Animated,
} from "react-native";
import Ionicons from "react-native-vector-icons/Ionicons";
import FAIcon from "react-native-vector-icons/FontAwesome";
import LinearGradient from "react-native-linear-gradient";
import DatePicker from "react-native-datepicker";
import moment from "moment";
import { useTheme } from "@react-navigation/native";
import styles from "./styles";
import { CustomIcon } from "../../config/LoadIcons";
import { FontFamily } from "../../config/typography";
import BaseColors from "../../config/colors";
import { translate } from "../../lang/Translate";

// Remove font scale
Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;

const CInput = React.forwardRef((props, ref) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    onSubmitEditing = () => {},
    placeholder = "Default placeholder",
    floatingPlaceholder = false,
    inputBackGroundColor = BaseColor.inputBackGroundColor,
    onChangeText = () => {},
    isLastInput,
    returnKeyType,
    textInputWrapper,
    secureTextEntry,
    icon,
    hideRightIcon,
    rightIconStyle,
    onShowPasswordpress,
    editable = true,
    value,
    colorStyle,
    keyboardType,
    placeholderTextColor = BaseColor.placeHolderColor,
    disabled = false,
    error,
    maxLength = 50,
    showCountryPicker = false,
    onCountrySelect,
    countryCode,
    containterStyle,
    inputStyle,
    textInputStyle,
    iconStyle,
    viewStyle,
    txtColor = BaseColor.blackColor,
    countryLabelColor = BaseColor.blackColor,
    multiline = false,
    disableTxtColor = "rgba(0, 0, 0, 0.4)",
    iconColor,
    iconSize,
    iconName,
    keyboardAppearance,
    hideLeftIcon,
    showError,
    errorMsg,
    datePicker,
    selectDate,
    onDateChange,
    onFocus = () => {},
    onBlur = () => {},
    ...rest
  } = props;

  const [showDate, setshowDate] = useState(false);
  const [small, setSmall] = useState();
  const dataPickRef = useRef();
  const moveText = useRef(new Animated.Value(0)).current;

  const moveTextTop = () => {
    setSmall(true);
    onFocus();
    Animated.timing(moveText, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const moveTextBottom = () => {
    setSmall(false);
    onBlur();
    if (value === "") {
      Animated.timing(moveText, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  };
  useEffect(() => {
    if (value !== "") {
      moveTextTop();
    }
  }, []);
  const yVal = moveText.interpolate({
    inputRange: [0, 1],
    outputRange: [4, -15],
  });

  const animStyle = {
    transform: [
      {
        translateY: yVal,
      },
    ],
  };
  return (
    <>
      <View
        style={[
          styles.inputWrapper,
          {
            backgroundColor: BaseColors.inputBackGroundColor,
            borderRadius: 12,
            // borderColor: BaseColor.textGrey,
            marginTop: 10,
          },
        ]}
      >
        {datePicker && value !== "" && (
          <Text
            style={{
              paddingLeft: 20,
              color: BaseColor.inputBackGroundColor,
              fontSize: small ? 12 : 14,
              paddingBottom: 1,
              top: small ? 14 : 8,
              position: "absolute",
              fontWeight: "700",
              fontFamily: FontFamily.bold,
            }}
          >
            {placeholder}
          </Text>
        )}
        {floatingPlaceholder ? (
          <Animated.View style={[styles.animatedStyle, animStyle]}>
            <Text
              style={{
                marginLeft: 20,
                color: "black",
                fontSize: small || value !== "" ? 12 : 14,
                paddingBottom: 1,
                top: small || value !== "" ? 30 : 20,
                position: "absolute",
                fontWeight: "700",
                fontFamily: FontFamily.bold,
              }}
            >
              {placeholder}
            </Text>
          </Animated.View>
        ) : null}
        {/* <View style={[viewStyle || styles.inputWrapper, textInputWrapper]}> */}
        {datePicker ? (
          <View
            style={{
              height: 64,
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <DatePicker
              ref={dataPickRef}
              style={{
                height: 64,
                justifyContent: "center",
                alignItems: "center",
                paddingStart: 20,
                color: value ? BaseColor.textGrey : BaseColor.textGrey,
              }}
              allowFontScaling={false}
              date={value || ""}
              mode="date"
              placeholder={translate("dob")}
              placeHolderColor={BaseColor.blackColor}
              format="DD-MM-YYYY"
              confirmBtnText="Confirm"
              cancelBtnText="Cancel"
              maxDate={moment().format("DD-MM-YYYY")}
              customStyles={{
                dateIcon: {
                  position: "absolute",
                  right: 20,
                  top: 6,
                  marginLeft: 0,
                  paddingRight: 10,
                },
                dateInput: {
                  width: "50%",
                  borderWidth: 0,
                  alignItems: "flex-start",
                  paddingTop: small || value !== "" ? 5 : 12,
                },
                disabled: {
                  backgroundColor: "#F6F6F6",
                  height: 0,
                },
                dateText: {
                  fontSize: 14,
                  color: BaseColor.blackColor,
                  fontFamily: FontFamily.default,
                },
                placeholderText: {
                  fontSize: 14,
                  color: BaseColor.blackColor,
                  fontWeight: "700",
                  fontFamily: FontFamily.bold,
                  marginTop: 0,
                },
                datePicker: {
                  backgroundColor: "#d1d3d8",
                  justifyContent: "center",
                },
              }}
              showIcon={false}
              onDateChange={onDateChange}
              // disabled={editable}
            />
            {!hideRightIcon ? (
              <TouchableOpacity
                style={{ marginRight: 20, marginBottom: 5 }}
                onPress={onShowPasswordpress}
              >
                <FAIcon
                  name={iconName || "rocket"}
                  size={15}
                  color={iconColor || BaseColor.blackColor}
                />
              </TouchableOpacity>
            ) : null}
          </View>
        ) : (
          <View
            style={{
              height: 64,
              flexDirection: "row",
              alignItems: "center",
              paddingStart: 20,
            }}
          >
            {!hideLeftIcon ? (
              <CustomIcon
                name={iconName || "rocket"}
                size={18}
                color={iconColor || BaseColor.blackColor}
              />
            ) : null}
            <TextInput
              {...rest}
              ref={ref}
              selectionColor={BaseColor.black50}
              placeholder={floatingPlaceholder ? null : placeholder}
              placeholderTextColor={placeholderTextColor}
              style={[
                styles.input,
                {
                  marginStart: hideLeftIcon ? 0 : 12,
                  color: "black",
                  marginTop: 10,
                  fontSize: 14,
                  // color: showError ? BaseColor.orange : BaseColor.whiteColor,
                },
                inputStyle,
                editable ? styles.colorInput : colorStyle,
              ]}
              onChangeText={onChangeText}
              blurOnSubmit={false}
              onFocus={moveTextTop}
              onBlur={moveTextBottom}
              onSubmitEditing={onSubmitEditing}
              returnKeyType={returnKeyType || (isLastInput ? "go" : "next")}
              secureTextEntry={secureTextEntry}
              editable={editable}
              value={value}
              maxLength={maxLength}
              keyboardType={keyboardType}
              multiline={multiline}
            />
            {!hideRightIcon ? (
              <TouchableOpacity
                style={{ marginRight: 20, marginBottom: 5 }}
                onPress={onShowPasswordpress}
              >
                <FAIcon
                  name={iconName || "rocket"}
                  size={15}
                  color={iconColor || BaseColor.blackColor}
                />
              </TouchableOpacity>
            ) : null}
            {showError && (
              <CustomIcon
                name="warning"
                size={18}
                color={iconColor || BaseColor.alertRed}
                style={{ marginEnd: 24 }}
              />
            )}
            {/* </View> */}
          </View>
        )}
      </View>
      {showError ? (
        <Text
          style={{
            // fontFamily: FontFamily.default,
            textAlign: "left",
            fontFamily: FontFamily.bold,
            fontWeight: "700",
            color: BaseColor.alertRed,
            marginStart: 24,
            marginEnd: 24,
            marginTop: 4,
            fontSize: 12,
            letterSpacing: 1,
          }}
        >
          {errorMsg}
        </Text>
      ) : null}
    </>
  );
});

export default CInput;
