/* eslint-disable react/jsx-props-no-spreading */
import React, { useRef, useState } from "react";
import { Text, TextInput, TouchableOpacity, View } from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome";
import LinearGradient from "react-native-linear-gradient";
import DatePicker from "react-native-datepicker";
import moment from "moment";
import { useTheme } from "@react-navigation/native";
import styles from "./styles";
import { CustomIcon } from "../../config/LoadIcons";
import { FontFamily } from "../../config/typography";

// Remove font scale
Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;

const CInput = React.forwardRef((props, ref) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    onSubmitEditing = () => {},
    placeholder = "Default placeholder",
    onChangeText = () => {},
    isLastInput,
    returnKeyType,
    textInputWrapper,
    secureTextEntry,
    icon,
    rightIcon,
    rightIconStyle,
    editable = true,
    value,
    colorStyle,
    keyboardType,
    placeholderTextColor = BaseColor.placeHolderColor,
    disabled = false,
    error,
    maxLength = 50,
    showCountryPicker = false,
    onCountrySelect,
    countryCode,
    containterStyle,
    inputStyle,
    textInputStyle,
    iconStyle,
    viewStyle,
    txtColor = BaseColor.blackColor,
    countryLabelColor = BaseColor.blackColor,
    multiline = false,
    disableTxtColor = "rgba(0, 0, 0, 0.4)",
    iconColor,
    iconSize,
    iconName,
    keyboardAppearance,
    hideLeftIcon,
    showError,
    errorMsg,
    datePicker,
    selectDate,
    onDateChange,
    selectColor,
    inputheight,
    ...rest
  } = props;

  const [showDate, setshowDate] = useState(false);
  const dataPickRef = useRef();

  return (
    <>
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={["#0000", BaseColor.white20]}
        style={[
          styles.inputWrapper,
          {
            backgroundColor: BaseColor.transparentWhite,
            borderColor: BaseColor.whiteColor,
          },
          textInputWrapper,
        ]}
      >
        {/* <View style={[viewStyle || styles.inputWrapper, textInputWrapper]}> */}
        {datePicker ? (
          <DatePicker
            ref={dataPickRef}
            style={{
              height: 50,
              justifyContent: "center",
              alignItems: "center",
              paddingStart: 24,
              color: value
                ? BaseColor.placeHolderColor
                : selectColor || BaseColor.whiteColor,
            }}
            allowFontScaling={false}
            date={value || ""}
            mode="date"
            placeholder="DOB"
            placeHolderColor={BaseColor.placeHolderColor}
            format="DD-MM-YYYY"
            confirmBtnText="Confirm"
            cancelBtnText="Cancel"
            maxDate={moment().format("DD-MM-YYYY")}
            customStyles={{
              dateIcon: {
                position: "absolute",
                right: 20,
                top: 6,
                marginLeft: 0,
                paddingRight: 10,
              },
              dateInput: {
                width: "50%",
                borderWidth: 0,
                alignItems: "flex-start",
              },
              disabled: {
                backgroundColor: "#F6F6F6",
                height: 0,
              },
              dateText: {
                fontSize: 14,
                color: selectColor || BaseColor.whiteColor,
                fontFamily: FontFamily.default,
              },
              placeholderText: {
                fontSize: 14,
                color: "#bdbdbd",
              },
              datePicker: {
                backgroundColor: "#d1d3d8",
                justifyContent: "center",
              },
            }}
            showIcon={false}
            onDateChange={onDateChange}
          />
        ) : (
          <View
            style={{
              height: inputheight || 50,
              flexDirection: "row",
              alignItems: "center",
              paddingStart: 24,
            }}
          >
            {!hideLeftIcon ? (
              <CustomIcon
                name={iconName || "rocket"}
                size={18}
                color={iconColor || BaseColor.whiteColor}
              />
            ) : null}
            <TextInput
              {...rest}
              ref={ref}
              placeholder={placeholder}
              placeholderTextColor={placeholderTextColor}
              style={[
                styles.input,
                {
                  marginStart: hideLeftIcon ? 0 : 12,
                  color: BaseColor.whiteColor,
                  height: inputheight || 50,
                  // color: showError ? BaseColor.blue : BaseColor.whiteColor,
                },
                inputStyle,
                editable ? styles.colorInput : colorStyle,
              ]}
              onChangeText={onChangeText}
              blurOnSubmit={false}
              onSubmitEditing={onSubmitEditing}
              returnKeyType={returnKeyType || (isLastInput ? "go" : "next")}
              secureTextEntry={secureTextEntry}
              editable={editable}
              value={value}
              maxLength={maxLength}
              keyboardType={keyboardType}
              multiline={multiline}
            />
            {showError && (
              <CustomIcon
                name="warning"
                size={18}
                color={iconColor || BaseColor.alertRed}
                style={{ marginEnd: 24 }}
              />
            )}
            {/* </View> */}
          </View>
        )}
      </LinearGradient>
      {showError ? (
        <Text
          style={{
            // fontFamily: FontFamily.default,
            textAlign: "left",
            fontWeight: "bold",
            color: BaseColor.alertRed,
            marginStart: 24,
            marginEnd: 24,
            marginTop: 4,
            fontSize: 12,
            letterSpacing: 1,
          }}
        >
          {errorMsg}
        </Text>
      ) : null}
    </>
  );
});

export default CInput;
