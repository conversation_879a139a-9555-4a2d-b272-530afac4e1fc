import { useTheme } from "@react-navigation/native";
import { isEmpty } from "lodash";
import React, { useState } from "react";
import {
  Dimensions,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import LinearGradient from "react-native-linear-gradient";
import { SvgUri } from "react-native-svg";
import { translate } from "../../lang/Translate";
import { CustomIcon } from "../../config/LoadIcons";
import { FontFamily } from "../../config/typography";

const DropDown = (props) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    data,
    onSelect = () => {},
    selectedObject = {},
    placeholder = "Placeholder",
    valueProp,
    style,
    textStyle,
    lang,
    svgProp,
    titleD = "",
    placeHolderColor,
    showArrow = true,
    textAlign,
    value,
    selectChild,
    inputBackGroundColor = BaseColor.inputBackGroundColor,
    deviceList,
    editable = true,
  } = props;

  const [showList, setshowList] = useState(false);

  return (
    <>
      {/* <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={[inputBackGroundColor, inputBackGroundColor]}
        style={[
          {
            backgroundColor: BaseColor.whiteColor,
            borderRadius: 10,
            height: 65,

            // alignItems: "center",
            // borderWidth: 1,
            borderColor: BaseColor.whiteColor,
          },
          style,
        ]}
      > */}
      {value !== "" && !textAlign && !selectChild && !deviceList && (
        <Text
          style={{
            paddingLeft: 20,
            color: BaseColor.blackColor,
            fontSize: 12,
            paddingBottom: 1,
            top: 14,
            position: "absolute",
            fontWeight: "700",
            fontFamily: FontFamily.bold,
          }}
        >
          {placeholder}
        </Text>
      )}
      <Text
        style={{
          fontSize: 12,
          fontWeight: "bold",
          marginTop: value !== "" ? 2 : 0,
          marginBottom: 7,
        }}
      >
        {titleD}
      </Text>
      <View style={{ flexDirection: "row" }}>
        <TouchableOpacity
          disabled={editable}
          onPress={() => setshowList(true)}
          style={{
            height: "100%",
            alignItems: "center",
            flexDirection: "row",
            width: "100%",
            paddingHorizontal: 5,
          }}
        >
          {lang ? (
            <SvgUri
              width={24}
              height={24}
              uri={!isEmpty(selectedObject) ? selectedObject[svgProp] : null}
              style={{
                marginStart: 10,
              }}
            />
          ) : null}
          <Text
            style={{
              color: placeHolderColor ? placeHolderColor : BaseColor.blackColor,
              flex: 1,
              ...textStyle,
              marginStart: lang ? 12 : null,
              fontWeight: placeHolderColor ? "700" : "100",
              textAlign: textAlign ? "right" : null,
              fontFamily: placeHolderColor
                ? FontFamily.bold
                : FontFamily.regular,
              marginTop: 2,
            }}
          >
            {isEmpty(selectedObject) ? placeholder : selectedObject[valueProp]}
          </Text>
          {showArrow && (
            <CustomIcon
              name="expand-button"
              size={16}
              color={BaseColor.textGrey}
            />
          )}
        </TouchableOpacity>
      </View>
      {/* </LinearGradient> */}
      <Modal
        visible={showList}
        onRequestClose={() => setshowList(false)}
        transparent
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            setshowList(false);
          }}
          style={{
            flex: 1,
            backgroundColor: BaseColor.black40,
            justifyContent: "center",
          }}
        >
          <View
            style={{
              marginHorizontal: 24,
              backgroundColor: BaseColor.whiteColor,
              borderRadius: 12,
              paddingVertical: 12,
              maxHeight: Dimensions.get("window").height / 1.5,
            }}
          >
            <ScrollView>
              {data.map((item) => (
                <TouchableOpacity
                  activeOpacity={0.7}
                  style={{
                    backgroundColor: BaseColor.whiteColor,
                    padding: 8,
                  }}
                  onPress={() => {
                    onSelect(item);
                    setshowList(false);
                  }}
                >
                  <Text style={{ color: BaseColor.blackColor }}>
                    {item[valueProp]}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default DropDown;
