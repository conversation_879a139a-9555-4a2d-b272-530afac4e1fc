import React, { useState } from "react";
// import { View, Text } from 'react-native';
import OTPInputView from "@twotalltotems/react-native-otp-input";
import { Dimensions, TouchableOpacity, View } from "react-native";
import { useTheme } from "@react-navigation/native";
import styles from "./styles";
import { FontFamily } from "../../config/typography";
const { height: dHeight, width: dWidth } = Dimensions.get("window");
export default function OtpComponent(props) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    onCodeFilled,
    noOfBox = 4,
    otpWrapperStyle,
    codeInputFieldStyle,
    codeInputHighlightStyle,
    onCodeChanged,
    onotpPress,
    code,
  } = props;

  const [clearCode, setclearCode] = useState(false);
  // const [code, setcode] = useState();

  return (
    <>
      <View style={{ alignItems: "center", width: "90%" }}>
        <OTPInputView
          style={{
            height: 100,
            fontSize: 20,
            fontFamily: FontFamily.regular,
            // paddingHorizontal: 15,
            // justifyContent: "center",
            // alignItems: "center",
            ...otpWrapperStyle,
            // backgroundColor: "red",
            width: "90%",
          }}
          editable={false}
          code={code}
          pinCount={noOfBox}
          // autoFocusOnLoad
          codeInputFieldStyle={{
            ...styles.underlineStyleBase,
            backgroundColor: BaseColor.whiteColor,
            color: BaseColor.blackColor,
            borderWidth: 1,
            borderRadius: 12,
            height: dWidth * 0.16,
            width: dWidth * 0.16,
            fontWeight: "bold",
            borderColor: BaseColor.blackColor,
            ...codeInputFieldStyle,
          }}
          codeInputHighlightStyle={{
            ...styles.underlineStyleHighLighted,
            backgroundColor: BaseColor.inputBackGroundColor,
            color: BaseColor.blackColor,
            borderWidth: 1,
            borderRadius: 12,
            height: dWidth * 0.16,
            width: dWidth * 0.16,
            borderColor: BaseColor.blackColor,
            ...codeInputHighlightStyle,
          }}
          clearInputs={clearCode}
          onCodeFilled={(code) => {
            onCodeFilled(code);
            setclearCode(true);
            setTimeout(() => {
              setclearCode(false);
              // setcode();
            }, 1000);
          }}
          onCodeChanged={(code) => {
            onCodeChanged(code);
            // setcode(code);
          }}
        />
        <TouchableOpacity
          style={{
            backgroundColor: "transparent",
            height: 100,
            width: "100%",
            position: "absolute",
          }}
          onPress={onotpPress}
        />
      </View>
    </>
  );
}
