/* eslint-disable quotes */
import { useTheme } from "@react-navigation/native";
import React, { useEffect, useState } from "react";
import {
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  Text,
  View,
  Image,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { CustomIcon } from "../../config/LoadIcons";
import { FontFamily } from "../../config/typography";
import { translate } from "../../lang/Translate";
import CButton from "../CButton";
import CInput from "../CInput";
import bluetoothActions from "../../redux/reducers/bluetooth/actions";

const CAlert = (props) => {
  const { setLowAlert, setHighAlert } = bluetoothActions;
  const colors = useTheme();
  const dispatch = useDispatch();
  const BaseColor = colors.colors;
  const [stateData, setStateData] = useState({
    lowTemp: "",
    highTemp: "",
  });
  const { lowAlert, highAlert } = useSelector((state) => state.bluetooth);

  const {
    visible,
    onRequestClose,
    alertMessage,
    alertTitle,
    onCancelPress = () => {},
    onOkPress = () => {},
    agreeTxt = translate("alertOkBtn"),
    cancelTxt = translate("alertCancelBtn"),
    type = "",
    loader = false,
    icon,
    images,
  } = props;

  useEffect(() => {
    if (visible) {
      setStateData({ lowTemp: lowAlert, highTemp: highAlert });
    }
  }, [visible]);

  // this function for handle Ok button press
  function handleOk() {
    if (type === "temp") {
      dispatch(setLowAlert(stateData.lowTemp));
      dispatch(setHighAlert(stateData.highTemp));
    }
    setTimeout(() => {
      onOkPress();
    }, 200);
  }

  if (!visible) {
    return <></>;
  }
  const image = require("../../assets/images/thumbs.png");
  return (
    <Modal
      visible={visible}
      onRequestClose={onRequestClose}
      style={{
        justifyContent: "flex-end",
        margin: 0,
        alignSelf: "flex-end",
        alignItems: "flex-end",
        alignContent: "flex-end",
        bottom: 0,
      }}
      transparent
      // animationType="slide"
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1 }} bounces={false}>
        <KeyboardAvoidingView
          style={{
            flex: 1,
            overflow: "hidden",
          }}
          behavior={Platform.OS === "ios" ? "height" : null}
        >
          <View
            style={{
              flex: 1,
              justifyContent: "center",
              backgroundColor: BaseColor.black40,
              alignItems: "center",
            }}
          >
            <View
              style={{
                borderRadius: 16,
                backgroundColor: BaseColor.whiteColor,
                padding: 22,
                alignItems: "center",
                paddingTop: 48,
                width: "90%",
              }}
            >
              {icon ? (
                <View
                  style={{
                    backgroundColor: BaseColor.blueDark,
                    justifyContent: "center",
                    alignItems: "center",
                    height: 64,
                    width: 64,
                    borderRadius: 64,
                    position: "absolute",
                    top: -30,
                  }}
                >
                  <Image
                    style={{
                      height: 64,
                      width: 64,
                      borderRadius: 64,
                    }}
                    source={image}
                  />
                </View>
              ) : (
                <View
                  style={{
                    backgroundColor: BaseColor.alertRed,
                    justifyContent: "center",
                    alignItems: "center",
                    height: 64,
                    width: 64,
                    borderRadius: 64,
                    position: "absolute",
                    top: -25,
                  }}
                >
                  <CustomIcon
                    name="warning"
                    size={24}
                    color={BaseColor.whiteColor}
                  />
                </View>
              )}
              {icon ? null : (
                <Text
                  style={{
                    color: BaseColor.black90,
                    fontSize: 16,
                    fontFamily: FontFamily.default,
                    fontWeight: "bold",
                  }}
                >
                  {alertTitle}
                </Text>
              )}
              <Text
                style={{
                  textAlign: "center",
                  width: "76%",
                  marginTop: 16,
                  fontFamily: FontFamily.default,
                  color: BaseColor.blackColor,
                }}
              >
                {alertMessage}
              </Text>
              {images ? (
                <View
                  style={{
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: 20,
                  }}
                >
                  <Image
                    style={{
                      height: 80,
                      width: 80,
                    }}
                    source={images}
                  />
                </View>
              ) : null}
              {type === "temp" ? (
                <View style={{ width: "100%" }}>
                  <CInput
                    keyboardType="numeric"
                    placeholder="Low Temperature"
                    value={stateData.lowTemp}
                    onChangeText={(val) => {
                      setStateData({ ...stateData, lowTemp: val });
                    }}
                    inputStyle={{ color: BaseColor.blackColor }}
                    textInputWrapper={{
                      width: "100%",
                      borderWidth: 1,
                      borderColor: BaseColor.blackColor,
                    }}
                    hideLeftIcon
                    placeholderTextColor={BaseColor.textGrey}
                  />
                  <CInput
                    keyboardType="numeric"
                    placeholder="High Temperature"
                    value={stateData.highTemp}
                    onChangeText={(val) => {
                      setStateData({ ...stateData, highTemp: val });
                    }}
                    inputStyle={{ color: BaseColor.blackColor }}
                    textInputWrapper={{
                      width: "100%",
                      borderWidth: 1,
                      borderColor: BaseColor.blackColor,
                      marginTop: 10,
                    }}
                    hideLeftIcon
                    placeholderTextColor={BaseColor.textGrey}
                  />
                </View>
              ) : null}
              <View style={{ flexDirection: "row", marginTop: 24 }}>
                {type === "tempAlert" ? null : (
                  <CButton
                    title={cancelTxt}
                    style={{
                      flex: 1,
                      marginEnd: 8,
                      borderWidth: 1,
                      borderColor: BaseColor.blueDark,
                      backgroundColor: BaseColor.whiteColor,
                      elevation: 0,
                      shadowOffset: {
                        width: 0,
                        height: 0,
                      },
                      shadowOpacity: 0.0,
                    }}
                    titleStyle={[
                      { color: BaseColor.whiteColor },
                      type === "leftChild" ? { fontSize: 11 } : {},
                    ]}
                    onPress={() => {
                      onCancelPress();
                    }}
                  />
                )}
                <CButton
                  title={agreeTxt}
                  style={{
                    flex: 1,
                    marginStart: 8,
                    borderWidth: 1,
                    borderColor: BaseColor.blueDark,
                    backgroundColor: BaseColor.blueDark,
                    elevation: 0,
                    shadowOffset: {
                      width: 0,
                      height: 0,
                    },
                    shadowOpacity: 0.0,
                  }}
                  loader={loader}
                  titleStyle={[
                    { color: BaseColor.whiteColor },
                    type === "leftChild" ? { fontSize: 11 } : {},
                  ]}
                  onPress={handleOk}
                />
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </ScrollView>
    </Modal>
  );
};

export default CAlert;
