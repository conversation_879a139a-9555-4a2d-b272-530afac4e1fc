// /* eslint-disable no-nested-ternary */
// /* eslint-disable quotes */
// import React, { useEffect, useState } from "react";
// import { Platform, View } from "react-native";
// import { useDispatch, useSelector } from "react-redux";
// import { isObject, isEmpty, isString } from "lodash";
// import messaging from "@react-native-firebase/messaging";
// import { withInAppNotification } from "../../lib/react-native-in-app-notification";
// import BaseSetting from "../../config/setting";
// import AuthActions from "../../redux/reducers/auth/actions";
// import { getApiData } from "../../utils/apiHelper";
// import InAppModal from "../InAppModal";
// import { addAction, sendErrorReport } from "../../utils/commonFunction";

// const IOS = Platform.OS === "ios";
// // const PushNotification = React.forwardRef((props, ref) => {
// const PushNotification = (props) => {
//   console.log("CALLED");
//   const { navigation } = props;
//   const { setUUid } = AuthActions;
//   // const { setNotiDetail, setNotiBadge, setCurrentTab } = commonActions;
//   // const { setConfirmData } = jobActions;
//   const dispatch = useDispatch();
//   const { uuid, userData, accessToken } = useSelector((state) => state.auth);
//   // const commonData = useSelector((state) => state.common);
//   // const { settings } = useSelector((state) => state.common);
//   // const [requestModal, setRequestModal] = useState(false);
//   const [notificationDetail, setNotificationDetail] = useState({});
//   const [postModal, setPostModal] = useState(false);
//   // const [cancelRequestModal, setCancelRequestModal] = useState(false);
//   // const [requestCancelPrice, setRequestCancelPrice] = useState(0);
//   // const [appUpdateModal, setAppUpdateModal] = useState(false);
//   // const [updateMandatory, setUpdateMandatory] = useState();
//   // const appState = useAppStatus();
//   // const [cAlert, setCAlert] = useState({
//   //   showAlert: false,
//   //   title: '',
//   //   message: '',
//   //   type: '',
//   // });

//   useEffect(() => {
//     if (isEmpty(accessToken)) {
//       const dontGo = ["SplashScreen", "Walkthrough", "RedirectLS"];
//       const currentScreen = navigation.current?.getCurrentRoute().name;
//       if (!dontGo.includes(currentScreen)) {
//         navigation.current.navigate("RedirectLS");
//       }
//     }
//   }, [accessToken]);

//   useEffect(() => {
//     if (isObject(userData) && !isEmpty(userData)) {
//       checkNotificationPermission();
//       console.log("CALLED====2");
//     }
//     // To Display a test notification
//     // props.showNotification({
//     //   title: 'New Provider Found!',
//     //   message: 'Hello we have found a new provider for you!! Please check now.',
//     //   onPress: () => {
//     //     // if (notificationData.type === 'new_job_request') {
//     //     //   setRequestModal(true);
//     //     // }
//     //   },
//     // });
//   }, [props, userData, accessToken]);

//   // useEffect(() => {
//   //   checkForAppUpdate();
//   // }, [appState, settings]);

//   // This Function checks for App version update!!
//   // function checkForAppUpdate(= false) {
//   //   const isClient = Config.IS_CLIENT === 'true';
//   //   if (isEmpty(settings)) {
//   //     return;
//   //   }
//   //   let type = '';
//   //   if (!IOS && !isClient) {
//   //     type = 'provider-android-app-version';
//   //   } else if (!IOS && isClient) {
//   //     type = 'client-android-app-version';
//   //   } else if (IOS && !isClient) {
//   //     type = 'provider-ios-app-version';
//   //   } else if (IOS && isClient) {
//   //     type = 'client-ios-app-version';
//   //   }
//   //   const liveVersion = settings.find((o) => o.key === type)?.value;
//   //   const isMandatory = Number(
//   //     settings.find((o) => o.key === 'app-update-mandatory')?.value,
//   //   );
//   //   const currentVersion = DeviceInfo.getVersion().toString();
//   //   const isUpdateAvailable =
//   //     liveVersion !== '0' && liveVersion !== currentVersion ? true : false;
//   //   // console.log('checkForAppUpdate ~ App type', type);
//   //   // console.log('checkForAppUpdate ~ liveVersion', liveVersion);
//   //   // console.log('checkForAppUpdate ~ currentVersion', currentVersion);
//   //   // console.log('checkForAppUpdate ~ isUpdateAvailable', isUpdateAvailable);
//   //   // console.log('checkForAppUpdate ~ isUpdateMandatory', isMandatory);

//   //   setUpdateMandatory(isMandatory);
//   //   if (isUpdateAvailable && !appUpdateModal) {
//   //     setAppUpdateModal(true);
//   //   } else {
//   //     setAppUpdateModal(false);
//   //   }

//   //   if (update) {
//   //     if (Config.IS_CLIENT === 'true') {
//   //       Linking.openURL(
//   //         IOS
//   //           ? 'https://itunes.apple.com/us/app/shaamel-provider/id1539128102?mt=8'
//   //           : 'http://play.google.com/store/apps/details?id=com.shaamel.provider',
//   //       );
//   //     } else {
//   //       Linking.openURL(
//   //         IOS
//   //           ? 'https://itunes.apple.com/us/app/shaamel/id1539127370?mt=8'
//   //           : 'http://play.google.com/store/apps/details?id=com.shaamel.client',
//   //       );
//   //     }
//   //   }
//   // }

//   // this function for check notification permission
//   async function checkNotificationPermission() {
//     const hasPermission = await messaging().hasPermission();
//     console.log("CALLED====3");
//     try {
//       const enabled =
//         hasPermission === messaging.AuthorizationStatus.AUTHORIZED ||
//         hasPermission === messaging.AuthorizationStatus.PROVISIONAL;
//       if (!enabled) {
//         const authorizationStatus = await messaging().requestPermission();
//         if (authorizationStatus === messaging.AuthorizationStatus.AUTHORIZED) {
//           // console.log('User has notification permissions enabled.');
//         } else if (
//           authorizationStatus === messaging.AuthorizationStatus.PROVISIONAL
//         ) {
//           // console.log('User has provisional notification permissions.');
//         } else {
//           // console.log('User has notification permissions disabled');
//         }
//       }
//       await messaging().registerDeviceForRemoteMessages(); // Register the device
//       if (!uuid && isObject(userData) && !isEmpty(userData)) {
//         getFcmToken();
//       }
//     } catch (error) {
//       sendErrorReport(error, "notification_permission");
//       console.log("checkApplicationPermission -> error", error);
//     }
//   }

//   // this function for send token to server
//   async function sendFcmToken(token) {
//     console.log("CALLED====4", accessToken);
//     const data = {
//       token,
//       platform: IOS ? "IOS" : "ANDROID",
//     };
//     try {
//       const response = await getApiData(
//         BaseSetting.endpoints.addToken,
//         "POST",
//         data,
//         {
//           "Content-Type": "application/json",
//           authorization: accessToken ? `Bearer ${accessToken}` : "",
//         }
//       );
//       console.log("fcm token send ==>", response);
//     } catch (err) {
//       console.log("ERRR==", err);
//       sendErrorReport(err, "send_fcm_token");
//     }
//   }

//   // this function for get firebase token
//   async function getFcmToken() {
//     console.log("get fcm calledd");
//     const fcmToken = await messaging()
//       .getAPNSToken()
//       .catch((e) => {
//         console.log("called====3Dddd", e);
//       });
//     const fcmToken1 = await messaging()
//       .getToken()
//       .catch((e) => {
//         console.log("called====3Dddd---", e);
//       });
//     console.log("fcm token", fcmToken1);
//     if (fcmToken && accessToken) {
//       sendFcmToken(fcmToken);
//       setFCMListeners();
//       dispatch(setUUid(fcmToken));
//     }
//   }

//   useEffect(() => {
//     const unsubscribe = messaging().onMessage(async (remoteMessage) => {
//       console.log("On message----", remoteMessage);
//       handleNotification(remoteMessage.data, true);
//     });

//     return unsubscribe;
//   }, []);

//   useEffect(() => {
//     const openNotification = messaging().onNotificationOpenedApp(
//       async (remoteMessage) => {
//         console.log(
//           "Notification caused app to open from background state:",
//           remoteMessage
//         );
//         if (isObject(remoteMessage.data) && !isEmpty(remoteMessage.data)) {
//           handleNotification(remoteMessage.data, false);
//         }
//       }
//     );

//     return openNotification;
//   }, []);

//   // this function set listeners for firebase
//   async function setFCMListeners() {
//     try {
//       const onTokenRefreshListener = messaging().onTokenRefresh((fcmToken) => {
//         if (fcmToken) {
//           console.log("setFCMListeners -> fcmToken", fcmToken);
//         }
//       });

//       console.log("onTokenRefreshListener ==", onTokenRefreshListener);

//       // try {
//       // messaging().onMessage(async (remoteMessage) => {
//       //   console.log(
//       //     remoteMessage,
//       //     'A new FCM message arrived!',
//       //     JSON.stringify(remoteMessage),
//       //   );
//       //   handleNotification(remoteMessage.data);
//       // });
//       // } catch (error) {
//       //   console.log('setFCMListeners -> error', error);
//       // }

//       // messaging().onNotificationOpenedApp(async (remoteMessage) => {
//       //   console.log(
//       //     'Notification caused app to open from background state:',
//       //     remoteMessage,
//       //   );
//       //   // handleNotification(remoteMessage, true);
//       // });

//       messaging()
//         .getInitialNotification()
//         .then(async (remoteMessage) => {
//           if (remoteMessage) {
//             console.log(
//               "Notification caused app to open from quit state:",
//               remoteMessage
//             );
//             // handleNotification(remoteMessage);
//           }
//         });
//     } catch (error) {
//       console.log("setFCMListeners -> error", error);
//       sendErrorReport(error, "fcm_listner");
//     }
//   }

//   // this function for handle notification
//   async function handleNotification(notificationData, bool) {
//     console.log("notification data=========", notificationData);
//     const nData =
//       isString(notificationData.objData) && !isEmpty(notificationData.objData)
//         ? JSON.parse(notificationData.objData)
//         : {};
//     if (
//       isObject(nData) &&
//       !isEmpty(nData) &&
//       (nData.campaign_type === "push_message" ||
//         notificationData.type === "test_post")
//     ) {
//       if (!postModal) {
//         setNotificationDetail(nData);
//         setPostModal(true);
//       }

//       if (nData.campaign_type === "push_message") {
//         addAction(nData, "viewed", accessToken);
//       }
//     } else {
//       setNotificationDetail(notificationData);
//     }
//     // condition for setting notification badge
//     // if (has(notificationData, 'badge') && Number(notificationData.badge) > 0) {
//     //   dispatch(setNotiBadge(notificationData.badge));
//     // }
//     props.showNotification({
//       title: notificationData.title || "Title",
//       message: notificationData.body || "Message",
//       onPress: () => {},
//     });
//   }

//   if (postModal) {
//     return (
//       <InAppModal
//         visible={postModal}
//         detail={notificationDetail}
//         campaignType={
//           notificationDetail?.campaign_type === "feed_post" ? "feed_post" : ""
//         }
//         button={notificationDetail?.button_info || []}
//         title={
//           !isEmpty(notificationDetail) ? notificationDetail?.text_info : {}
//         }
//         image={
//           !isEmpty(notificationDetail)
//             ? notificationDetail?.campaign_type === "feed_post"
//               ? notificationDetail.media_link
//               : notificationDetail?.post_file
//             : ""
//         }
//         position={
//           !isEmpty(notificationDetail)
//             ? notificationDetail?.message_position
//             : ""
//         }
//         onClose={() => {
//           setPostModal(false);
//           addAction(notificationDetail, "clicked", accessToken);
//         }}
//       />
//     );
//   }

//   return <View />;
// };

// PushNotification.propTypes = {};

// PushNotification.defaultProps = {};

// export default withInAppNotification(PushNotification);
/* eslint-disable no-nested-ternary */
/* eslint-disable quotes */
import React, { useEffect, useState } from "react";
import { Platform, View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { isObject, isEmpty, isString } from "lodash";
import messaging from "@react-native-firebase/messaging";
import { withInAppNotification } from "../../lib/react-native-in-app-notification";
import BaseSetting from "../../config/setting";
import AuthActions from "../../redux/reducers/auth/actions";
import { getApiData } from "../../utils/apiHelper";
import InAppModal from "../InAppModal";
import { addAction, sendErrorReport } from "../../utils/commonFunction";

const IOS = Platform.OS === "ios";
// const PushNotification = React.forwardRef((props, ref) => {
/**
 *
 *@module PushNotification
 *
 */
const PushNotification = (props) => {
  console.log("CALLED");
  const { navigation } = props;
  const { setUUid, setNotiCount } = AuthActions;
  // const { setNotiDetail, setNotiBadge, setCurrentTab } = commonActions;
  // const { setConfirmData } = jobActions;
  const dispatch = useDispatch();
  const { uuid, userData, accessToken } = useSelector((state) => state.auth);
  // const commonData = useSelector((state) => state.common);
  // const { settings } = useSelector((state) => state.common);
  // const [requestModal, setRequestModal] = useState(false);
  const [notificationDetail, setNotificationDetail] = useState({});
  const [postModal, setPostModal] = useState(false);
  // const [cancelRequestModal, setCancelRequestModal] = useState(false);
  // const [requestCancelPrice, setRequestCancelPrice] = useState(0);
  // const [appUpdateModal, setAppUpdateModal] = useState(false);
  // const [updateMandatory, setUpdateMandatory] = useState();
  // const appState = useAppStatus();
  // const [cAlert, setCAlert] = useState({
  //   showAlert: false,
  //   title: '',
  //   message: '',
  //   type: '',
  // });

  useEffect(() => {
    if (isEmpty(accessToken)) {
      const dontGo = ["SplashScreen", "Walkthrough", "RedirectLS"];
      const currentScreen = navigation.current?.getCurrentRoute().name;
      if (!dontGo.includes(currentScreen)) {
        navigation.current.navigate("RedirectLS");
      }
    }
  }, [accessToken]);

  useEffect(() => {
    if (isObject(userData) && !isEmpty(userData)) {
      checkNotificationPermission();
      console.log("checkNotificationPermission====2");
    }
  }, [props, userData, accessToken]);

  /** this function for get Badge Count
   * @function getBadgeCount
   * @param {object} data {}
   */
  async function getBadgeCount() {
    const headers = {
      "Content-Type": "application/json",
      authorization: accessToken ? `Bearer ${accessToken}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getUserAlertCount,
        "POST",
        {},
        headers
      );

      if (response.success) {
        dispatch(setNotiCount(response));
      } else {
        console.log(
          "🚀 ~ file: index.js ~ line 15ewe ~ getBadgeCount ~ response",
          response
        );
      }
    } catch (error) {
      console.log("error for device list ===", error);
      sendErrorReport(error, "get_device_list");
    }
  }

  // this function for check notification permission
  async function checkNotificationPermission() {
    const hasPermission = await messaging().hasPermission();
    console.log(
      "CALLED====3 hasPermission hasPermission hasPermission hasPermission hasPermission hasPermission hasPermission",
      hasPermission
    );
    try {
      const enabled =
        hasPermission === messaging.AuthorizationStatus.AUTHORIZED ||
        hasPermission === messaging.AuthorizationStatus.PROVISIONAL;

      if (!enabled) {
        console.log("CALLED====3----");
        const authorizationStatus = await messaging().requestPermission();
        if (authorizationStatus === messaging.AuthorizationStatus.AUTHORIZED) {
          console.log("User has notification permissions enabled.");
        } else if (
          authorizationStatus === messaging.AuthorizationStatus.PROVISIONAL
        ) {
          console.log("User has provisional notification permissions.");
        } else {
          console.log("User has notification permissions disabled");
        }
      }
      console.log("uuid====fcm", uuid);
      if (!uuid && isObject(userData) && !isEmpty(userData)) {
        setTimeout(() => {
          getFcmToken();
        }, 1000);
      }
    } catch (error) {
      sendErrorReport(error, "notification_permission");
      console.log("checkApplicationPermission -> error", error);
    }
  }

  // this function for send token to server
  /** this function for send token to server
   * @function sendFcmToken
   * @param {object} data {}
   */
  async function sendFcmToken(token) {
    console.log("CALLED====4", accessToken);
    const data = {
      token,
      platform: IOS ? "IOS" : "ANDROID",
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.addToken,
        "POST",
        data,
        {
          "Content-Type": "application/json",
          authorization: accessToken ? `Bearer ${accessToken}` : "",
        }
      );
      console.log("fcm token send ==>", response);
    } catch (err) {
      console.log("ERRR==", err);
      sendErrorReport(err, "send_fcm_token");
    }
  }
  // this function for get firebase token
  async function getFcmToken() {
    try {
      const fcmToken = await messaging().getToken();
      console.log("🚀 ~ getFcmToken ~ fcmToken:", fcmToken);

      if (fcmToken && accessToken) {
        sendFcmToken(fcmToken);
        setFCMListeners();
        dispatch(setUUid(fcmToken));
        console.log("✅ FCM Token received:", fcmToken);
      } else {
        console.log("⚠️ FCM token is null or missing accessToken.");
      }
    } catch (e) {
      console.log("❌ Error getting FCM token:", e);
    }
  }

  useEffect(() => {
    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      console.log("On message", remoteMessage);
      handleNotification(remoteMessage.data, true);
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    const openNotification = messaging().onNotificationOpenedApp(
      async (remoteMessage) => {
        if (isObject(remoteMessage?.data) && !isEmpty(remoteMessage?.data)) {
          handleNotification(remoteMessage?.data, false);
        }
      }
    );

    return openNotification;
  }, []);

  // this function set listeners for firebase
  async function setFCMListeners() {
    try {
      const onTokenRefreshListener = messaging().onTokenRefresh((fcmToken) => {
        if (fcmToken) {
          console.log("setFCMListeners -> fcmToken", fcmToken);
        }
      });

      console.log("onTokenRefreshListener ==", onTokenRefreshListener);

      messaging()
        .getInitialNotification()
        .then(async (remoteMessage) => {
          if (remoteMessage) {
            console.log(
              "Notification caused app to open from quit state:",
              remoteMessage
            );
            // handleNotification(remoteMessage);
          }
        });
    } catch (error) {
      console.log("setFCMListeners -> error", error);
      sendErrorReport(error, "fcm_listner");
    }
  }

  // this function for handle notification
  async function handleNotification(notificationData, bool) {
    const nData =
      isString(notificationData?.objData) && !isEmpty(notificationData?.objData)
        ? JSON.parse(notificationData?.objData) || notificationData?.objData
        : {};

    console.log("notification data------", notificationData, nData);

    setNotificationDetail(notificationData);

    if (
      isObject(nData) &&
      !isEmpty(nData) &&
      (nData.campaign_type === "push_message" ||
        notificationData.type === "test_post")
    ) {
      if (!postModal) {
        setNotificationDetail(nData);
        setPostModal(true);
      }

      if (nData.campaign_type === "push_message") {
        addAction(nData, "viewed", accessToken);
      }
    } else {
      setNotificationDetail(notificationData);
    }
    // condition for setting notification badge
    // if (has(notificationData, 'badge') && Number(notificationData.badge) > 0) {
    //   dispatch(setNotiBadge(notificationData.badge));
    // }
    props.showNotification({
      title: notificationData.title || "Title",
      message: notificationData.body || "Message",
      onPress: () => {},
    });
  }

  if (postModal) {
    return (
      <InAppModal
        visible={postModal}
        detail={notificationDetail}
        campaignType={
          notificationDetail?.campaign_type === "feed_post" ? "feed_post" : ""
        }
        button={notificationDetail?.button_info || []}
        title={
          !isEmpty(notificationDetail) ? notificationDetail?.text_info : {}
        }
        image={
          !isEmpty(notificationDetail)
            ? notificationDetail?.campaign_type === "feed_post"
              ? notificationDetail.media_link
              : notificationDetail?.post_file
            : ""
        }
        position={
          !isEmpty(notificationDetail)
            ? notificationDetail?.message_position
            : ""
        }
        onClose={() => {
          setPostModal(false);
          getBadgeCount();
          addAction(notificationDetail, "clicked", accessToken);
        }}
      />
    );
  }
  return <View />;
};

PushNotification.propTypes = {};

PushNotification.defaultProps = {};

export default withInAppNotification(PushNotification);
