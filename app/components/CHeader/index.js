import React from "react";
import { Image, Text, TouchableOpacity, View } from "react-native";
import { CustomIcon } from "../../config/LoadIcons";
import { FontFamily } from "../../config/typography";
import FAIcon from "react-native-vector-icons/FontAwesome";
import { useTheme } from "@react-navigation/native";
import SVGImg2 from "../../assets/images/cleanAirLogoWhite.svg";
import SVGBlck from "../../assets/images/cleanAirLogoWhite.svg";
const CHeader = (props) => {
  const {
    image,
    title,
    customLeftIcon,
    customRightIcon,
    leftIconName,
    rightIconName,
    onLeftPress,
    onRightPress,
    backBtn,
    leftBackColor,
    isViolateLogo,
  } = props;

  const colors = useTheme();
  const BaseColor = colors.colors;

  return (
    <View
      style={{
        justifyContent: "space-between",
        flexDirection: "row",
        alignItems: "center",
        height: 80,
        alignContent: "center",
        paddingTop: 48,
        paddingHorizontal: 16,
        marginBottom: 16,
      }}
    >
      <View
        style={{
          height: 40,
          width: 40,
          borderRadius: 20,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {leftIconName || backBtn ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onLeftPress}
            style={{
              backgroundColor: leftBackColor || BaseColor.blue,
              height: 40,
              width: 40,
              borderRadius: 20,
              justifyContent: "center",
              alignItems: "center",
              shadowColor: "#fff",
              shadowOffset: {
                width: 0,
                height: 4,
              },
              shadowOpacity: 0.27,
              shadowRadius: 4.65,

              elevation: 6,
            }}
          >
            {backBtn ? (
              <FAIcon
                name={"angle-left"}
                size={24}
                color={BaseColor.whiteColor}
              />
            ) : leftIconName ? (
              <CustomIcon
                name={leftIconName}
                size={18}
                color={BaseColor.whiteColor}
              />
            ) : null}
            {/* {leftIconName } */}
          </TouchableOpacity>
        ) : null}
      </View>
      <View
        style={{
          flex: 1,
          alignItems: "center",
        }}
      >
        {/* {image ? (
          <Image style={{ height: 50, width: "60%" }} source={image} />
        ) : (
          <Text
            style={{
              fontFamily: FontFamily.default,
              color: BaseColor.whiteColor,
              fontSize: 24,
              fontWeight: "bold",
              // paddingHorizontal: 24,
            }}
            numberOfLines={1}
          >
            {title}
          </Text>
        )} */}
        {image && isViolateLogo ? (
          <SVGBlck width={30} height={30} />
        ) : (
          image && <SVGImg2 width={30} height={30} />
        )}
      </View>

      <View
        style={{
          height: 40,
          width: 40,
          borderRadius: 20,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {rightIconName ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onRightPress}
            style={{
              backgroundColor: BaseColor.blue,
              height: 40,
              width: 40,
              borderRadius: 20,
              justifyContent: "center",
              alignItems: "center",
              shadowColor: "#fff",
              shadowOffset: {
                width: 0,
                height: 4,
              },
              shadowOpacity: 0.27,
              shadowRadius: 4.65,

              elevation: 6,
            }}
          >
            {rightIconName ? (
              <CustomIcon
                name={rightIconName}
                size={18}
                color={BaseColor.whiteColor}
              />
            ) : null}
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

export default CHeader;
