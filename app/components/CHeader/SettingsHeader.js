/* eslint-disable no-nested-ternary */
import React from "react";
import { Image, LogBox, Text, TouchableOpacity, View } from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome";
import { useTheme } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { SvgUri } from "react-native-svg";
import { CustomIcon } from "../../config/LoadIcons";
import { FontFamily } from "../../config/typography";
import SVGImg2 from "../../assets/images/cleanAirLogoWhite.svg";
import SVGBlck from "../../assets/images/cleanAirLogoWhite.svg";
import LinearGradient from "react-native-linear-gradient";

const SettingsHeader = (props) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    image,
    isViolateLogo,
    title,
    title2,
    customLeftIcon,
    customRightIcon,
    leftIconName,
    rightIconName,
    onLeftPress,
    onRightPress,
    backBtn,
    titleTextColor,
    headerBackgroundColor = BaseColor.cbtGradientColor,
    isRadius = true,
    home = false,
    otp = false,
    backTitle = "Back",
    chat,
    childInfo,
  } = props;

  const { notificationCount } = useSelector((state) => state.auth);

  return (
    <LinearGradient
      colors={["rgba(20, 135, 255, 12)", "rgba(41, 239, 196, 90)"]}
      useAngle={true}
      angle={138}
      style={{
        justifyContent: "space-between",
        flexDirection: "row",
        alignItems: "center",
        height: home ? 105 : chat ? 180 : 144,
        alignContent: "center",
        paddingTop: 48,
        paddingHorizontal: 20,
        borderBottomLeftRadius: isRadius ? 40 : 0,
        borderBottomRightRadius: isRadius ? 40 : 0,
      }}
    >
      {/* <View
      style={{
        backgroundColor: BaseColor.inputBackGroundColor,
        justifyContent: "space-between",
        flexDirection: "row",
        alignItems: "center",
        height: home ? 105 : chat ? 180 : 144,
        alignContent: "center",
        paddingTop: 48,
        paddingHorizontal: 20,
        borderBottomLeftRadius: isRadius ? 40 : 0,
        borderBottomRightRadius: isRadius ? 40 : 0,
      }}
    > */}
      <View
        style={{
          height: 40,
          width: 40,
          borderRadius: 20,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {leftIconName || backBtn ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onLeftPress}
            hitSlop={{ top: 20, bottom: 50, left: 20, right: 100 }}
            style={{
              marginStart: 28,
              borderColor:
                leftIconName === "left-arrow" ? null : BaseColor.textGrey,
            }}
          >
            {backBtn ? (
              <FAIcon name="angle-left" size={24} color={titleTextColor} />
            ) : leftIconName ? (
              <>
                {leftIconName === "settings-2" &&
                notificationCount.chat_count > 0 ? (
                  <View
                    style={{
                      position: "absolute",
                      backgroundColor: "red",
                      width: 18,
                      height: 18,
                      top: -3,
                      right: -3,
                      borderRadius: 10,
                      // alignItems: "center",
                      // justifyContent: "center",
                    }}
                  >
                    <Text
                      style={{
                        color: "#fff",
                        fontSize: 12,
                      }}
                      numberOfLines={1}
                    >
                      {notificationCount.chat_count}
                    </Text>
                  </View>
                ) : null}
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    marginTop: -40,
                    flex: 1,
                    minWidth: 70,
                  }}
                >
                  <CustomIcon
                    name={leftIconName}
                    size={leftIconName === "left-arrow" ? 12 : 18}
                    color={BaseColor.whiteColor}
                  />
                  {leftIconName === "left-arrow" ? (
                    <Text
                      style={{
                        fontSize: 12,
                        fontWeight: "700",
                        fontFamily: FontFamily.regular,
                        color: BaseColor.whiteColor,
                        marginLeft: 5,
                      }}
                    >
                      {backTitle}
                    </Text>
                  ) : null}
                </View>
              </>
            ) : null}
            {/* {leftIconName } */}
          </TouchableOpacity>
        ) : null}
      </View>
      <View style={{ flex: 1, alignItems: "center" }}>
        <View style={{ marginTop: home ? 10 : -10, marginBottom: 10 }}>
          {image && isViolateLogo ? (
            <SVGBlck width={30} height={30} />
          ) : (
            image && <SVGImg2 width={30} height={30} />
          )}
        </View>
        <View
          style={{
            flexDirection: chat ? "column" : "row",
            alignItems: chat ? "flex-start" : "center",
            alignSelf: "flex-start",
            marginLeft: -40,
          }}
        >
          {title2 && !otp && (
            <Text
              style={{
                fontFamily: FontFamily.default,
                color: titleTextColor,
                fontSize: 24,
                fontWeight: "400",
                textAlign: "left",
                marginTop: chat ? 25 : 0,
                // letterSpacing: 2,
                // paddingHorizontal: 24,
              }}
              numberOfLines={1}
            >
              {title2}
            </Text>
          )}
          <Text
            style={{
              fontFamily: FontFamily.bold,
              fontWeight: "700",
              color: titleTextColor,
              fontSize: 24,
              // letterSpacing: 2,
              // marginTop: -5,
              paddingHorizontal: title2 ? (chat ? 0 : 5) : childInfo ? 4 : 24,
              textAlign: "left",
            }}
            numberOfLines={1}
          >
            {title2 === "QR code" ? `/ ${title}` : title}
          </Text>
        </View>
      </View>

      <View
        style={{
          height: 40,
          width: 40,
          borderRadius: 20,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {rightIconName ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onRightPress}
            style={{
              backgroundColor: BaseColor.whiteColor,
              height: 40,
              width: 40,
              borderRadius: 20,
              justifyContent: "center",
              alignItems: "center",
              borderWidth: 0.5,
              borderColor: BaseColor.textGrey,
            }}
          >
            {rightIconName ? (
              <>
                {rightIconName === "notifications-bell-button" &&
                notificationCount.notification_count > 0 ? (
                  <View
                    style={{
                      position: "absolute",
                      backgroundColor: "red",
                      width: 18,
                      height: 18,
                      top: -3,
                      right: -3,
                      borderRadius: 10,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Text
                      style={{
                        color: "#fff",
                        fontSize: 12,
                      }}
                      numberOfLines={1}
                    >
                      {notificationCount.notification_count}
                    </Text>
                  </View>
                ) : null}
                <CustomIcon
                  name={rightIconName}
                  size={rightIconName === "clear" ? 24 : 18}
                  color={
                    rightIconName === "check"
                      ? BaseColor.blueDark
                      : BaseColor.blackColor
                  }
                />
              </>
            ) : null}
          </TouchableOpacity>
        ) : null}
      </View>
    </LinearGradient>
    // </View>
  );
};

export default SettingsHeader;
