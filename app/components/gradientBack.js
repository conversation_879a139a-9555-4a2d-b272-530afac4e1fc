import { useTheme } from "@react-navigation/native";
import React from "react";
import { Dimensions, View } from "react-native";
import LinearGradient from "react-native-linear-gradient";
import BaseColor from "../config/colors";

const GradientBack = (props) => {
  const colors = useTheme();
const {borderRadius} = props
  return (
    <LinearGradient
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
      colors={[colors.colors.blue, colors.colors.blueDark]}
      style={{
        paddingLeft: 15,
        paddingRight: 15,
        height: "100%",
        width: "100%",
        position: "absolute",
        borderBottomRightRadius: borderRadius || 0,
        borderBottomLeftRadius: borderRadius || 0,
      }}
    ></LinearGradient>
  );
};

export default GradientBack;
