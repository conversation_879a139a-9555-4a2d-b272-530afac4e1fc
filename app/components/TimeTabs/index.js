import * as React from "react";
import { Animated, Text, TouchableOpacity, View } from "react-native";
import _ from "lodash";
import BaseColor from "../../config/colors";

const TimeTabs = (props) => {
  const { tabs, onChangeIndex, tKey, selectedIndex, bgColor } = props;
  return (
    <View
      style={{
        borderRadius: 30,
        height: 48,
        backgroundColor: bgColor
          ? BaseColor.inputBackGroundColor
          : BaseColor.whiteColor,
        flexDirection: "row",
        justifyContent: "space-evenly",
        padding: 4,
      }}
    >
      {_.isArray(tabs) && tabs.length > 0
        ? tabs.map((tab, index) => (
            <TouchableOpacity
              activeOpacity={0.7}
              key={`top_item_${tKey}_${tab.id}`}
              onPress={
                onChangeIndex
                  ? () => {
                      onChangeIndex(index, tab);
                    }
                  : null
              }
              style={{ flex: 1 }}
            >
              <View
                style={{
                  backgroundColor:
                    selectedIndex === index
                      ? BaseColor.cbtGradientColor
                      : bgColor
                      ? BaseColor.inputBackGroundColor
                      : BaseColor.whiteColor,
                  borderRadius: 30,
                  alignContent: "center",
                  alignItems: "center",
                  justifyContent: "center",
                  flex: 1,
                }}
              >
                <Text
                  style={{
                    color:
                      selectedIndex === index
                        ? BaseColor.whiteColor
                        : BaseColor.blackColor,
                    fontSize: 16,
                    marginVertical: 9,
                    // marginHorizontal: 39,
                  }}
                >
                  {tab.title}
                </Text>
              </View>
            </TouchableOpacity>
          ))
        : null}
    </View>
  );
};

export default TimeTabs;
