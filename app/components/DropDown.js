import { useTheme } from "@react-navigation/native";
import { isEmpty } from "lodash";
import React, { useState } from "react";
import {
  Dimensions,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import LinearGradient from "react-native-linear-gradient";
import { SvgUri } from "react-native-svg";
import { CustomIcon } from "../config/LoadIcons";

const DropDown = (props) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    data,
    onSelect = () => {},
    selectedObject = {},
    placeholder = "Placeholder",
    valueProp,
    style,
    textStyle,
    lang,
    svgProp,
    backGround,
    backColor,
    textColor,
    selectColor,
    paddingStart,
  } = props;

  const [showList, setshowList] = useState(false);

  return (
    <>
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={["#0000", BaseColor.white20]}
        style={[
          {
            backgroundColor: backGround
              ? backColor
              : BaseColor.transparentWhite,
            borderRadius: 25,
            height: 50,
            flexDirection: "row",
            alignItems: "center",
            borderWidth: 1,
            borderColor: BaseColor.whiteColor,
          },
          style,
        ]}
      >
        <TouchableOpacity
          onPress={() => setshowList(true)}
          style={{
            height: "100%",
            alignItems: "center",
            flexDirection: "row",
            width: "100%",
            paddingStart: paddingStart || 24,
            paddingEnd: 16,
          }}
          activeOpacity={0.7}
        >
          {lang ? (
            <SvgUri
              width={24}
              height={24}
              uri={!isEmpty(selectedObject) ? selectedObject[svgProp] : null}
              style={{
                marginStart: 10,
              }}
            />
          ) : null}
          <Text
            style={{
              color: isEmpty(selectedObject)
                ? textColor
                : selectColor || BaseColor.whiteColor,
              flex: 1,
              ...textStyle,
              marginStart: lang ? 12 : null,
            }}
          >
            {isEmpty(selectedObject) ? placeholder : selectedObject[valueProp]}
          </Text>
          <CustomIcon
            name="expand-button"
            size={16}
            color={backGround ? textColor : BaseColor.whiteColor}
          />
        </TouchableOpacity>
      </LinearGradient>
      <Modal
        visible={showList}
        onRequestClose={() => setshowList(false)}
        transparent
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            setshowList(false);
          }}
          style={{
            flex: 1,
            backgroundColor: BaseColor.black40,
            justifyContent: "center",
          }}
        >
          <View
            style={{
              marginHorizontal: 24,
              backgroundColor: BaseColor.whiteColor,
              borderRadius: 12,
              paddingVertical: 12,
              maxHeight: Dimensions.get("window").height / 1.5,
            }}
          >
            <ScrollView>
              {data.map((item) => (
                <TouchableOpacity
                  activeOpacity={0.7}
                  style={{
                    backgroundColor: BaseColor.whiteColor,
                    padding: 8,
                  }}
                  onPress={() => {
                    onSelect(item);
                    setshowList(false);
                  }}
                >
                  <Text style={{ color: BaseColor.blackColor }}>
                    {item[valueProp]}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default DropDown;
