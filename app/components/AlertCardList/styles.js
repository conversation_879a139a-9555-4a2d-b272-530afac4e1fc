import { Dimensions, StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  cardView: {
    backgroundColor: BaseColor.whiteColor,
    marginVertical: 5,
    borderRadius: 10,
    marginHorizontal: 16,
  },
  icons: {
    width: 50,
    height: 50,
    backgroundColor: BaseColor.whiteColor,
    textAlignVertical: 'center',
    textAlign: 'center',
    borderRadius: 30,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    justifyContent: 'center',
    alignItems: 'center'
  },
  touchableContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 6,
    paddingVertical: 20,
    borderRadius: 10,
    marginHorizontal: 16,
  },
  title: {
    color: BaseColor.blackColor,
    fontSize: 18,
    fontFamily: FontFamily.default,
    fontWeight: 'bold',
  },
  time: {
    color: BaseColor.textGrey,
    fontSize: 12,
    paddingVertical: 2,
    width: Dimensions.get('screen').width / 2 - 60,
  },
  contentTextView: {
    paddingHorizontal: 10,
  },
  altMsg: {
    color: 'red',
    paddingTop: 5,
  },
  horizontalLine: {
    width: Dimensions.get('screen').width - 56,
    alignSelf: 'center',
    height: 1,
    backgroundColor: BaseColor.textGrey,
  },
  sendIcon: {
    marginRight: 16,
    width: 40,
    height: 40,
    textAlign: 'center',
    textAlignVertical: 'center',
    borderRadius: 40,
    backgroundColor: BaseColor.blueLight,
    justifyContent: 'center',
    alignItems: 'center'
  },
  textInputView: {
    marginVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
  },
  textInput: {
    flex: 1,
    marginLeft: 54,
    paddingHorizontal: 20,
  },
});

export default styles;
