/* eslint-disable quotes */
/* eslint-disable no-undef */
const devMode = __DEV__;
// 3463819424
// const baseUrl = devMode
//   ? "http://192.168.0.170:8090/"
//   : "https://api.chillbaby-test.io/";
const baseUrl = "https://api.chillbaby-test.io/";
// const baseUrl = "https://c8dc-47-189-197-48.ngrok-free.app/";
// const baseUrl = "https://3b55-47-161-216-133.ngrok-free.app/";
const BaseSetting = {
  name: "CBT SMART CAR",
  displayName: "CBT SMART CAR",
  appVersionCode: "1",
  // bugsnagApiKey: "97983f80d92e9c63fa56df79f1252515",
  baseUrl,
  socketUrl: baseUrl,
  api: `${baseUrl}api/`,
  // ? 'http://192.168.0.137/kashtah/kahstahApi/backend/v1/'
  // : `${baseUrl}/api/`,
  shareEndPoint: baseUrl,
  endpoints: {
    signUp: "user/signup",
    login: "user/login",
    childProfile: "user/add-child",
    gender: "user/update-gender",
    forgotPassword: "user/forgot-password",
    updatePassword: "user/update-password",
    otp: "user/check-otp",
    addToken: "user/add-token",
    deleteToken: "delete-token",
    getUserChild: "getUserChild",
    sendOtp: "user/send-otp",
    productList: "getProductDetails",
    sendPackage: "getTokenByPackage",
    sendNotifi: "sendUserNotification",
    getPost: "campaigns/get-post",
    uploadChatAttachment: "uploadChatAttachment",
    getAppChat: "chat/get-app-chat",
    insertAttachment: "insertAttachment",
    getLanguageList: "getLanguageList",
    getFeed: "campaigns/add-action",
    addUserProduct: "addUserProduct",
    updateChild: "user/update-child",
    getDevice: "brand_devices/get-device",
    connectedDevice: "getUserConnectedDevices",
    getFeedPost: "campaigns/get-feed-post",
    addAction: "campaigns/add-action",
    getAlerts: "alerts/get-alerts",
    addAlert: "alerts/add-alert",
    clearAlerts: "alerts/clear-alerts",
    notificationStatus: "user/change-notification-status",
    editDeviceChild: "editDeviceChild",
    deleteChildDevice: "deleteChildDevice",
    faqsList: "brand_faqs/get-list",
    getProductCategories: "getProductCategories",
    disconnectChildDevice: "disconnectChildDevice",
    setUserLogActiveTime: "setUserLogActiveTime",
    sendEmergencyMessageToUser: "sendEmergencyMessageToUser",
    saveDeviceDataInfo: "saveDeviceDataInfo",
    saveErrorLog: "saveErrorLog",
    addproductaction: "product/add-product-action",
    removeChild: "user/remove-child",
    deleteAccount: "user/delete-account",
    updateUser: "user/update-user",
    getUserAlertCount: "getUserAlertCount",
  },
};

export default BaseSetting;
