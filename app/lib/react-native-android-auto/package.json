{"name": "react-native-android-auto", "title": "React Native Android Auto", "version": "1.0.0", "description": "Android auto integration for React native apps", "main": "lib/index.js", "files": ["README.md", "android", "index.js", "ios", "react-native-android-auto.podspec"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc --build", "prepublish": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/github_account/react-native-android-auto.git", "baseUrl": "https://github.com/github_account/react-native-android-auto"}, "keywords": ["react-native"], "author": {"name": "Your Name", "email": "<EMAIL>"}, "license": "private", "licenseFilename": "LICENSE", "readmeFilename": "README.md", "peerDependencies": {"react": "~16.13.1", "react-native": ">=0.62.0 <1.0.x"}, "devDependencies": {"@types/lodash": "^4.14.165", "@types/react-native": "^0.63.37", "@types/react-reconciler": "^0.18.0", "react": "^16.9.0", "react-native": "^0.61.5", "typescript": "^4.1.2"}, "dependencies": {"lodash": "^4.17.20", "react-reconciler": "^0.26.1"}}