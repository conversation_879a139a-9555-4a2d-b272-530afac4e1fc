import { ImageSourcePropType } from 'react-native';
import { Template, TemplateConfig } from './Template';
export declare type NowPlayingButton = {
    id: string;
} & ({
    type: 'shuffle' | 'add-to-library' | 'more' | 'playback' | 'repeat';
} | {
    type: 'image';
    image: ImageSourcePropType;
});
export interface NowPlayingTemplateConfig extends TemplateConfig {
    albumArtistButtonEnabled?: boolean;
    upNextButtonTitle?: string;
    upNextButtonEnabled?: boolean;
    onAlbumArtistButtonPressed?(): void;
    onUpNextButtonPressed?(): void;
    onButtonPressed?(e: {
        id: string;
        templateId: string;
    }): void;
    buttons?: NowPlayingButton[];
}
export declare class NowPlayingTemplate extends Template<NowPlayingTemplateConfig> {
    get type(): string;
    get eventMap(): {
        albumArtistButtonPressed: string;
        upNextButtonPressed: string;
        buttonPressed: string;
    };
}
