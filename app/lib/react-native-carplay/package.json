{"name": "react-native-carplay", "version": "2.3.0", "description": "CarPlay for React Native", "main": "lib/index.js", "files": ["src", "lib", "ios", "react-native-carplay.podspec"], "podspecPath": "./react-native-carplay.podspec", "scripts": {"prepare": "yarn run build", "prettier": "prettier --write '**/*.{json,md,js,jsx,ts,tsx}'", "tslint": "tslint --fix 'src/**/*.{ts,tsx}' -p .", "build": "tsc", "build:docs": "typedoc --out docs src/index.ts", "build:dev": "tsc && cp -r lib example/node_modules/react-native-carplay", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/birkir/react-native-carplay.git"}, "keywords": ["react", "native", "carplay", "navigation", "car", "auto"], "author": "<PERSON><PERSON><PERSON> <birkir.gudjon<PERSON>@gmail.com>", "license": "MIT", "bugs": {"url": "https://github.com/birkir/react-native-carplay/issues"}, "homepage": "https://github.com/birkir/react-native-carplay#readme", "devDependencies": {"@types/node": "14.14.35", "@types/react-native": "0.63.52", "husky": "5.1.3", "lint-staged": "10.5.4", "prettier": "2.2.1", "rimraf": "3.0.2", "tslint": "6.1.3", "tslint-config-prettier": "1.18.0", "typedoc": "0.20.32", "typescript": "4.2.3"}, "dependencies": {"traverse": "^0.6.6"}}