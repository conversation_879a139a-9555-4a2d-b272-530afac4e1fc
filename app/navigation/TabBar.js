import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
} from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome";
import LinearGradient from "react-native-linear-gradient";
// import Animated from 'react-native-reanimated';
import { useTheme } from "@react-navigation/native";
import { CustomIcon } from "../config/LoadIcons";
import { FontFamily } from "../config/typography";
import Home from "../assets/svgs/home";
import { Settings } from "../assets/svgs/settings";
import { Dashboard } from "../assets/svgs/dashboard";

const TabBar = ({ state, descriptors, navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  useEffect(() => {
    setspring(state.index);
    setObject(state.routes[state.index]);
  }, [state]);

  const selectedIndex = state.index;
  // const selectedObject = state.routes[state.index];
  const [selectedObject, setObject] = useState(state.routes[state.index]);
  console.log("TabBar -> selectedIndex", selectedObject, selectedIndex);

  const [translateValue, setTranslateValue] = useState(new Animated.Value(0));

  const focusedOptions = descriptors[state.routes[state.index].key].options;

  if (focusedOptions.tabBarVisible === false) {
    return null;
  }

  const tabWidth = Dimensions.get("window").width - 32;
  const sTabWidth = tabWidth / 3;

  const setspring = (index) => {
    const tabWidth = sTabWidth;
    Animated.spring(translateValue, {
      toValue: 1 + index * tabWidth,
      velocity: 10,
      useNativeDriver: true,
    }).start();
  };

  return (
    <View
      style={{
        flexDirection: "row",
        width: tabWidth,
        // margin: 16,
        backgroundColor: "rgba(241, 241, 241, 0.3)", //"#00FFFFFF",
        justifyContent: "center",
        alignItems: "center",
        // borderTopEndRadius: 40,
        // borderBottomStartRadius: 40,
        borderRadius: 50,
        overflow: "hidden",
        shadowColor: "#000",
        shadowOffset: {
          width: 0,
          height: 6,
        },
        shadowOpacity: 0.37,
        shadowRadius: 7.49,
        elevation: 12,
        alignSelf: "center",
        marginBottom: 34,
      }}
    >
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
            ? options.title
            : route.name;

        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: "tabPress",
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: "tabLongPress",
            target: route.key,
          });
        };

        return (
          <View
            key={`tab_${label}`}
            style={{
              // backgroundColor: "rgba(241, 241, 241, 0.3)", //BaseColor.whiteColor,
              flex: 1,
              // borderRadius: 20,
              // borderBottomStartRadius: label === "DEVICES" ? 40 : 0,
              // borderTopEndRadius: label === "HOME" ? 40 : 0,
              width: sTabWidth,
            }}
          >
            <TouchableOpacity
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={() => {
                setObject(route);
                setspring(index);
                onPress();
              }}
              onLongPress={onLongPress}
              style={{
                flex: 1,
                height: 70,
                // backgroundColor: isFocused
                //   ? BaseColor.blueDark
                //   : BaseColor.whiteColor,
                // justifyContent: "space-evenly",
                // alignItems: "center",
                // borderBottomStartRadius: 40,
                // borderTopEndRadius: 40,

                // borderRadius: 40,
                overflow: "hidden",
              }}
            >
              <View
                style={{
                  flex: 1,
                  height: 70,
                  // backgroundColor: isFocused ? "#ededed" : BaseColor.whiteColor,
                  justifyContent: "space-evenly",
                  alignItems: "center",
                  overflow: "hidden",
                  // position: "absolute",
                }}
              >
                {label === "DEVICES" ? (
                  <Settings
                    height={22}
                    width={22}
                    color={isFocused ? "#00000001" : "#FFFFFF"}
                  />
                ) : label === "DASHBOARD" ? (
                  <Dashboard
                    height={22}
                    width={22}
                    color={isFocused ? "#00000001" : "#FFFFFF"}
                  />
                ) : (
                  <Home
                    height={22}
                    width={22}
                    color={isFocused ? "#00000001" : "#FFFFFF"}
                  />
                )}
                {/* <CustomIcon
                  name={
                    label === "DEVICES"
                      ? "swap"
                      : label === "DASHBOARD"
                      ? "user-3"
                      : "home"
                  }
                  size={20}
                  color={isFocused ? BaseColor.whiteColor : BaseColor.textGrey}
                /> */}
                {/* <Text
                  style={{
                    color: isFocused
                      ? BaseColor.whiteColor
                      : BaseColor.textGrey,
                    fontSize: 12,
                    fontFamily: FontFamily.default,
                    fontWeight: "bold",
                    letterSpacing: 1,
                  }}
                >
                  {label}
                </Text>
                {isFocused ? (
                  <FAIcon
                    name="circle"
                    size={8}
                    color={
                      isFocused ? BaseColor.whiteColor : BaseColor.textGrey
                    }
                  />
                ) : null} */}
              </View>
            </TouchableOpacity>
          </View>
        );
      })}
      <Animated.View
        style={{
          position: "absolute",
          left: -1,
          right: 0,
          bottom: 0,
          top: 0,
          alignItems: "center",
          justifyContent: "center",
          width: sTabWidth,
          // borderBottomStartRadius: 40,
          // borderTopEndRadius: 40,

          overflow: "hidden",
          transform: [{ translateX: translateValue }],
          elevation: 5,
          shadowColor: "#000",
          shadowOffset: {
            width: 2,
            height: 5,
          },
          shadowOpacity: 0.55,
          shadowRadius: 3.84,
        }}
      >
        <LinearGradient
          start={{ x: 0, y: 0.6 }}
          end={{ x: 0, y: 1 }}
          colors={["rgba(256, 256, 256, 0.9)", "rgba(256, 256, 256, 0.9)"]}
          style={{
            width: sTabWidth,
            height: 50,
            width: 50,
            borderRadius: 25,
            // backgroundColor: true ? BaseColor.whiteColor : BaseColor.whiteColor,
            justifyContent: "space-evenly",
            alignItems: "center",
            overflow: "hidden",
          }}
        >
          {selectedObject.name === "DEVICES" ? (
            <Settings height={22} width={22} color={"#B5B5B5"} />
          ) : selectedObject.name === "DASHBOARD" ? (
            <Dashboard height={22} width={22} color="#B5B5B5" />
          ) : (
            <Home height={22} width={22} color={"#B5B5B5"} />
          )}
          {/* <CustomIcon
            name={
              selectedObject.name === "DEVICES"
                ? "swap"
                : selectedObject.name === "DASHBOARD"
                ? "user-3"
                : "home"
            }
            size={20}
            color={true ? BaseColor.textGrey : BaseColor.whiteColor}
          /> */}
          {/* <Text
            style={{
              color: true ? BaseColor.whiteColor : BaseColor.textGrey,
              fontSize: 12,
              fontFamily: FontFamily.default,
              fontWeight: "bold",
              letterSpacing: 1,
            }}
          >
            {selectedObject.name}
          </Text> */}
          {/* {true ? (
            <FAIcon
              name="circle"
              size={8}
              color={true ? BaseColor.whiteColor : BaseColor.textGrey}
            />
          ) : null} */}
        </LinearGradient>
      </Animated.View>
    </View>
  );
};

export default TabBar;
