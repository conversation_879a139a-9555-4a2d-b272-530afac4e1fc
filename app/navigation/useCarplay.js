import { useEffect } from 'react';
import { CarPlay, GridTemplate } from 'react-native-carplay';
import { PERMISSIONS, requestMultiple } from 'react-native-permissions';
// import { showBasicMap } from '../utils/carPlay';
// import { isAndroid } from '../utils/device';
import { useSelector } from "react-redux";
import { NativeModules } from 'react-native';


const gridItemImage = require("../assets/images/three_front_correct.png");
const warning = require("../assets/images/warning.png");

// const getBasicMapConfig = () => ({
//   id: ,
//   component: ,
//   hidesButtonsWithNavigationBar: true,
//   leadingNavigationBarButtons: [
//     // define buttons that appear on the top left size of the map
//     // maximum 2 buttons possible
//   ],
//   trailingNavigationBarButtons: [
//     // define buttons that appear on the top right size of the map
//     // maximum 2 buttons possible
//   ],
//   mapButtons: [
//     // define buttons that appear on the bottom right size of the map
//     // maximum 4 buttons possible
//   ],
//   onMapButtonPressed: (e) => {
//     // define callback function after clicking map button
//   },
//   onBarButtonPressed:  (e) => {
//     // define callback function after clicking on trailing or leading buttons
//   },
// });

const showBasicMap = async () => {
  if (!CarPlay.connected) {
  console.log('CarPlay.NOTconnected CarPlay.NOTconnected CarPlay.NOTconnected CarPlay.NOTconnected CarPlay.NOTconnected')

    return;
  }

  console.log('CarPlay.connected CarPlay.connected CarPlay.connected CarPlay.connected CarPlay.connected')

  const noLogin = new GridTemplate({
    buttons: [
      {
        id: "List",
        titleVariants: [
          "Please Login from device in app,to experience carplay.",
        ],
        image: warning,
      },
    ],
    // onButtonPressed: ({ id }) => {
    //   navigation.navigate(id);
    // },
    onWillAppear: () => {
      navigation.navigate("Menu");
    },
    title: "SMART 360 IQ",
  });
  const gridTemplate = new GridTemplate({
    buttons: [
      {
        id: "List",
        titleVariants: ["Dashboardd"],
        image: gridItemImage,
      },

      // {
      //   id: "Search",
      //   titleVariants: ["Search"],
      //   image: gridItemImage,
      // },
      // {
      //   id: "Information",
      //   titleVariants: ["Information"],
      //   image: gridItemImage,
      // },

      // {
      //   id: "CarplayAlert",
      //   titleVariants: ["Alert"],
      //   image: gridItemImage,
      // },
      {
        id: "ActionSheet",
        titleVariants: ["ActionSheett"],
        image: gridItemImage,
      },
    ],
    onButtonPressed: ({ id }) => {
    //   navigation.navigate(id);
    },
    onWillAppear: () => {
    //   navigation.navigate("Menu");
    },
    title: "SMART 360 IQ",
  });

  CarPlay.setRootTemplate(true ? gridTemplate : noLogin);
//   const mapTemplate = new MapTemplate(getBasicMapConfig());
//   CarPlay.setRootTemplate(mapTemplate, true);
};


export const useCarPlay = () => {
    // const { CarPlayNotificationManager } = NativeModules;

    // function sendCarPlayNotification(title, body) {
    //     CarPlayNotificationManager.sendCarPlayNotification(title, body);
    //   }

    const onCarPlayConnect = async () => {
      await showBasicMap();
      // Initialize any events that you want to subscribe to on application side 
      // You can define listeners both on Phone and CarPlay side. 
      // Use listeners and CarPlay.emitter.emit({}) method to send messages between both layers
      // CarPlay.emitter.addListener(,,);
  // Function to trigger CarPlay notification
  
  //   console.log('CARPLAY NOTIFICATION NOTIFICATION NOTIFICATION NOTIFICATION NOTIFICATION')
    // Call this function when you want to send a CarPlay notification
  //   sendCarPlayNotification("CarPlay Alert", "Your custom notification message");
      // Request permissions here if you want to support voice control
      requestMultiple([
        PERMISSIONS.IOS.SPEECH_RECOGNITION,
        PERMISSIONS.IOS.MICROPHONE,
      ]);
    };


  const accessToken = useSelector((state) => state.auth.accessToken);

  useEffect(() => {


    console.log('BEFORE CARPLAY CONNECTSSSSSSSSSSSSSSssssss ', CarPlay)
    if (CarPlay) {
      CarPlay.registerOnConnect(onCarPlayConnect);
      CarPlay.registerOnDisconnect(() => {
        // Disable listeners on disconnect - they are not unmounted on disconnect
        CarPlay.emitter.removeAllListeners();
      });
    }


    return () => {
    // make sure to cleanup on closing the app
    if(CarPlay) {
      CarPlay.unregisterOnConnect(onCarPlayConnect);
      CarPlay.emitter.removeAllListeners();
    }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accessToken]);
};