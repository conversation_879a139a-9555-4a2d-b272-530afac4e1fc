{"text": "TEXT", "welcomeToBaby": "Welcome to BabyAuto", "functionalComfortable": "Functional and Comfortable, but above all state", "login": "<PERSON><PERSON>", "signup": "REGISTER", "loginscreen": "<PERSON><PERSON>", "loginUser": "EMAIL ID", "loginPassword": "PASSWORD", "loginRemember": "REMEMBER ME", "loginBtn": "LOGIN", "loginForgot": "FORGOT PASSWORD", "loginAccount": "Don't Have An Account?", "loginToSignup": "Sign up", "forgotScreen": "Forgot Password?", "forgotEmail": "Enter the Email address associated \nwith your account.", "forgotInput": "PHONE NUMBER", "forgotBtn": "RESET", "otpScreen": "WE SENT A CODE", "otpText": "Enter the code sent via SMS and email", "otpEmail": "Enter the OTP sent to associated Email", "otpBtn": "Submit", "otpResend": "Resent code", "genderScreen": "SELECT GENDER", "genderFemale": "Female", "genderMale": "Male", "qrScannerScreen": "DEVICES", "qrScannerText": "Using your phones camera, scan your\nunique QR code which can be found on your\nBaby Auto.", "qrScannerLink": "Click here if you are using non SMART product", "connectScreen": "Connected!", "connectSuccessText": "You Have Successfully connected The\nDevice", "childProfileScreen": "CHILD PROFILE", "chooseProfileText": "CHOOSE PROFILE", "addNew": "+ ADD NEW", "tapToAddText": "Tap To Add New Profile", "infoTitle": "INFO", "nickName": "NICKNAME", "age": "AGE", "fewWordInputText": "FEW WORDS ABOUT ME", "height": "HEIGHT (cms)", "weight": "WEIGHT (kgs) ", "devices": "DEVICES", "dashboard": "DASHBOARD", "home": "HOME", "deviceThreeText": "1 Device Connected", "activeDevice": "Active", "addNewDevice": "Add a new device", "deactivedDevice": "Deactivated", "tapToAddNewProfile": " Add a New child Profile", "homeAddSmartDevice": " Add SMART Device", "homeAlertMsg": "The car seat is loose or not connected properly, Please ensure the safety before continuing journey", "homeAlertTitle": "<PERSON> Seat Alert", "alertOkBtn": "OK", "alertCancelBtn": "CANCEL", "homeVideoGuideText": "Top tip for installing a car seat", "settingScreen": "SETTINGS", "tempText": "Temperature ", "pushText": "Push Notification", "darkmodeText": "Dark Mode", "customerText": "Customer Service", "tempC": "C", "tempF": "F", "modeOn": "On", "modeOff": "Off", "about": "About", "support": "FAQ'S & Support", "version": "VERSION 1.0", "alertScreen": "ALERTS", "tempAlert": "High Temperature Alert", "tempAlertText": "11 Min <PERSON><PERSON>", "strollerAlert": "<PERSON>'s Stroller", "strollerText": "Today, 11:22PM", "strollerMsg": "<PERSON>", "babyInSeatAlert": "Baby In Seat Alert", "babyInSeatTime": "2Hr Ago", "babyOutOfSeatAlert": "<PERSON> Out of Seat", "babyOutOfSeatTime": "2Hr Ago", "listenIntoAlert": "Listen Into The Alert", "connectedDeviceName": "<PERSON><PERSON>", "charlie": "<PERSON>", "smartcushion": "SMART cushion", "chatScreenTitle": "<PERSON> - Smart Car", "chatInputText": "Type An Answer", "fullName": "FULL NAME", "updatePasswordScreen": "UPDATE PASSWORD", "newPassword": "New Password", "confirmPassword": "Confirm Password", "logout": "Log out", "emailId": "EMAIL", "agreeTo": "AGREE TO", "termNCondition": "TERMS & CONDITION", "whatsNew": "WHAT'S NEW", "dob": "BIRTHDAY", "faqScreen": "FAQ'S & Support", "selectChild": "Select Child", "deleteDevice": "DELETE DEVICE", "changeDevice": "CHANGE DEVICE", "editDevice": "EDIT DEVICE", "children": "CHILDREN", "products": "Products", "productDetail": "Product Detail", "technicalDrawing": "Product video", "faqNManuals": "FAQ & Manuals", "agree": "AGREE", "ticketClosed": "Your ticket has been closed by service provider", "selectLang": "Select Language", "temperatureAlertTitle": "Enter Low & High Temperature Alert Value", "emergencyAlertTitle": "Emergency Alert", "emergencyAlertMessage": "Your child is still in their seat, please ensure they are with a responsible adult", "noAlerts": "No Alerts", "highAlertMsg": "The temperature is high around your child, please ensure they are safe and comfortable.", "lowTempAlertMsg": "The temperature is low around your child, please ensure they are safe and comfortable.", "tempAlertTitle": "Temperature alert", "batteryAlertTitle": "Battery alert", "batteryLowMsg": "Battery is lower then 25%", "noPosts": "No Posts", "noDevice": "No Devices", "reset": "RESET", "mydevice": "MY DEVICES", "goodmorning": "Good Morning!", "goodafternoon": "Good Afternoon!", "goodevening": "Good Evening", "goodnight": "Good Night", "enterusername": "Please enter mobile/username", "enterpassword": "Please enter password", "enterfullname": "Please enter fullname", "entervalidpassword": "Password must contain 8-15 characters, 1 x Upper case, 1 x Lower case, 1 x number and 1 x special character such as !,?,&", "enteremail": "Please enter email", "entervalidemail": "Please enter valid email", "enterphone": "Please enter phone number", "entervalidphone": "Please enter valid phone number", "accepttermsandcondition": "Please accept Terms and Conditions", "enternickname": "Please enter nickname", "selectdob": "Please select DOB", "selectheight": "Please select height", "selectweight": "Please select Weight", "addprofilepicture": "Please add profile picture", "deviceconnected": "Device Connected", "alertlogout": "LOGOUT", "youwanttologout": "Are you sure you want to logout?", "nofaqfound": "No FAQ found", "urlError": "Requested URL is not valid", "powerDownTitle": "Power Down", "powerDownMessage": "Powering down", "productDesc": "Product Description", "addDeviceTitle": "ADD DEVICE", "scanQRText": "Using your phones camera. Scan your unique QR code which can be found on your Smart Car.", "qrLinkText": "Click here if you are using non SMART product", "childLeftInSeatTitle": "Child In Seat", "childLeftInSeatMessage": "Child is left in car seat", "changeSeatToForward": "Due to your child's weight you've entered, please set the child seat to forward facing position", "changeSeatToRearFace": "Due to your child's weight you've entered, please set the child seat to rear facing position", "fanOn": "Fan is on", "fanOff": "Fan is off", "fanTitle": "Fan", "tempTitle": "TEMPERATURE", "humidityTitle": "HUMIDITY", "carSeatText": "CAR SEAT", "noProducts": "No Products", "contactname": "CONTACT NAME", "contactnumber": "CONTACT NUMBER", "emergencycontact": "EMERGENCY CONTACT", "turnOnBle": "Please turn on your bluetooth", "leftSeatTitle": "<PERSON> has left seat", "leftSeatMessage": "The child is out of seat and the system will power down in 30 seconds", "sendSMS": "Send Emergency SMS", "responsibleAdult": "With Responsible Adult", "productConnected": "CBT seat connected. {{child_name}} in Seat", "noCameraAcces": "No access to camera. To scan code allow us to use your camera in Settings > Privacy > Camera", "openSettings": "Tap to open Settings", "notEnough": "{{child_name}} is not yet big enough to travel forward facing, please set the car seat in rear facing mode", "recommended": "It is recommended you use the top tether for {{child_name}}’s car seat", "rearFacing": "{{child_name}} is now too big to travel rear facing, please set the car seat in front facing mode", "notInstall": "{{child_name}} car seat is not installed correctly please resolve the issues shown on screen", "moveHead": "Please move {{child_name}}’s head rest up to the next position", "downHead": "Please move {{child_name}}’s head rest down to the next position", "deleteProfile": "Delete Profile", "editProfile": "Edit Profile", "correctRearFacing": "Congratulations, you have set up {{child_name}} car seat correctly for Rear facing! you are now ready for travel", "correctFrontFacing": "Congratulations, you have set up {{child_name}} car seat correctly for Front facing! you are now ready for travel", "boosterMode": "Congratulations, you have set up {{child_name}} car seat correctly in Booster mode! you are now ready for travel", "reclineAlert": "Please recline {{child_name}}’s seat for travel in rear facing mode", "frontUprightPosition": "Please place {{child_name}}’s seat in the upright position for travel in front facing mode", "boosterUprightPosition": "Please place {{child_name}}’s seat in the upright position for travel in Booster mode", "assignDevice": "ASSIGN DEVICE", "introTitle1": "Welcome", "introTitle2": "Connect your Smart Car", "introTitle3": "Link your device to your child profile", "introTitle4": "For anyone caring for your child", "introText11": "Main purpose of the app goes here. For example, monitor your CBT SMART CAR smart devices or check on the air quality your baby is breathing.", "introText12": "Let us help you to get set up.", "introText13": "", "introText14": "Turn on your Smart Car device and make sure your phone bluetooth is on. In the next step you will be able to link the device to this app.", "introText21": "During set-up you will need to locate", "introText22": "your personal QR code to scan with", "introText23": "you phone which will link to your", "introText24": "new CBT SMART CAR smart device.", "introText31": "You can use your CBT SMART CAR app to", "introText32": "contact our customer service team and", "introText33": "even discover our latest news.", "oneLowerCase": "At least one lower case letter", "oneUpperCase": "At least one upper case letter", "oneNumber": "At least one number", "oneSp": "At least one special character", "twLong": "At least twelve character", "pswdReq": "Password requirements:", "allCleanAir": "All Smart Car", "smartcar": "Smart Car", "myFamilyProfiles": "My family profiles", "MyQRcode": "QR code", "shareMyDevices": "share my devices", "settingsText": "Settings", "MyAccount": "My account", "userManual": "User manual", "resetYour": "Reset your", "otpWeHaveSent": "We've sent you a code", "createNewProfile": "Create a new child profile", "whyNecessary": "Why is this necessary?", "uploadPic": "Upload picture", "tempNotifyText": "Notify me and automatically turn on CleanAir fan when the outside temperature is over:"}