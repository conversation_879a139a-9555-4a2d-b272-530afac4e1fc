/* eslint-disable global-require */
/* eslint-disable quotes */
import i18n from "i18n-js";
// import RNRestart from "react-native-restart";
import actions from "../redux/reducers/language/actions";
import { sendErrorReport } from "../utils/commonFunction";
// import { store } from '../redux/store/configureStore';

const translationGetters = {
  en: () => require("./en.json"),
  fr: () => require("./fr.json"),
  zh: () => require("./zh.json"),
  ger: () => require("./ger.json"),
  por: () => require("./por.json"),
  es: () => require("./es.json"),
  ru: () => require("./ru.json"),
};

export const translate = (key, config) => {
  if (!config) {
    config = {};
  }
  config.defaultValue = key;
  return i18n.t(key, config);
};

const setI18nConfig = (language, store, bool, lang) => {
  const isRTL = false;
  let appLanguage = language;
  if (language === null) {
    appLanguage = "en";
    store.dispatch({
      type: actions.SET_LANGUAGE,
      languageData: appLanguage,
    });
  }

  // if (appLanguage === 'ar') {
  //   isRTL = true;
  // }

  console.log("appLanguage", appLanguage);
  console.log("isRTL", isRTL);

  const ReactNative = require("react-native");
  try {
    ReactNative.I18nManager.allowRTL(isRTL);
    ReactNative.I18nManager.forceRTL(isRTL);
  } catch (e) {
    console.log("Error in RTL", e);
    sendErrorReport(e, "setI18nConfig");
  }
  i18n.translations = { [appLanguage]: translationGetters[appLanguage]() };
  i18n.locale = appLanguage;
  store.dispatch({
    type: actions.UPDATE_LANGUAGE,
    updateLanguage: !lang,
  });
  // if (bool) {
  //   setTimeout(() => {
  //     RNRestart.Restart();
  //   }, 1000);
  // }
};

export const initTranslate = (store, bool = false) => {
  const {
    language: { languageData, updateLanguage },
  } = store.getState();
  setI18nConfig(languageData, store, bool, updateLanguage);
};
