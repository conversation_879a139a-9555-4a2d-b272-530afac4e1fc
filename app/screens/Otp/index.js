/* eslint-disable quotes */
/* eslint-disable no-unused-expressions */
import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  BackHandler,
  ActivityIndicator,
} from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import LinearGradient from "react-native-linear-gradient";
import FIcon from "react-native-vector-icons/Feather";
import Toast from "react-native-simple-toast";
import { useDispatch, useSelector } from "react-redux";
import { useTheme } from "@react-navigation/native";
import styles from "./styles";
import CButton from "../../components/CButton";
import OtpComponent from "../../components/OtpComponent/index";
import { CustomIcon } from "../../config/LoadIcons";
import GradientBack from "../../components/gradientBack";
import { translate } from "../../lang/Translate";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import AuthAction from "../../redux/reducers/auth/actions";
import { sendErrorReport } from "../../utils/commonFunction";
import SettingsHeader from "../../components/CHeader/SettingsHeader";
import { FontFamily } from "../../config/typography";
import SVGRedCircle from "../../assets/images/redCircle.svg";
import SVGRedCross from "../../assets/images/redCross.svg";

let backPressed = 0;

function Otp({ route, navigation }) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const { setUserId, setAccessToken, setUserData } = AuthAction;
  const dispatch = useDispatch();

  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);

  const [numPad, setNumPad] = useState(false);
  const [code, setCode] = useState("");
  const { type, userId } = route.params;
  const [otpLoader, setOtpLoader] = useState(false);
  const [isError, setIsError] = useState(false);
  const user_id = useSelector((state) => state.auth.user_id);

  const validation = () => {
    if (code === "") {
      Toast.show("Enter One Time Password");
    } else {
      otpCheck();
    }
  };

  const otpCheck = () => {
    setloader(true);
    setanim(true);
    setBackAnim(false);
    const data = {
      user_id: userId || user_id,
      otp_code: Number(code),
    };

    getApiData(BaseSetting.endpoints.otp, "POST", data)
      .then((response) => {
        if (response.success) {
          if (type !== "ForgotPassword") {
            dispatch(setUserData(response.data.user));
            dispatch(setUserId(response.data.user.id));
            dispatch(setAccessToken(response.data.token));
          }
          setTimeout(() => {
            setloader(false);
            setdone(true);
          }, 2000);
          setTimeout(() => {
            if (type === "ForgotPassword") {
              navigation.push("UpdatePassword");
            } else if (type === "Signup") {
              navigation.push("ChildInfo", { type: "Otp" });
            } else if (type === "LoginInactive") {
              navigation.push("Login");
            }
          }, 3000);
        } else {
          Toast.show(response.message);
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch((err) => {
        Toast.show("Something went wrong while verifying otp");
        sendErrorReport(err, "verify_otp");
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log("ERRR", err);
      });
  };

  // this function for resend otp
  async function resendOtp() {
    setOtpLoader(true);
    try {
      const response = await getApiData(BaseSetting.endpoints.sendOtp, "POST", {
        user_id: userId || user_id,
      });
      Toast.show(response.message);
      setOtpLoader(false);
    } catch (error) {
      console.log("resend otp error ===", error);
      sendErrorReport(error, "resend_otp");
    }
  }

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show("Press Again To Exit");
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  return (
    <>
      <View style={{ flex: 1, backgroundColor: BaseColor.whiteColor }}>
        <SettingsHeader
          image
          title2={type === "Signup" ? translate("otp") : translate("resetYour")}
          title={
            type === "Signup" ? translate("passcode") : translate("password")
          }
          backTitle={type === "Signup" ? "Back" : "Login"}
          leftIconName="left-arrow"
          onLeftPress={() => {
            navigation.navigate("RedirectLS");
          }}
          titleTextColor={BaseColor.whiteColor}
          headerBackgroundColor={BaseColor.cbtGradientColor}
        />
        {/* <GradientBack /> */}
        <View style={styles.mainContainer}>
          <View>
            <Text
              style={[styles.associatedTest, { color: BaseColor.blackColor }]}
            >
              {translate("otpWeHaveSent")}
            </Text>
            <Text
              style={[
                styles.associatedTest,
                {
                  fontSize: 16,
                  fontFamily: FontFamily.regular,
                  marginBottom: 0,
                  lineHeight: 24,
                  color: BaseColor.blackColor,
                  marginTop: 15,
                },
              ]}
            >
              {translate("otpText")}
            </Text>
          </View>
          <View style={styles.inputWrapper}>
            <OtpComponent
              code={code}
              onCodeFilled={(cd) => {
                // codeFilled("checkOtp", cd);
                console.log("checkOtp", cd);
              }}
              onCodeChanged={(val) => {}}
              onotpPress={() => {
                setNumPad(true);
              }}
            />
            {isError && (
              <View style={{ flexDirection: "row" }}>
                <View>
                  <SVGRedCircle width={20} height={20} />
                  <View style={{ position: "absolute", top: 6, left: 6 }}>
                    <SVGRedCross width={8} height={8} />
                  </View>
                </View>
                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: FontFamily.regular,
                    fontWeight: "400",
                    color: "#D25F5F",
                    paddingLeft: 10,
                  }}
                >
                  {translate("fillInTheCode")}
                </Text>
              </View>
            )}
          </View>
          <View style={styles.btnCon}>
            <CButton
              style={[
                styles.loginButton,
                { backgroundColor: BaseColor.blackColor },
              ]}
              titleStyle={{ color: BaseColor.whiteColor }}
              title={translate("otpBtn")}
              anim
              playAnimation={anim}
              backAnim={backAnim}
              onPress={() => {
                validation();
              }}
              loader={loader}
              done={done}
            />
          </View>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles.resendView}
            onPress={otpLoader ? null : resendOtp}
          >
            {otpLoader ? (
              <ActivityIndicator color={BaseColor.blackColor} />
            ) : (
              <>
                <Text
                  style={[(styles.resendText, { color: BaseColor.blackColor })]}
                >
                  {translate("otpResend")}
                </Text>
                <CustomIcon
                  name="reload"
                  size={16}
                  style={{ marginLeft: 10 }}
                  color={BaseColor.blackColor}
                />
              </>
            )}
          </TouchableOpacity>
          {/* <View style={styles.loginTextView}>
            <View>
              <View
                style={[
                  styles.lockIconStyle,
                  { backgroundColor: BaseColor.whiteColor },
                ]}
              >
                <MaterialCommunityIcons
                  name="key-outline"
                  color={BaseColor.blueDark}
                  size={40}
                />
              </View>
              <Text style={[styles.loginText, { color: BaseColor.whiteColor }]}>
                {translate("otpScreen")}
              </Text>
            </View>
            <Text
              style={[styles.associatedTest, { color: BaseColor.whiteColor }]}
            >
              {translate("otpText")}
            </Text>
          </View> */}
          {/* <View style={styles.inputWrapper}>
            <OtpComponent
              code={code}
              onCodeFilled={(cd) => {
                // codeFilled("checkOtp", cd);
                console.log("checkOtp", cd);
              }}
              onCodeChanged={(val) => {}}
              onotpPress={() => {
                setNumPad(true);
              }}
            />
          </View> */}
          {/* <View style={styles.inputWrapper}>
            <CButton
              style={styles.loginBtn}
              title={translate("otpBtn")}
              anim
              playAnimation={anim}
              backAnim={backAnim}
              onPress={() => {
                validation();
              }}
              loader={loader}
              done={done}
            />
          </View> */}
          {/* <TouchableOpacity
            activeOpacity={0.8}
            style={styles.resendView}
            onPress={otpLoader ? null : resendOtp}
          >
            {otpLoader ? (
              <ActivityIndicator color={BaseColor.whiteColor} />
            ) : (
              <>
                <CustomIcon
                  name="reload"
                  size={16}
                  color={BaseColor.whiteColor}
                />
                <Text
                  style={[styles.resendText, { color: BaseColor.whiteColor }]}
                >
                  {translate("otpResend")}
                </Text>
              </>
            )}
          </TouchableOpacity> */}
        </View>
      </View>
      <Modal
        visible={numPad}
        onRequestClose={() => {
          setNumPad(false);
        }}
        animationType="slide"
        transparent
      >
        <TouchableOpacity
          style={{
            backgroundColor: "transparent",
            flex: 1,
            justifyContent: "flex-end",
          }}
          onPress={() => {
            setNumPad(false);
          }}
        >
          <LinearGradient
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            colors={[BaseColor.blueLight, BaseColor.blueDark]}
            style={{
              paddingLeft: 15,
              paddingRight: 15,
              borderRadius: 5,
            }}
          >
            <View>
              <View
                style={{
                  flexDirection: "row",
                }}
              >
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 1);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    1
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 2);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    2
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 3);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    3
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  flexDirection: "row",
                }}
              >
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 4);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    4
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 5);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    5
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 6);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    6
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  flexDirection: "row",
                }}
              >
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 7);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    7
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 8);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    8
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 9);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    9
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  flexDirection: "row",
                }}
              >
                <TouchableOpacity style={styles.numPad}>
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 0);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.numTxtStyle,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    0
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    const pCode = code;
                    const nCode = pCode.slice(0, -1);
                    setCode(nCode);
                  }}
                >
                  <FIcon name="delete" size={24} color={BaseColor.whiteColor} />
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        </TouchableOpacity>

        {/* <View
          style={{
            backgroundColor: "transparent",
            flex: 1,
            justifyContent: "flex-end",
          }}
        >
          <View>
            <View
              style={{
                backgroundColor: BaseColor.blueDark,
                flexDirection: "row",
              }}
            >
              <TouchableOpacity style={styles.numPad}>
                <Text style={styles.numTxtStyle}>1</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.numPad}>
                <Text style={styles.numTxtStyle}>2</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.numPad}>
                <Text style={styles.numTxtStyle}>3</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View> */}
      </Modal>
    </>
  );
}

export default Otp;
