import { Dimensions, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";

const { height: dHeight, width: dWidth } = Dimensions.get("window");

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.red,
  },
  mainContainer: {
    // flex: 1,
    // backgroundColor: BaseColor.blueDark,
    justifyContent: "flex-start",
    alignItems: "flex-start",
    marginTop: 25,
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: "flex-end",
  },
  btnCon: {
    width: dWidth * 0.87,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
  },

  loginButton: {
    height: 65,
    alignSelf: "center",
    fontFamily: FontFamily.default,
    borderColor: BaseColor.blackColor,
    borderRadius: 26,
    backgroundColor: BaseColor.blackColor,
    marginVertical: 7,
    marginHorizontal: 20,
  },
  mainInputStyle: {
    // flex: 1,
    justifyContent: "center",
  },
  inputWrapper: {
    marginVertical: 7,
    marginHorizontal: 20,
    width: dWidth,
  },
  loginText: {
    fontSize: 20,
    color: BaseColor.whiteColor,
    fontWeight: "700",
    letterSpacing: 0.8,
    fontFamily: FontFamily.default,
  },
  loginTextView: {
    marginTop: 40,
    marginBottom: 20,
    alignItems: "center",
    fontFamily: FontFamily.default,
  },
  associatedTest: {
    fontFamily: FontFamily.default,
    color: BaseColor.blackColor,
    fontSize: 20,
    textAlign: "left",
    marginHorizontal: 25,
  },
  lockIconStyle: {
    backgroundColor: BaseColor.whiteColor,
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    width: 80,
    height: 80,
    borderRadius: 40,
    elevation: 5,
    marginBottom: 20,
  },
  resendView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    marginVertical: 20,
  },
  resendText: {
    fontSize: 16,
    fontFamily: FontFamily.default,
    color: BaseColor.blackColor,
    fontWeight: "bold",
    paddingHorizontal: 8,
  },
  numPad: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    height: 60,
  },
  numTxtStyle: {
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.default,
    fontSize: 24,
  },
});

export default styles;
