/* eslint-disable quotes */
import React, { useEffect, useState } from "react";
import { FlatList, View, BackHandler, Text, Alert } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";
import styles from "./styles";
import CHeader from "../../components/CHeader";
import DeviceList from "../../components/ConDeviceList";
import GradientBack from "../../components/gradientBack";
import { translate } from "../../lang/Translate";
import CButton from "../../components/CButton";
import CAlert from "../../components/CAlert";
import BaseColor from "../../config/colors";
import { useSelector } from "react-redux";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import { sendErrorReport } from "../../utils/commonFunction";
import Toast from "react-native-simple-toast";

// const device = [
//   {
//     id: 1,
//     img: require("../../assets/images/13.jpg"),
//     title: translate("charlie"),
//     text: translate("smartcushion"),
//   },
//   {
//     id: 2,
//     img: require("../../assets/images/14.jpg"),
//     title: translate("charlie"),
//     text: translate("smartcushion"),
//   },
//   {
//     id: 2,
//     img: require("../../assets/images/14.jpg"),
//     title: translate("charlie"),
//     text: translate("smartcushion"),
//   },
// ];

const CDeviceList = ({ navigation, route }) => {
  const {
    params: { deviceDetail, title, item },
  } = route;
  const listAnim = useSharedValue(0);
  const opacityAnim = useSharedValue(0);

  const [isDeleting, setIsDeleting] = useState(false);

  const [btnLoad, setBtnLoad] = useState("");
  const token = useSelector((state) => state.auth.accessToken);

  const listStyleAnim = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(listAnim.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(opacityAnim.value, {
      duration: 1000,
    }),
  }));

  useEffect(() => {
    opacityAnim.value = 1;
    listAnim.value = -300;
  }, []);

  const render = ({ item }) => (
    <DeviceList
      img={item.device_image}
      title={item.device_name}
      text={item.text}
      onEditPress={() => {
        navigation.navigate("EditDevice", { deviceDetail: item });
      }}
    />
  );
  const ConfirmdeleteDevice = () => {
    setIsDeleting(true);
    // UPGRADE REQUIRED FOR THIS
    // Alert.alert(
    //   "Delete profile",
    //   "Are you sure you want to delete this profile",
    //   [
    //     {
    //       text: "No",
    //       onPress: () => console.log("Cancel Pressed"),
    //       style: "cancel",
    //     },
    //     { text: "Yes", onPress: () => deleteDevice() },
    //   ]
    // );
  };

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  async function deleteDevice() {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    const data = {
      child_id: item?.id,
    };

    setBtnLoad("delete");

    try {
      const response = await getApiData(
        BaseSetting.endpoints.removeChild,
        "POST",
        data,
        headers
      );
      console.log(
        "🚀 ~ file: index.js ~ line 116 ~ deleteDevice ~ response",
        response
      );

      Toast.show(response.message);
      setBtnLoad("");
      navigation.navigate("DrawerNav");
    } catch (error) {
      console.log("error ===", error);
      setBtnLoad("");
      sendErrorReport(error, "delete_device");
    }
  }

  return (
    <View style={styles.root}>
      <GradientBack />
      <CHeader
        title={title || ""}
        backBtn
        leftIconName
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <Animated.View style={[styles.flatlistView, { top: 300 }, listStyleAnim]}>
        <FlatList
          data={deviceDetail || []}
          renderItem={render}
          keyExtractor={(item) => {
            item.id;
          }}
          bounces={false}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={() => {
            if (deviceDetail.length === 0) {
              return (
                <View style={styles.emptyView}>
                  <Text style={styles.emptyDataText}>
                    {translate("noDevice")}
                  </Text>
                </View>
              );
            }
            return null;
          }}
        />
        <View
          style={{ flex: 1, justifyContent: "flex-end", marginHorizontal: 16 }}
        >
          <View style={{ flexDirection: "row", marginBottom: 16 }}>
            <CButton
              title={translate("editProfile")}
              style={{
                backgroundColor: BaseColor.whiteColor,
                borderRadius: 8,
                marginTop: 16,
                marginEnd: 4,
                flex: 1,
                borderColor: BaseColor.textGrey,
                borderWidth: 0.5,
              }}
              titleStyle={{
                color: BaseColor.whiteColor,
                fontWeight: "bold",
              }}
              loader={btnLoad === "edit"}
              onPress={() => {
                // editDevice();
                navigation.navigate("ChildInfo", {
                  type: "edit",
                  item: route.params.item || null,
                });
              }}
            />
            <CButton
              title={translate("deleteProfile")}
              style={{
                backgroundColor: BaseColor.alertRed,
                borderRadius: 8,
                marginTop: 16,
                marginStart: 4,
                flex: 1,
              }}
              titleStyle={{
                color: BaseColor.whiteColor,
                fontWeight: "bold",
              }}
              loader={btnLoad === "delete"}
              onPress={ConfirmdeleteDevice}
            />
          </View>
        </View>
      </Animated.View>
      <CAlert
          visible={isDeleting}
          onRequestClose={() =>
            setIsDeleting(false)
          }
          onCancelPress={() =>
            setIsDeleting(false)
          }
          loader={btnLoad === "delete"}
          onOkPress={deleteDevice}
          alertTitle={"Delete profile"}
          alertMessage={"Are you sure you want to delete this profile"}
          agreeTxt={"Yes"}
        />
    </View>
  );
};

export default CDeviceList;
