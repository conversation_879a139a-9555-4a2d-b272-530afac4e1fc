import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  BackHandler,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Keyboard,
} from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { useSelector } from "react-redux";
import styles from "./styles";
import BaseColor from "../../config/colors";
import CButton from "../../components/CButton";
import CInput from "../../components/CInput";
import { translate } from "../../lang/Translate";
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from "../../utils/commonFunction";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import GradientBack from "../../components/gradientBack";
import Toast from "react-native-simple-toast";

let backPressed = 0;

function UpdatePassword({ navigation }) {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const confirmPass = useRef();

  const [newPasswordError, setNewPasswordError] = useState(false);
  const [confirmPasswordError, setConfirmPasswordError] = useState(false);
  const [newPasswordErrorTxt, setNewPasswordErrorTxt] = useState("");
  const [confirmPasswordErrorTxt, setConfirmPasswordErrorTxt] = useState("");

  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);

  const userId = useSelector((state) => state.auth.user_id);

  const validation = () => {
    const passVal = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    enableAnimateInEaseOut();

    if (newPassword == "") {
      allErrorFalse();
      setNewPasswordError(true);
      setNewPasswordErrorTxt("Please enter New Password");
    } else if (!passVal.test(String(newPassword))) {
      allErrorFalse();
      setNewPasswordError(true);
      setNewPasswordErrorTxt(
        "New Password should contain atleast 8 characters, use uppper and lower case character, use 1 or more numbers and 1 special character",
      );
    } else if (newPassword != confirmPassword) {
      allErrorFalse();
      setConfirmPasswordError(true);
      setConfirmPasswordErrorTxt(
        "Confirm Password and New Password cannot match",
      );
    } else {
      allErrorFalse();
      updatePassword();
    }
  };

  const allErrorFalse = () => {
    setNewPasswordError(false);
    setConfirmPasswordError(false);
  };

  const updatePassword = () => {
    setloader(true);
    setanim(true);
    setBackAnim(false);
    const data = {
      user_id: userId,
      new_password: newPassword,
    };

    getApiData(BaseSetting.endpoints.updatePassword, "POST", data)
      .then((response) => {
        if (response.success) {
          setTimeout(() => {
            setloader(false);
            setdone(true);
          }, 2000);
          setTimeout(() => {
            navigation.navigate("Login");
          }, 3000);
        } else {
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch((err) => {
        Toast.show("Something went wrong while changing password");
        sendErrorReport(err, "changing_pass");
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log("ERRR", err);
      });
  };

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show("Press Again To Exit");
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={styles.root}>
      <GradientBack />
      <ScrollView contentContainerStyle={styles.mainContainer} bounces={false}>
        <KeyboardAvoidingView
          style={{ flex: 1, justifyContent: "center" }}
          behavior={Platform.OS == "ios" ? "height" : null}>
          <View style={styles.loginTextView}>
            <View>
              <View style={styles.lockIconStyle}>
                <MaterialCommunityIcons
                  name="key-outline"
                  color={BaseColor.blueDark}
                  size={40}
                />
              </View>
              <Text style={styles.loginText}>
                {translate("updatePasswordScreen")}
              </Text>
            </View>
          </View>
          <View style={styles.inputWrapper}>
            <CInput
              placeholder={translate("newPassword")}
              secureTextEntry
              value={newPassword}
              onChangeText={(val) => {
                setNewPassword(val);
              }}
              onSubmitEditing={() => {
                confirmPass.current.focus();
              }}
              placeholderTextColor={BaseColor.placeHolderColor}
              iconName="unlocked-padlock"
              showError={newPasswordError}
              errorMsg={newPasswordErrorTxt}
            />
          </View>
          <View style={styles.inputWrapper}>
            <CInput
              onSubmitEditing={() => {
                Keyboard.dismiss();
              }}
              ref={confirmPass}
              placeholder={translate("confirmPassword")}
              secureTextEntry
              value={confirmPassword}
              onChangeText={(val) => {
                setConfirmPassword(val);
              }}
              placeholderTextColor={BaseColor.placeHolderColor}
              iconName="unlocked-padlock"
              showError={confirmPasswordError}
              errorMsg={confirmPasswordErrorTxt}
            />
          </View>
          <View style={styles.inputWrapper}>
            <CButton
              style={styles.loginBtn}
              title={translate("forgotBtn")}
              anim
              playAnimation={anim}
              backAnim={backAnim}
              onPress={() => {
                validation();
              }}
              loader={loader}
              done={done}
            />
          </View>
        </KeyboardAvoidingView>
      </ScrollView>
    </View>
  );
}

export default UpdatePassword;
