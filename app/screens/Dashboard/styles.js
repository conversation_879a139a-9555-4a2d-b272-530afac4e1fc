/* eslint-disable quotes */
import { Dimensions, StyleSheet } from "react-native";
import { color } from "react-native-reanimated";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";
const dWidth = Dimensions.get("window").width;
const dHeight = Dimensions.get("window").height;

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  ring: {
    position: "absolute",
    width: 100,
    height: 100,
    borderRadius: 50,
    borderColor: "#39ECF9",
    borderWidth: 3,
  },
  ringView: {
    alignItems: "center",
    justifyContent: "center",
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  blueIconView: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: "center",
    alignItems: "center",
    // backgroundColor: "#447CD4",
  },
  disableRing: {
    borderColor: "#4E5C5E",
    borderWidth: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  arrowImage: {
    position: "absolute",
    backgroundColor: "#447CD4",
    paddingVertical: 10,
    paddingLeft: 5,
    right: 0,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  timeView: {
    backgroundColor: "rgba(96, 96, 96, 0.52)",
    borderRadius: 20,
    height: 40,
    flexDirection: "row",
    alignItems: "center",
    // alignContent: "center",
    paddingHorizontal: 27,
    width: 200,
    justifyContent: "space-between",
  },
  cardStyle: {
    flexWrap: "wrap",
    overflow: "hidden",
    width: dWidth - 32,
    height: dWidth - 32,
    borderRadius: 20,
    backgroundColor: BaseColor.whiteColor,
  },
  imageLastShadow: {
    marginTop: 10,
    backgroundColor: BaseColor.white20,
    justifyContent: "center",
    // width: dWidth - 132,
    // height: dWidth - 70,
    alignSelf: "center",
    borderRadius: 20,
    width: Dimensions.get("screen").width / 1.6,
    height: Dimensions.get("screen").height / 2.75,
  },
  imagemiddleShadow: {
    backgroundColor: BaseColor.transparentWhite,
    justifyContent: "center",
    // width: dWidth - 100,
    // height: dWidth - 100,
    alignSelf: "center",
    borderRadius: 20,
    width: Dimensions.get("screen").width / 1.4,
    height: Dimensions.get("screen").height / 3,
  },
  imageView: {
    // backgroundColor: BaseColor.whiteColor,
    // width: dWidth - 12,
    // height: dWidth - 82,
    alignSelf: "center",
    justifyContent: "center",
    borderRadius: 20,
    width: Dimensions.get("screen").width - 60,
    height: Dimensions.get("screen").height / 3,
    backgroundColor: "#fff",
    marginBottom: 30,
  },
  selectedImage: {
    // width: dWidth - 70,
    // height: dWidth - 92,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
    width: "100%",
    height: "100%",
  },
  imgTxt: {
    position: "absolute",
    zIndex: 10,
    left: 16,
    bottom: 16,
  },
  childName: {
    fontWeight: "bold",
    fontSize: 22,
    color: BaseColor.blackColor,
  },
  deviceMonit: {
    fontWeight: "bold",
  },
  shape: {
    width: dWidth,
    // flex: 1,
    // backgroundColor: 'red',
    // height: 132,
    // alignSelf: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    position: "absolute",
    // left: 0,
    // right: 0
  },
  circleContainer: {
    height: dWidth * 0.25,
    width: dWidth * 0.25,
    borderRadius: 100,
    padding: 8,
    position: "relative",
  },
  circleTitle: {
    width: dWidth * 0.25,
    textAlign: "center",
    fontSize: 11,
    color: BaseColor.whiteColor,
    position: "absolute",
    top: -(dWidth * 0.05),
  },
  shapeTitle: {
    width: "84%",
    alignSelf: "center",
    padding: 8,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingEnd: 64,
    paddingStart: 54,
  },
  alertView: {
    alignItems: "center",
    marginTop: -16,
  },
  alertTxt: {
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.default,
    fontWeight: "bold",
    fontSize: 12,
  },
  alertcircle: {
    backgroundColor: BaseColor.whiteColor,
    padding: 6,
    borderRadius: 50,
    marginTop: 4,
  },
  card: {
    backgroundColor: BaseColor.alertRed,
    width: dWidth - 32,
    height: dWidth - 32,
  },
  label: {
    fontSize: 24,
  },
  containerStyle: {
    backgroundColor: BaseColor.whiteColor,
    borderRadius: 20,
  },
  batteryContainer: {
    borderWidth: 1,
    borderColor: BaseColor.blackColor,
    borderRadius: 3,
    padding: 1,
    width: 35,
    height: 20,
  },
  batteryStatus: {
    backgroundColor: "green",
    width: "80%",
    height: "100%",
    borderRadius: 3,
  },
  batteryDot: {
    height: 7,
    width: 6,
    backgroundColor: "#000",
    position: "absolute",
    right: -6,
    top: "34%",
    borderTopRightRadius: 3,
    borderBottomRightRadius: 3,
  },
  batteryDisplay: {
    alignItems: "flex-end",
    justifyContent: "flex-end",
    paddingHorizontal: 40,
    position: "absolute",
    top: 20,
    right: -20,
  },
  pulseViewStyle: {
    position: "absolute",
    top: 40,
    bottom: 0,
    left: 0,
    right: 35,
    zIndex: 100,
    alignItems: "center",
    justifyContent: "center",
  },
  ripleStyle: {
    position: "absolute",
    height: 50,
    width: 50,
    borderRadius: 50,
    opacity: 0.4,
  },
  batteryPercentStyle: {
    position: "absolute",
    zIndex: 99,
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    height: "100%",
  },
  curvImgViewStyle: {
    flex: 1,
    // alignItems: "center",
    // justifyContent: "center",
    position: "absolute",
    bottom: 0,
  },
  curvImgStyle: {
    // position: "absolute",
    // bottom: -50,
  },
  sensorDotIndicator: {
    position: "absolute",
    height: 15,
    width: 15,
    borderRadius: 25,
  },
  sensorWrapper: {
    position: "absolute",
    height: 40,
    width: 40,
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
    backgroundColor: BaseColor.transparentWhite,
    borderRadius: 20,
  },
  seatAngleTextWrapper1: {
    position: "absolute",
    right: 70,
    top: 95,
  },
  seatAngleTextWrapper2: {
    position: "absolute",
    right: 70,
    bottom: 60,
  },
  angleText: { fontWeight: "bold" },
  resetButton: {
    alignSelf: "flex-end",
    borderWidth: 1,
    paddingHorizontal: 15,
    paddingVertical: 5,
    marginHorizontal: 20,
    marginVertical: 15,
    borderRadius: 5,
    backgroundColor: BaseColor.darkBlue,
    borderColor: BaseColor.whiteColor,
  },
  resetTxt: {
    color: BaseColor.whiteColor,
  },
  alertTitle: {
    fontWeight: "bold",
    textAlign: "center",
    paddingVertical: 10,
  },
  alertMsg: {
    textAlign: "center",
    paddingLeft: 20,
    // color: "red",
  },
  iconMainView: {
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 50,
    position: "absolute",
    bottom: 0,
    left: -24,
    top: 0,
  },
  alertMsgView: {
    marginTop: 50,
    marginHorizontal: 50,
    padding: 10,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,.2)",
  },
  // ----
  background__image: {
    width: dWidth,
    height: dHeight,
    backgroundColor: "#17181a",
    // marginTop: -10,
  },
  logo: {
    width: 40,
    height: 40,
    marginTop: 40,
    marginLeft: 10,
  },
  txtlogo: {
    width: dWidth * 0.7,
    height: 190,
    marginLeft: 10,
    marginBottom: -30,
  },
  cloud: {
    width: 22,
    height: 14,
    marginRight: 10,
  },
  containerImg: {
    height: dHeight * 0.5,
    display: "flex",
    // backgroundColor: "red",
    marginVertical: 20,
    alignContent: "center",
    alignItems: "center",
    justifyContent: "center",
  },
  seat_image: {
    width: "100%",
    height: "120%",
    // alignSelf: "center",
    marginLeft: -dWidth * 0.12,
    // marginTop: -29,
  },
  good_text: {
    color: BaseColor.green,
    fontSize: 12,
    fontWeight: "bold",
  },
  temp_main_text: {
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
  },
  temp_sub_text: {
    fontSize: 10,
    textAlign: "center",
    color: "#fff",
  },
  temp_sub_text1: {
    fontSize: 12,
    textAlign: "center",
    color: "#fff",
  },
  container: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 20,
  },
  slider: {
    width: 250,
    height: 40,
    transform: [{ scaleY: -1 }],
  },
  marksContainer: {
    flexDirection: "row",

    width: 250,

    marginTop: 10,
  },
  markText: {
    color: "#000",
    fontSize: 12,
  },
  right_buttons: {
    position: "absolute",
    right: 0,
    marginTop: 50,

    flexDirection: "row",
    zIndex: 99,
    alignItems: "center",
  },
  hwT: {
    position: "absolute",
    right: 0,
    marginTop: 10,
    backgroundColor: "rgba(54, 54, 54, 0.52)",
    flexDirection: "row",
    zIndex: 99,
    // alignItems: "center",
    width: "50%",
    height: 53,
    borderRadius: 30,
    justifyContent: "center",
  },
  right_sub_buttons: {
    backgroundColor: BaseColor.transparentColor,
    borderBottomLeftRadius: 15,
    // height: 120,
    // width: 40,
    // position: "absolute",
    // right: 8,
    // alignItems: "center",
    // justifyContent: "center",
    // borderRadius: 25,
    // shadowColor: "#000",
    // shadowOpacity: 0.4,
    // shadowRadius: 4,
    // shadowOffset: { width: -4, height: 4 },
    // elevation: 4,
    // transform: [{ scaleX: -1 }],
    flexDirection: "row",
    marginTop: -10,
    paddingTop: 15,
    paddingLeft: 20,
    paddingBottom: 5,
  },
  sub_button: {
    // backgroundColor: BaseColor.whiteColor,
    borderRadius: 5,
    width: 30,
    height: 30,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 5,
  },
  ble_dot: {
    width: 3,
    height: 3,
    borderRadius: 3,
    backgroundColor: BaseColor.green,
    position: "absolute",
    right: 12,
  },
  battery_percentage: {
    fontSize: 8,
    // marginTop: -5,
  },
  buttom_btns_row: {
    width: dWidth * 0.9,
    flexDirection: "row",
    justifyContent: "space-evenly",
    alignSelf: "center",
    marginVertical: 20,
    marginBottom: 200,
  },
  buttom_btn: {
    width: dWidth * 0.25,
    // height: dHeight * 0.15,
    paddingVertical: 10,
    backgroundColor: BaseColor.whiteColor,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 15,
  },
  title__text: {
    fontWeight: "bold",
    color: BaseColor.blackColor,
    fontSize: 12,
    marginVertical: 5,
  },
  values_row: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  baby_image: {
    width: 40,
    height: 40,
  },
  num__text: {
    fontWeight: "bold",
    color: BaseColor.blackColor,
    fontSize: 20,
  },
  value__text: {
    fontWeight: "bold",
    color: BaseColor.blackColor,
    fontSize: 8,
  },
  buttom_nvt_bar: {
    width: dWidth * 0.9,
    flexDirection: "row",
    justifyContent: "space-evenly",
    // backgroundColor: "red",
    alignSelf: "center",
    marginVertical: 20,
  },
  home_iocn: {
    width: dWidth * 0.25,
    paddingVertical: 10,
    backgroundColor: BaseColor.whiteColor,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 15,
    height: 30,
  },
});

export default styles;
