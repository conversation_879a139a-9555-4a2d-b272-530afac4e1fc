import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from "react-native";
import BaseColor from "../../config/colors";
import { useNavigation } from "@react-navigation/native";
import { translate } from "../../lang/Translate";
const dWidth = Dimensions.get("window").width;
const dHeight = Dimensions.get("window").height;
const DashboardNav = () => {
  const navigation = useNavigation();
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.tabButton}
        onPress={() => navigation.navigate(translate("home"))}
      >
        <Image
          source={require("../../assets/images/home.png")}
          style={styles.tabIcon}
        />
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tabButton, styles.active]}
        onPress={() => navigation.navigate("dashboard")}
      >
        <Image
          source={require("../../assets/images/dashboard.png")}
          style={styles.tabIcon}
        />
      </TouchableOpacity>
      <TouchableOpacity style={styles.tabButton}>
        <Image
          source={require("../../assets/images/graph.png")}
          style={styles.tabIcon}
        />
      </TouchableOpacity>
      <TouchableOpacity style={styles.tabButton}>
        <Image
          source={require("../../assets/images/settings.png")}
          style={styles.tabIcon}
        />
      </TouchableOpacity>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: BaseColor.black40,
    borderTopWidth: 1,
    borderColor: "#ccc",
    position: "absolute",
    bottom: 20,
    left: dWidth * 0.05,
    right: 0,
    width: dWidth * 0.9,
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
  tabButton: {
    padding: 8,
  },
  tabIcon: {
    width: 22,
    height: 22,
    resizeMode: "contain",
  },
  active: {
    backgroundColor: BaseColor.whiteColor,
    borderRadius: 25,
  },
});

export default DashboardNav;
