import React, { useEffect, useState } from "react";
import { NativeEventEmitter, NativeModules, Text, View } from "react-native";
import {
  CarPlay,
  ContactTemplate,
  ListTemplate,
  NowPlayingTemplate,
  GridTemplate,
  ActionSheetTemplate,
} from "../../lib/react-native-carplay";
import { useSelector } from "react-redux";
import BleManager from "react-native-ble-manager";
import { sendErrorReport } from "../../utils/commonFunction";
const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

const sections = Array.from({ length: 26 }).map((_, i) => ({
  header: `Header ${String.fromCharCode(97 + i).toLocaleUpperCase()}`,
  items: Array.from({ length: 3 }).map((_, j) => ({
    text: `Item ${j + 1}`,
  })),
  sectionIndexTitle: String.fromCharCode(97 + i).toLocaleUpperCase(),
}));

export function List() {
  const [selected, setSelected] = useState(childName);
  const title = "List";
  // useEffect(() => {
  //   const listTemplate = new ListTemplate({
  //     title,
  //     sections: [],
  //   });

  //   console.log("Stations.setRootTemplate: 1");
  //   CarPlay.setRootTemplate(listTemplate);
  //   return () => {
  //     console.log("Stations.popToRootTemplate: 1");
  //     CarPlay.popToRootTemplate(true);
  //   };
  // }, []);
  const gridItemImage = require("../../assets/images/three_front_correct.png");
  const warning = require("../../assets/images/warning.png");
  const checkmark = require("../../assets/images/checkmark.png");
  const bluetoothDis = require("../../assets/images/bluetoothDis.png");
  const {
    connectedDeviceDetail,
    activeChildDetail,
    isBleConnected,
    alertData,
    deviceID,
  } = useSelector((state) => state.bluetooth);
  const childName =
    activeChildDetail?.nick_name ||
    connectedDeviceDetail?.nick_name ||
    "Your Child";

  const retrieveConnected = async (deviceId) =>
    new Promise((resolve) => {
      BleManager.getConnectedPeripherals([]).then((results) => {
        if (results.length == 0) {
          console.log("No connected peripherals");
        }
        console.log(results);
        let matched = false;
        for (let i = 0; i < results.length; i++) {
          const peripheral = results[i];
          if (deviceId == peripheral.id) matched = true;
          // peripheral.connected = true;
          // peripherals.set(peripheral.id, peripheral);
          // setList(Array.from(peripherals.values()));
        }

        resolve(matched);
      });
    });
  /* To connect device => Returns a promise */
  const connectDevice = async () =>
    new Promise((resolve) => {
      console.log("BLE ==> Connecting", deviceID);
      if (!deviceID) {
        resolve(false);
      }
      BleManager.connect(deviceID)
        .then(() => {
          console.log("BLE ==> Connected");
          resolve(true);
        })
        .catch((error) => {
          console.log("BLE ==> Error");
          // setisConnecting(false);
          // Failure code
          console.log("error====*****", error, deviceID);
          sendErrorReport(error, "connect_device");
          resolve(false);
        });
    });

  const readData = async () => {
    // setisConnecting(true);
    let connected = null;
    try {
      const isAlreadyConnected = await retrieveConnected(deviceID);
      console.log(
        "CHECK --> BLE ==> isAlreadyConnected ==> 1",
        isAlreadyConnected
      );
      if (!isAlreadyConnected) {
        connected = await connectDevice(deviceID);
      } else {
        connected = true;
      }
    } catch (err) {
      console.log("CHECK --> BLE ==> coonect ==> Error 1", err);
      sendErrorReport(err, "read_data");
      // setisConnecting(false);
    }
    console.log("CHECK --> BLE ==> connected ===> 1", connected);

    // Success code
    console.log("CHECK --> Connected");
    // setconnnectedID(id);

    if (connected) {
      console.log("CHECK --> Before MTU");
      await setMTU(deviceID);
      console.log("CHECK --> Before Retreive");
      await BleManager.retrieveServices(deviceID)
        .then(async (peripheralData) => {
          console.log(
            "CHECK --> Retrieved peripheral services 1",
            peripheralData
          );

          const char = peripheralData.characteristics;

          // const mainChar = char[char.length - 1];

          // console.log("char-----", char);
          let mainChar = {};

          if (Platform.OS === "android") {
            char.forEach((charEle) => {
              // console.log("char-----element--", charEle.properties.Notify);
              if (charEle?.properties.Notify === "Notify") {
                // console.log("obj===notify--in for", charEle);
                mainChar = charEle;
              }
            });
          } else {
            mainChar = char.find((obj) => {
              // console.log("obj===-", obj);
              if (obj?.properties.includes("Notify")) {
                console.log("obj===notify", obj);
                return obj;
              }
            });
          }
          BleManager.startNotification(
            deviceID,
            mainChar.service,
            mainChar.characteristic
          )
            .then(() => {
              console.log(
                `CHECK --> Started notification on ${deviceID} ${mainChar.characteristic}`
              );
            })
            .catch((error) => {
              // Toast.show("ERROR:" + JSON.stringify(error));
              console.log("CHECK --> Notification error", error);
              sendErrorReport(error, "unable_to_start_notification");
            });
          // }, 1000);
          // });
          return true;
        })
        .catch((e) => {
          console.log(
            "CHECK EEEE --> Error -> Unable to Retrive Services",
            e?.message
          );
          sendErrorReport(e, "unable_retrieve_service");
          // sendErrorReport(JSON.stringify(e), "unable_retrieve_service");
          return true;
        });
      console.log("CHECK --> After Retreive");

      dispatch(BluetoothActions.setIsBleConnected(true));

      setTimeout(() => {
        setstartRead(true);
        // setisConnecting(false);
        Toast.show("CONNECTED");
      }, 2000);
    } else {
      console.log("CHECK --> not connected");
      // setisConnecting(false);
    }
  };

  const setMTU = (did) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve(true);
      }, 3000);
      BleManager.requestMTU(did, 512)
        .then((mtu) => {
          // Success code
          console.log("CHECK --> MTU size changed to " + mtu + " bytes");
          resolve(true);
        })
        .catch((error) => {
          // Failure code
          console.log("CHECK --> MTU", error);
          resolve(true);
        });
    });
  };

  useEffect(() => {
    const gridTemplate = new GridTemplate({
      // trailingNavigationBarButtons: [
      //   {
      //     id: "LEAD_2",
      //     type: "image",
      //     image: warning,
      //   },
      // ],
      buttons: [
        {
          id: childName,
          image: bluetoothDis,
          titleVariants: [childName],
        },
        {
          id: isBleConnected
            ? "Smart 360 IQ connected!"
            : "Smart 360 IQ not connected!",
          image: !isBleConnected ? warning : checkmark,
          titleVariants: [
            isBleConnected
              ? "Smart 360 IQ connected!"
              : "Smart 360 IQ not connected!",
          ],
        },
        {
          id: isBleConnected ? "" : "Press to Connect",
          image: !isBleConnected ? bluetoothDis : checkmark,
          titleVariants: [isBleConnected ? "" : "Press to Connect"],
        },
      ],
      title: "Dashboard",
      onButtonPressed(e) {
        setSelected(e.id);
        console.log("Dddddd connect to device---", e.id);
        if (e.id === "Press to Connect") {
          readData();
        }
      },
      onBarButtonPressed(e) {
        setSelected(e.id);
      },
    });

    CarPlay.pushTemplate(gridTemplate, true);

    return () => {
      CarPlay.popToRootTemplate(true);
    };
  }, []);
  useEffect(() => {
    if (alertData?.message) {
      const template = new ActionSheetTemplate({
        title: alertData?.title,
        message: alertData?.message,
        actions: [
          {
            id: "ok",
            title: "Ok",
          },
          {
            id: "cancel",
            title: "Cancel",
            style: "cancel",
          },
          {
            id: "remove",
            title: "Remove",
            style: "destructive",
          },
        ],
        onActionButtonPressed(e) {
          navigation.navigate("Menu");
        },
      });
      CarPlay.presentTemplate(template);
    }
  }, [alertData?.message]);
  // useEffect(() => {
  //   const nowPlay = new ContactTemplate({
  //     name: childName,
  //     image: gridItemImage,
  //     subtitle: isBleConnected
  //       ? "Smart 360 IQ connected!"
  //       : "Smart 360 IQ not connected!",
  //   });
  //   const listTemplate = new ListTemplate({
  //     sections,
  //     title: "List Template",
  //     async onItemSelect(e) {
  //       const { index } = e;
  //       setSelected(index);
  //     },
  //   });

  //   CarPlay.pushTemplate(nowPlay, true);

  //   return () => CarPlay.popToRootTemplate(true);
  // }, []);

  return (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <Text> {selected || "No item selected"}</Text>
    </View>
  );
}

List.navigationOptions = {
  headerTitle: "List Template",
};
