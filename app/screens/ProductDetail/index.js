/* eslint-disable quotes */
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  Linking,
  ScrollView,
  // Share,
  Text,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome";
import { useTheme } from "@react-navigation/native";
import Icon from "react-native-vector-icons/MaterialIcons";
import InAppBrowser from "react-native-inappbrowser-reborn";
import Toast from "react-native-simple-toast";
import HTML from "react-native-render-html";
import CHeader from "../../components/CHeader";
import GradientBack from "../../components/gradientBack";
import { translate } from "../../lang/Translate";
import styles from "./styles";
import {
  enableAnimateInEaseOut,
  enableAnimateLinear,
  openInAppBrowser,
  sendErrorReport,
} from "../../utils/commonFunction";
import { FontFamily } from "../../config/typography";
import RNFetchBlob from "rn-fetch-blob";
import { CustomIcon } from "../../config/LoadIcons";
import { getApiData } from "../../utils/apiHelper";
import { useSelector } from "react-redux";
import BaseSetting from "../../config/setting";
import Share from "react-native-share";

export default function ProductDetail({ navigation, route }) {
  const token = useSelector((state) => state.auth.accessToken);
  const productDetails = route?.params?.productDetail;

  const colors = useTheme();
  const BaseColor = colors.colors;

  const [currentIndex, setcurrentIndex] = useState(0);

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }
  // const link = `https://dcd020.myshopify.com/collections/${collectionTitle}/products/${productDetails.handle}?utm_source=App&utm_medium=shop&utm_id=CBT`;

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  // const onViewRef = React.useRef((viewableItems) => {
  //   console.log(viewableItems.viewableItems[0].index);
  //   setcurrentIndex(viewableItems.viewableItems[0].index);
  //   // Use viewable items in state or as intended
  // });
  // const viewConfigRef = React.useRef({ viewAreaCoveragePercentThreshold: 50 });

  async function getProductAction(id, type) {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.addproductaction,
        "POST",
        {
          product_id: id,
          type,
        },
        headers
      );

      if (response.success) {
        // setPostLike(response.data);
        // Toast.show(type);
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      sendErrorReport(error, "get_feed_post");
      console.log("feed post error ===", error);
    }
  }

  console.log("productDetails ", productDetails);

  const shareImage = (url) => {
    if (
      productDetails.product_link &&
      (productDetails.product_link.includes("http://") ||
        productDetails.product_link.includes("https://"))
    ) {
      const shareResponse = Share.open({
        url,
        message: `Shared Post ${productDetails.product_link}`,
      })
        .then((res1) => {})
        .catch((err) => {});
    } else {
      Toast.show(translate("urlError"));
    }
  };

  const onShare = async (type) => {
    try {
      if (type === "share") {
        const { fs } = RNFetchBlob;
        let imagePath = null;
        RNFetchBlob.config({
          fileCache: true,
        })
          .fetch("GET", productDetails.product_image)
          // the image is now dowloaded to device's storage
          .then((resp) => {
            // the image path you can use it directly with Image component
            imagePath = resp.path();
            return resp.readFile("base64");
          })
          .then((base64Data) => {
            // here's base64 encoded image
            console.log("base64Data =====>", base64Data);
            const base64 = `data:image/png;base64,${base64Data}`;
            shareImage(base64);
            // remove the file from storage
            return fs.unlink(imagePath);
          });
      } else {
        const result = await Share.open({
          message: `Shared Post ${productDetails.product_link}`,
          // url: shareText,
        });
        if (result.action === Share.sharedAction) {
          if (result.activityType) {
            // shared with activity type of result.activityType
          } else {
            // shared
          }
        } else if (result.action === Share.dismissedAction) {
          // dismissed
        }
      }
    } catch (error) {
      console.log("error: ==>", error);
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <GradientBack />

      <CHeader
        title={productDetails.product_name}
        backBtn
        leftIconName
        onLeftPress={() => {
          navigation.goBack();
        }}
      />

      <ScrollView contentContainerStyle={{}}>
        <View style={styles.root}>
          <View style={{ height: 400, width: "100%" }}>
            {/* <FlatList
              data={imgArr}
              horizontal
              pagingEnabled
              renderItem={({ item }) => (
                <Image style={styles.imageStyle} source={item.image} />
              )}
              contentContainerStyle={styles.imgFlatlist}
              onViewableItemsChanged={onViewRef.current}
              viewabilityConfig={viewConfigRef.current}
              showsHorizontalScrollIndicator={false}
            /> */}

            <Image
              style={styles.imageStyle}
              source={{ uri: productDetails.product_image }}
            />

            {/* <View style={styles.imgScrollDot}>
              {imgArr.map((item, index) => (
                <View
                  style={[
                    styles.dots,
                    {
                      backgroundColor:
                        index == currentIndex
                          ? BaseColor.blue
                          : BaseColor.black30,
                      borderColor:
                        index == currentIndex
                          ? BaseColor.blackColor
                          : BaseColor.whiteColor,
                    },
                  ]}
                />
              ))}
            </View> */}
          </View>

          <View style={{ padding: 8 }}>
            <View
              style={{
                flex: 1,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text style={[styles.name, { color: BaseColor.whiteColor }]}>
                {productDetails?.product_name}
              </Text>
              <TouchableOpacity
                style={{ padding: 8 }}
                activeOpacity={0.7}
                onPress={() => {
                  getProductAction(productDetails.id, "shared");
                  onShare("share");
                }}
              >
                <CustomIcon
                  name="send-2"
                  color={BaseColor.whiteColor}
                  size={18}
                />
              </TouchableOpacity>
            </View>
            <Text style={[styles.price, { color: BaseColor.whiteColor }]}>
              $ {productDetails?.sell_price}
            </Text>

            {/* <Text style={[styles.despTxt, { color: BaseColor.whiteColor }]}>
              Description
            </Text>
            {despArr.map((item) => (
              <View style={[styles.row, styles.desp]}>
                <FAIcon
                  name="dot-circle-o"
                  color={BaseColor.whiteColor}
                  size={16}
                />
                <Text
                  style={[
                    styles.addToCartText,
                    { color: BaseColor.whiteColor },
                  ]}
                >
                  {item?.text}
                </Text>
              </View>
            ))} */}

            {/* <HTML
              source={{ html: "<p>testinng</p>" }}
              contentWidth={useWindowDimensions().width}
            /> */}

            <TouchableOpacity
              // ref={this.accordian}
              activeOpacity={0.7}
              style={[
                styles.accCont,
                { marginTop: 24, backgroundColor: BaseColor.blueDark },
              ]}
              onPress={async () => {
                if (
                  productDetails?.product_guide &&
                  productDetails?.product_guide.includes("https://")
                ) {
                  openInAppBrowser(productDetails.product_guide);
                } else {
                  Toast.show("Not valid Link");
                }
              }}
            >
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: "bold",
                  color: BaseColor.whiteColor,
                  fontFamily: FontFamily.default,
                }}
              >
                {translate("faqNManuals")}
              </Text>
              <Icon
                name="keyboard-arrow-down"
                size={30}
                color={BaseColor.whiteColor}
              />
            </TouchableOpacity>
            <TouchableOpacity
              // ref={this.accordian}
              activeOpacity={0.7}
              style={[styles.accCont, { backgroundColor: BaseColor.blueDark }]}
              onPress={() => {
                if (
                  productDetails?.video_link &&
                  productDetails?.video_link.includes("https://")
                ) {
                  openInAppBrowser(productDetails.video_link);
                } else {
                  Toast.show("Not valid Link");
                }
              }}
            >
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: "bold",
                  color: BaseColor.whiteColor,
                  fontFamily: FontFamily.default,
                }}
              >
                {translate("technicalDrawing")}{" "}
              </Text>
              <Icon
                name="keyboard-arrow-down"
                size={30}
                color={BaseColor.whiteColor}
              />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      <TouchableOpacity
        style={[styles.addToCartView, { backgroundColor: BaseColor.blue }]}
        activeOpacity={0.7}
        onPress={() => {
          console.log(
            "openUrl -> productDetails?.product_link",
            productDetails?.product_link
          );
          if (
            productDetails?.product_link &&
            productDetails?.product_link.includes("https://")
          ) {
            getProductAction(productDetails.id, "clicked");
            openInAppBrowser(productDetails?.product_link);
          } else {
            Toast.show("Not valid Link");
          }
        }}
      >
        <FAIcon name="shopping-cart" color={BaseColor.whiteColor} size={24} />
        <Text style={[styles.addToCartText, { color: BaseColor.whiteColor }]}>
          Buy now
        </Text>
      </TouchableOpacity>
    </View>
  );
}
