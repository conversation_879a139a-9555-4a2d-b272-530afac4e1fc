/* eslint-disable quotes */
import { Dimensions, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";

const { height: HEIGHT, width: WIDTH } = Dimensions.get("window");

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  imageStyle: {
    height: 400,
    width: WIDTH,
    alignSelf: "center",
  },
  imgFlatlist: {
    // width: WIDTH,
    backgroundColor: "red",
    flexWrap: "wrap",
    height: 400,
  },
  dots: {
    backgroundColor: BaseColor.black80,
    borderRadius: 24,
    height: 12,
    width: 12,
    marginHorizontal: 4,
    borderWidth: 0.5,
    borderColor: BaseColor.whiteColor,
  },
  imgScrollDot: {
    height: 20,
    flexDirection: "row",
    position: "absolute",
    bottom: 8,
    left: 0,
    right: 0,
    justifyContent: "center",
    alignItems: "center",
  },
  name: {
    fontSize: 18,
    marginVertical: 8,
    fontFamily: FontFamily.default,
    color: BaseColor.whiteColor,
    fontWeight: "bold",
  },

  price: {
    fontSize: 18,
    // fontWeight: 'bold',
    fontFamily: FontFamily.default,
    color: BaseColor.whiteColor,
  },
  row: { flexDirection: "row", width: WIDTH },
  addToCartView: {
    backgroundColor: BaseColor.orange,
    borderRadius: 50,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    margin: 12,
    flexDirection: "row",
    marginBottom: 24,
  },
  addToCartText: {
    fontFamily: FontFamily.default,
    color: BaseColor.whiteColor,
    marginStart: 12,
  },
  desp: {
    alignItems: "center",
    marginTop: 8,
  },
  despTxt: {
    fontSize: 18,
    fontFamily: FontFamily.default,
    fontWeight: "bold",
    color: BaseColor.whiteColor,
    marginTop: 24,
  },
  accCont: {
    flexDirection: "row",
    justifyContent: "space-between",
    height: 56,
    paddingLeft: 25,
    paddingRight: 18,
    alignItems: "center",
    backgroundColor: BaseColor.blueDark,
  },
  newTextStyle: {
    color: BaseColor.black90,
    fontFamily: FontFamily.default,
    marginTop: 5,
    fontWeight: "bold",
  },
});

export default styles;
