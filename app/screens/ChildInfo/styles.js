import { Platform, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  chooseProfile: {
    paddingBottom: 20,
    fontSize: 12,
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.default,
    fontWeight: "bold",
    textAlign: "center",
  },
  imageLastShadow: {
    marginTop: 10,
    backgroundColor: BaseColor.white20,
    justifyContent: "center",
    width: 172,
    height: 268,
    alignSelf: "center",
    borderRadius: 20,
  },
  imagemiddleShadow: {
    marginBottom: 20,
    backgroundColor: BaseColor.transparentWhite,
    justifyContent: "center",
    width: 212,
    height: 260,
    alignSelf: "center",
    borderRadius: 20,
  },
  imageView: {
    // backgroundColor: BaseColor.whiteColor,
    width: 250,
    height: 250,
    alignSelf: "center",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 20,
  },
  selectedImage: {
    width: 250,
    height: 250,
    borderRadius: 20,
  },
  imageText: {
    color: BaseColor.blueDark,
    fontSize: 18,
    textAlign: "center",
    paddingTop: 26,
    fontWeight: "bold",
    fontFamily: FontFamily.default,
  },
  imageText1: {
    color: BaseColor.textGrey,
    fontSize: 12,
    textAlign: "center",
  },
  infoView: {
    backgroundColor: BaseColor.blueDark,
    borderTopLeftRadius: 22,
    borderTopRightRadius: 22,
    paddingHorizontal: 20,
  },
  horizontalLine: {
    width: 40,
    height: 4,
    backgroundColor: BaseColor.black30,
    position: "absolute",
    alignSelf: "center",
    top: 18,
    borderRadius: 3,
  },
  infoText: {
    color: BaseColor.whiteColor,
    fontSize: 18,
    fontWeight: "bold",
    paddingTop: 38,
    marginBottom: 18,
    fontFamily: FontFamily.default,
  },
  textInputView: {
    flexDirection: "row",
    marginBottom: 8,
  },
  iconView: {
    alignSelf: "center",
    borderRadius: 150,
    justifyContent: "center",
    alignItems: "center",
  },
  genderIcon: {
    textAlign: "center",
    alignSelf: "center",
    textAlignVertical: "center",
    borderRadius: 75,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: BaseColor.whiteColor,
    padding: 16,
    borderColor: BaseColor.whiteColor,
    borderWidth: 4,
  },
  selectedCheck: {
    width: 24,
    height: 24,
    position: "absolute",
    bottom: 20,
    right: 0,
    borderColor: BaseColor.whiteColor,
    borderWidth: 3,
    textAlign: "center",
    alignSelf: "center",
    textAlignVertical: "center",
    borderRadius: 75,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10,
    padding: 4,
  },
  genderName: {
    fontSize: 14,
    textAlign: "center",
    fontFamily: FontFamily.default,
    marginTop: 4,
    color: BaseColor.whiteColor,
    fontWeight: "bold",
  },
  cardStyle: {
    flexWrap: "wrap",
    overflow: "hidden",
    height: 250,
    width: 250,
    borderRadius: 20,
    backgroundColor: BaseColor.whiteColor,
  },
  containerStyle: {
    backgroundColor: BaseColor.whiteColor,
    borderRadius: 20,
  },
  date_picker: {
    width: "100%",
    backgroundColor: BaseColor.whiteColor,
    height: 50,
    borderRadius: 12,
    justifyContent: "center",
    paddingLeft: 28,
  },
  date_input: {
    color: BaseColor.blackColor,
  },
  editIcon: {
    position: "absolute",
    bottom: 5,
    borderWidth: 1,
    borderColor: BaseColor.blue,
    borderRadius: 30,
    padding: 8,
    backgroundColor: BaseColor.whiteColor,
  },
});

export default styles;
