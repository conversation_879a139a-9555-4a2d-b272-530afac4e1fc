/* eslint-disable quotes */
import React, { useEffect } from "react";
import {
  Text,
  View,
  StatusBar,
  TouchableOpacity,
  BackHandler,
} from "react-native";
import Toast from "react-native-simple-toast";
import { useTheme } from "@react-navigation/native";
import GradientBack from "../../components/gradientBack";
import styles from "./styles";
import { CustomIcon } from "../../config/LoadIcons";
import { translate } from "../../lang/Translate";

const Connect = ({ navigation, route }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  function handleBackButtonClick() {
    Toast.show("Can't go back!");
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  return (
    <>
      <GradientBack />
      <View style={styles.root}>
        <StatusBar backgroundColor="transparent" barStyle="light-content" />
        <View
          style={[
            styles.imageLastShadow,
            { backgroundColor: BaseColor.white20 },
          ]}
        >
          <View
            style={[
              styles.imagemiddleShadow,
              { backgroundColor: BaseColor.transparentWhite },
            ]}
          >
            <View
              style={[
                styles.imageView,
                { backgroundColor: BaseColor.whiteColor },
              ]}
            >
              <CustomIcon
                name="smartphone"
                size={40}
                color={BaseColor.blueLight}
                style={styles.deviceIcon}
              />
            </View>
          </View>
        </View>

        <Text style={[styles.connectText, { color: BaseColor.whiteColor }]}>
          {translate("connectScreen")}
        </Text>
        <Text style={[styles.otherText, { color: BaseColor.whiteColor }]}>
          {translate("connectSuccessText")}
        </Text>

        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            navigation.navigate("ChildInfo", {
              type: "connect",
              product_id: route?.params?.product_id,
              device_id: route?.params?.device_id,
              product_id: route?.params?.product_id,
            });
          }}
          style={[
            styles.checkIconView,
            { backgroundColor: BaseColor.whiteColor },
          ]}
        >
          <CustomIcon
            name="check"
            size={20}
            color={BaseColor.blueLight}
            style={styles.checkIcon}
          />
        </TouchableOpacity>
      </View>
    </>
  );
};

export default Connect;
