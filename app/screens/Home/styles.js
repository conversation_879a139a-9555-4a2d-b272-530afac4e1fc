/* eslint-disable quotes */
import { StyleSheet } from "react-native";
import BaseColor from "../../config/colors";

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
  },
  deviceCard: {
    height: 100,
    width: 100,
    backgroundColor: BaseColor.whiteColor,
    // padding: 8,
    overflow: "hidden",
    borderRadius: 16,
    marginEnd: 12,
    justifyContent: "center",
    alignItems: "center",
    borderStyle: "dashed",
    borderColor: "#fff",
  },
  smartDeviceView: {
    marginTop: 24,
  },
  guideView: {
    marginTop: 36,
  },
  guideRootView: {
    backgroundColor: BaseColor.whiteColor,
    borderRadius: 8,
    overflow: "hidden",
    marginVertical: 12,
    display: "flex",
  },
  videoView: {
    marginTop: 20,
    flex: 1,
    // borderWidth: 1,
    borderTopLeftRadius: 50,
    // backgroundColor: "#039be5",
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: "flex-end",
    position: "absolute",
    right: 12,
    top: 34,
  },
  emptyComponent: {
    minHeight: 350,
    alignItems: "center",
    justifyContent: "center",
  },
});

export default styles;
