/* eslint-disable quotes */
import React, { useEffect, useState, useRef } from "react";
import {
  BackHandler,
  TouchableOpacity,
  View,
  Text,
  Dimensions,
} from "react-native";
import Share from "react-native-share";
import { useTheme } from "@react-navigation/native";
import QRCode from "react-native-qrcode-svg";
import Toast from "react-native-simple-toast";
import { useDispatch, useSelector } from "react-redux";
import { findIndex, isEmpty } from "lodash";
import BleManager from "react-native-ble-manager";
import RNFetchBlob from "rn-fetch-blob";
import CHeader from "../../components/CHeader";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import BluetoothActions from "../../redux/reducers/bluetooth/actions";
import { translate } from "../../lang/Translate";
import { FontFamily } from "../../config/typography";

/**
 *
 *@module QRCodeDetail
 *
 */

let interval = null;
export default function QRCodeDetail({ navigation, route }) {
  const QRDetail = route?.params?.QRCodeDetail;
  const colors = useTheme();
  const BaseColor = colors.colors;
  const dispatch = useDispatch();
  const [timer, setTimer] = React.useState(0);
  const accessToken = useSelector((state) => state.auth.accessToken);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);
  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const { setActiveDeviceId } = BluetoothActions;
  const { width: dWidth } = Dimensions.get("window");
  const { connectedDeviceDetail, activeDeviceId, qrDetail } = useSelector(
    (state) => state.bluetooth
  );
  const [qrCode, setqrCode] = useState("");
  let svg = useRef();

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  useEffect(() => {
    if (loader) {
      interval = setInterval(() => setTimer((p) => p - 1), 1000);
    }
  }, [loader]);

  useEffect(() => {
    if (timer === 0) {
      clearInterval(interval);
      setloader(false);
    }
  }, [timer]);

  /** this function for handle Manual Disconnect device
   * @function handleManualDisconnect
   * @param {object} data id
   */
  const handleManualDisconnect = (id, childId) => {
    const arr = [...connectedDeviceDetail] || [];
    const index = findIndex(arr, (lt) => lt?.product_id === id);
    if (index > -1 && connectedDeviceDetail[index]?.connected === 1) {
      setloader(true);
      setTimer(10);
      setTimeout(() => {
        BleManager.disconnect(`${id}`)
          .then(() => {
            // Success code
            // if (index !== -1) arr.splice(index, 1);
            // delete isCurrentActiveDevice[id];
            disconnectChildDevice(arr, index, childId);
          })
          .catch((error) => {
            // Failure code
            Toast.show("Device already disconnected");
          });
      }, 10000);
    } else {
      Toast.show("Device already disconnected");
    }
  };

  /** this function for handle Manual Disconnect device
   * @function disconnectChildDevice
   * @param {object} data device_id
   */
  async function disconnectChildDevice(item, index, childId) {
    console.log("disconnnect ------> called1");
    setanim(true);
    setBackAnim(false);
    const headers = {
      "Content-Type": "application/json",
      authorization: accessToken ? `Bearer ${accessToken}` : "",
    };

    if (!isEmpty(activeDeviceId) && activeDeviceId?.id) {
      try {
        const response = await getApiData(
          BaseSetting.endpoints.disconnectChildDevice,
          "POST",
          {
            device_id: activeDeviceId?.id,
            product_id: activeDeviceId?.product_id,
            child_id: childId,
            app_name: "cbtaqm",
          },
          headers
        );
        if (response.success) {
          setloader(false);
          const array = [...connectedDeviceDetail];
          array[index].connected = 0;

          dispatch(
            setActiveDeviceId(
              connectedDeviceDetail?.length > 0 ? array[index] : {}
            )
          );
          dispatch(BluetoothActions.setConnectedDeviceDetail(array));
          dispatch(BluetoothActions.setConnectedDeviceDetails(array));
          setanim(false);
          setBackAnim(true);
          setdone(true);
        }
      } catch (error) {
        console.log("disconnect device error ===", error);
        setloader(false);
        setanim(false);
        setBackAnim(true);
        setdone(false);
      }
    }
  }
  const saveQRCode = () => {
    svg.toDataURL(callback);
  };

  const callback = (dataURL) => {
    const shareImageBase64 = {
      title: "CleanAir",
      url: `data:image/png;base64,${dataURL}`,
      subject: "Share QR code",
      message: qrDetail || QRDetail.device_ssid, //  for email
    };
    Share.open(shareImageBase64).catch((error) => console.log(error));
  };
  console.log("hhhhh", qrDetail);
  return (
    <View style={{ flex: 1, backgroundColor: BaseColor.whiteColor }}>
      {/* <GradientBack /> */}

      <CHeader
        title={QRDetail.device_ssid}
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
        image
        titleTextColor={BaseColor.blackColor}
        headerBackgroundColor={BaseColor.inputBackGroundColor}
        isViolateLogo
        // backTitle="Settings"
      />
      <View style={{ flex: 1, alignItems: "center", marginTop: 78 }}>
        {QRDetail?.device_ssid && (
          <QRCode
            getBase64={(base64) => {
              setqrCode(base64);
            }}
            value={QRDetail?.device_ssid}
            size={200}
            getRef={(c) => (svg = c)}
          />
        )}
        <TouchableOpacity
          // disabled={connectedDeviceDetail.length === 0}
          style={{
            marginTop: 35,
            backgroundColor: BaseColor.blackColor,
            borderRadius: 12,
            height: 64,
            width: dWidth - 40,

            justifyContent: "center",
          }}
          onPress={() => {
            console.log("on share");
            saveQRCode();
          }}
          // handleManualDisconnect(QRDetail?.product_id, QRDetail.child_id)}
        >
          <Text
            style={{
              color: BaseColor.whiteColor,
              fontSize: 16,
              fontFamily: FontFamily.default,
              fontWeight: "700",
              textAlign: "center",
            }}
          >
            Share link
            {/* {timer > 0
              ? `${timer > 9 ? timer : `0${timer}`} `
              : translate("allowPairing")} */}
          </Text>
        </TouchableOpacity>
        {/* <View style={{ textAlign: "center", paddingHorizontal: 40 }}>
          <Text
            style={{
              color: BaseColor.blackColor,
              fontSize: 15,
              fontFamily: FontFamily.default,
              fontWeight: "bold",
              // letterSpacing: 1,
              textAlign: "center",
              marginTop: 35,
            }}
          >
            {translate("allowPairingText")}
          </Text>
        </View> */}
      </View>
    </View>
  );
}
