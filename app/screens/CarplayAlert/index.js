import React, { useEffect, useState } from "react";
import { Text, View } from "react-native";
import { CarPlay, AlertTemplate } from "react-native-carplay";

export function CarplayAlert() {
  const [buttonClicked, setButtonClicked] = useState();

  useEffect(() => {
    const template = new AlertTemplate({
      titleVariants: ["Alert", "This is alert"],
      actions: [
        {
          id: "ok",
          title: "Ok",
        },
        {
          id: "cancel",
          title: "Cancel",
          style: "cancel",
        },
        {
          id: "remove",
          title: "Remove",
          style: "destructive",
        },
      ],
      onActionButtonPressed({ id }) {
        setButtonClicked(id);
        if (id === "remove") {
          CarPlay.dismissTemplate();
        }
      },
    });
    CarPlay.presentTemplate(template);
    return () => {
      CarPlay.dismissTemplate();
    };
  }, []);

  return (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <Text>Alert</Text>
      <Text>{`Clicked button: ${buttonClicked}`}</Text>
    </View>
  );
}

CarplayAlert.navigationOptions = {
  headerTitle: "Alert Template",
};
