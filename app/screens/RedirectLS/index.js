/* eslint-disable global-require */
/* eslint-disable quotes */
import React, { useEffect, useState } from "react";
import Video from "react-native-video";
import {
  ActivityIndicator,
  BackHandler,
  ImageBackground,
  Modal,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
  Platform,
  Dimensions,
} from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import Toast from "react-native-simple-toast";
import { SvgXml } from "react-native-svg";
import { useDispatch, useSelector } from "react-redux";
import LinearGradient from "react-native-linear-gradient";
import styles from "./styles";
import CButton from "../../components/CButton";
import { initTranslate, translate } from "../../lang/Translate";
import { CustomIcon } from "../../config/LoadIcons";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";
import langArr from "../../assets/flagSvg/flags";
import languageActions from "../../redux/reducers/language/actions";
import { store } from "../../redux/store/configureStore";

let backPressed = 0;
const { width: dWidth } = Dimensions.get("window");
const RedirectLS = ({ navigation }) => {
  const dispatch = useDispatch();
  const { setLanguage } = languageActions;
  const languageData = useSelector((state) => state.language.languageData);
  // console.log("RedirectLS -> languageData", languageData);

  const logoAnimT = useSharedValue(0);
  const logoAnimO = useSharedValue(0);
  const textAnimT = useSharedValue(0);
  const btnAnimT = useSharedValue(0);
  const langAnimT = useSharedValue(0);

  const flagsArr = langArr;
  const selectedLang = flagsArr.filter((item) => item.code === languageData);

  const [selectedLanguage, setselectedLanguage] = useState(selectedLang[0]);
  const [langModal, setlangModal] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const animTime = 200;
  const anim = useSharedValue(1);
  const titleStyle = useAnimatedStyle(() => ({
    opacity: withTiming(
      anim.value,
      {
        duration: animTime,
      },
      () => {
        anim.value = 1;
      }
    ),
  }));
  const desStyle = useAnimatedStyle(() => ({
    opacity: withTiming(
      anim.value,
      {
        duration: animTime,
      },
      () => {
        anim.value = 1;
      }
    ),
  }));
  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show("Press Again To Exit");
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const logoStyleAnim = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(logoAnimT.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(logoAnimO.value, {
      duration: 1000,
    }),
  }));

  const textStyleAnim = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(textAnimT.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(logoAnimO.value, {
      duration: 1000,
    }),
  }));

  const btnStyleAnim = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(btnAnimT.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(logoAnimO.value, {
      duration: 1000,
    }),
  }));

  const langStyleAnim = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(langAnimT.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(logoAnimO.value, {
      duration: 1000,
    }),
  }));

  useEffect(() => {
    logoAnimT.value = -200;
    textAnimT.value = -100;
    btnAnimT.value = -50;
    logoAnimO.value = 1;
    langAnimT.value = -50;
  }, []);

  const changeLanguage = (code, name) => {
    dispatch(setLanguage(code, name));
    setTimeout(() => {
      initTranslate(store, true);
    }, 100);

    setTimeout(() => {
      setRefresh(false);
      logoAnimT.value = -200;
      textAnimT.value = -100;
      btnAnimT.value = -50;
      logoAnimO.value = 1;
      langAnimT.value = -50;
    }, 500);
    // dispatch(setLanguage(code, name));
  };

  // useEffect(() => {
  //   setTimeout(() => {
  //     initTranslate(store, true);
  //   }, 500);
  // }, [selectedLang]);

  return (
    <>
      <StatusBar
        backgroundColor="transparent"
        barStyle="light-content"
        translucent
      />
      {/* <ImageBackground
        style={styles.root}
        source={require("../../assets/images/RedirectImg.jpg")}
        resizeMode="cover"
      ></ImageBackground> */}
      <Video
        source={require("../../assets/images/loginpage.mp4")}
        style={styles.backgroundVideo}
        muted={true}
        repeat={true}
        resizeMode={"cover"}
        rate={1.0}
        ignoreSilentSwitch={"obey"}
      />
      {!refresh ? (
        <View style={styles.mainContainer}>
          <View style={styles.container}>
            <View
              style={[
                {
                  position: "absolute",
                  right: dWidth * 0.45,
                  top: "6%",
                },
              ]}
            >
              {/* <SVGImg2 width={30} height={30} /> */}
            </View>
            {/* <Animated.Image
                source={require("../../assets/images/logo.png")}
                style={[
                  {
                    height: 50,
                    width: 50,
                    position: "absolute",
                    right: 16,
                    top: 240,
                    borderRadius: 3,
                  },
                  logoStyleAnim,
                ]}
              /> */}
            {/* <Animated.Text style={[styles.txtStyle, textStyleAnim]}>
                {translate("designedForDiscovery")}
              </Animated.Text> */}
            <View style={styles.contentView}>
              <Animated.Text style={[styles.titleText, titleStyle]}>
                {translate("introTitle4")}
              </Animated.Text>
              <Animated.Text style={[styles.descriptionText, desStyle]}>
                {translate(
                  "Each smart device can be connected to multiple apps so partner and care givers all can monitor the child's health"
                )}
              </Animated.Text>
              <Animated.View
                style={[
                  styles.row,
                  {
                    bottom: 0,
                    position: "absolute",
                    alignSelf: "center",
                  },
                  btnStyleAnim,
                ]}
              >
                <CButton
                  title={translate("loginToSignup")}
                  style={styles.signupBtn}
                  btnColor={["rgba(0, 0, 0, 0)", "rgba(0, 0, 0, 0)"]}
                  titleStyle={{
                    ...styles.loginTxt,
                    color: BaseColor.blackColor,
                  }}
                  onPress={() => {
                    navigation.navigate("Signup");
                  }}
                />
                <CButton
                  title={translate("login")}
                  style={styles.loginBtn}
                  titleStyle={styles.loginTxt}
                  onPress={() => {
                    navigation.navigate("Login");
                  }}
                />
              </Animated.View>
            </View>
            {/* <Animated.View
                style={[
                  {
                    top: 80,
                    marginTop: 12,
                    position: "absolute",
                    margin: 8,
                  },
                  langStyleAnim,
                ]}
              >
                <TouchableOpacity
                  style={styles.langBtn}
                  activeOpacity={0.7}
                  onPress={() => {
                    setlangModal(true);
                  }}
                >
                  {/* <SvgXml xml={selectedLanguage.svg} width={20} height={20} /> }
                  <Text
                    style={{
                      color: BaseColor.whiteColor,
                      marginHorizontal: 8,
                      fontFamily: FontFamily.default,
                      fontWeight: "bold",
                    }}
                  >
                    {selectedLanguage.title}
                  </Text>
                  <CustomIcon
                    name="expand-button"
                    color={BaseColor.whiteColor}
                  />
                </TouchableOpacity>
              </Animated.View> */}
            <View />
          </View>
        </View>
      ) : null}

      <Modal
        transparent
        style={{ flex: 1 }}
        visible={langModal}
        animationType="fade"
      >
        <TouchableOpacity
          style={{ flex: 1 }}
          onPress={() => {
            setlangModal(false);
          }}
        >
          <View style={styles.modalCont}>
            {flagsArr.map((item, index) => (
              <TouchableOpacity
                key={`${index}`}
                style={styles.langBtn}
                activeOpacity={0.7}
                onPress={() => {
                  setlangModal(false);
                  logoAnimT.value = 0;
                  textAnimT.value = 0;
                  btnAnimT.value = 0;
                  logoAnimO.value = 0;
                  langAnimT.value = 0;
                  setTimeout(() => {
                    setRefresh(true);
                  }, 500);
                  setTimeout(() => {
                    changeLanguage(item.code, item.title);
                  }, 2000);
                  setselectedLanguage(item);
                }}
              >
                <View style={styles.flagDesign}>
                  <SvgXml xml={item.svg} width={20} height={20} />
                  <Text
                    style={{
                      color: BaseColor.blackColor,
                      marginHorizontal: 8,
                      fontFamily: FontFamily.default,
                    }}
                  >
                    {item.title}
                  </Text>
                </View>
                <CustomIcon name="expand-button" color={BaseColor.blackColor} />
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>

      <Modal
        transparent
        style={{ flex: 1 }}
        visible={refresh}
        animationType="fade"
      >
        <TouchableOpacity
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: BaseColor.white50,
          }}
          onPress={() => setRefresh(false)}
        >
          <View
            style={{
              padding: 12,
              backgroundColor: BaseColor.whiteColor,
              borderRadius: 12,
            }}
          >
            <Text
              style={{
                fontFamily: FontFamily.default,
                fontWeight: "bold",
                marginBottom: 12,
              }}
            >
              Changing language
            </Text>
            <ActivityIndicator size="small" color={BaseColor.blueDark} />
          </View>
        </TouchableOpacity>
      </Modal>
      
      {/* <ImageBackground
        style={styles.root}
        source={require("../../assets/images/04-1.1.png")}
        resizeMode="cover"
      >
        <LinearGradient
          start={{ x: 0, y: 0.5 }}
          end={{ x: 0, y: 1 }}
          colors={[BaseColor.white10, BaseColor.blackColor]}
          style={{ height: "100%" }}
        >
          {!refresh ? (
            <View style={styles.mainContainer}>
              <View style={styles.container}>
                <Animated.Image
                  source={require("../../assets/images/app_logo.png")}
                  style={[{ height: 100, width: 280, top: 250 }, logoStyleAnim]}
                />
                <Animated.Text
                  style={[styles.txtStyle, { top: 100 }, textStyleAnim]}
                >
                  {translate("functionalComfortable")}
                </Animated.Text>
                <Animated.View
                  style={[styles.row, { marginTop: 40, top: 50 }, btnStyleAnim]}
                >
                  <CButton
                    title={translate("login")}
                    style={styles.loginBtn}
                    titleStyle={{ color: BaseColor.whiteColor }}
                    onPress={() => {
                      navigation.navigate("Login");
                    }}
                  />
                  <CButton
                    title={translate("loginToSignup")}
                    style={styles.signupBtn}
                    // titleStyle={{
                    //   ...styles.loginTxt,
                    //   padding: Platform.OS === "ios" ? 0 : 20,
                    // }}
                    titleStyle={{ color: BaseColor.whiteColor }}
                    onPress={() => {
                      navigation.navigate("Signup");
                    }}
                  />
                </Animated.View>
                <Animated.View
                  style={[{ top: 50, marginTop: 4 }, langStyleAnim]}
                >
                  <TouchableOpacity
                    style={styles.langBtn}
                    activeOpacity={0.7}
                    onPress={() => {
                      setlangModal(true);
                    }}
                  >
                    <SvgXml xml={selectedLanguage.svg} width={20} height={20} />
                    <Text
                      style={{
                        color: BaseColor.white90,
                        marginHorizontal: 8,
                        fontFamily: FontFamily.default,
                      }}
                    >
                      {selectedLanguage.title}
                    </Text>
                    <CustomIcon
                      name="expand-button"
                      color={BaseColor.whiteColor}
                    />
                  </TouchableOpacity>
                </Animated.View>
                <View />
              </View>
            </View>
          ) : null}
          <Modal
            transparent
            style={{ flex: 1 }}
            visible={langModal}
            animationType="slide"
          >
            <TouchableOpacity
              style={{ flex: 1 }}
              onPress={() => {
                setlangModal(false);
              }}
            >
              <View style={styles.modalCont}>
                {flagsArr.map((item, index) => (
                  <TouchableOpacity
                    key={`${index}`}
                    style={styles.langBtn}
                    activeOpacity={0.7}
                    onPress={() => {
                      setlangModal(false);
                      logoAnimT.value = 0;
                      textAnimT.value = 0;
                      btnAnimT.value = 0;
                      logoAnimO.value = 0;
                      langAnimT.value = 0;
                      setTimeout(() => {
                        setRefresh(true);
                      }, 500);
                      setTimeout(() => {
                        changeLanguage(item.code, item.title);
                      }, 2000);
                      setselectedLanguage(item);
                    }}
                  >
                    <View style={styles.flagDesign}>
                      <SvgXml xml={item.svg} width={20} height={20} />
                      <Text
                        style={{
                          color: BaseColor.blackColor,
                          marginHorizontal: 8,
                          fontFamily: FontFamily.default,
                        }}
                      >
                        {item.title}
                      </Text>
                    </View>
                    <CustomIcon
                      name="expand-button"
                      color={BaseColor.blackColor}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            </TouchableOpacity>
          </Modal>

          <Modal
            transparent
            style={{ flex: 1 }}
            visible={refresh}
            animationType="fade"
          >
            <TouchableOpacity
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: BaseColor.white50,
              }}
              onPress={() => setRefresh(false)}
            >
              <View
                style={{
                  padding: 12,
                  backgroundColor: BaseColor.whiteColor,
                  borderRadius: 12,
                }}
              >
                <Text
                  style={{
                    fontFamily: FontFamily.default,
                    fontWeight: "bold",
                    marginBottom: 12,
                  }}
                >
                  Changing language
                </Text>
                <ActivityIndicator size="small" color={BaseColor.blueDark} />
              </View>
            </TouchableOpacity>
          </Modal>
        </LinearGradient>
      </ImageBackground> */}
    </>
  );
};

export default RedirectLS;
