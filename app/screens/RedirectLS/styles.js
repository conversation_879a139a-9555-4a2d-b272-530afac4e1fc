/* eslint-disable quotes */
import { Dimensions, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily, FontWeight } from "../../config/typography";

const { height: dHeight, width: dWidth } = Dimensions.get("window");

const styles = StyleSheet.create({
  // root: {
  //   flex: 1,
  // },
  // backImg: {
  //   flex: 1,
  //   height: dHeight,
  //   width: dWidth,
  // },
  // mainContainer: {
  //   flex: 1,
  //   height: dHeight,
  //   width: dWidth,
  //   padding: 16,
  // },
  // container: {
  //   flex: 1,
  //   justifyContent: "flex-end",
  // },
  // txtStyle: {
  //   marginTop: 60,
  //   color: BaseColor.whiteColor,
  //   fontSize: 36,
  //   fontWeight: "100",
  //   fontFamily: FontFamily.default,
  //   marginEnd: 64,
  // },
  // row: {
  //   flexDirection: "row",
  //   width: "100%",
  // },
  // loginBtn: {
  //   flex: 1,
  //   marginEnd: 8,
  //   borderWidth: 1,
  //   borderColor: BaseColor.white50,
  //   backgroundColor: "transparent",
  // },
  // signupBtn: {
  //   flex: 1,
  //   marginStart: 8,
  //   borderWidth: 1,
  //   backgroundColor: BaseColor.whiteColor,
  // },
  // loginTxt: {
  //   color: BaseColor.blackColor,
  // },
  // langBtn: {
  //   flexDirection: "row",
  //   alignItems: "center",
  //   alignSelf: "center",
  //   marginTop: 8,
  // },
  // modalCont: {
  //   backgroundColor: BaseColor.whiteColor,
  //   position: "absolute",
  //   bottom: 24,
  //   alignSelf: "center",
  //   borderRadius: 16,
  //   padding: 12,
  // },
  // flagDesign: {
  //   flexDirection: "row",
  //   alignItems: "center",
  //   justifyContent: "flex-start",
  //   minWidth: 140,
  // },
  root: {
    flex: 1,
  },
  backImg: {
    flex: 1,
    height: dHeight,
    width: dWidth,
  },
  mainContainer: {
    flex: 1,
    height: dHeight,
    width: dWidth,
    // padding: 16,
  },
  container: {
    flex: 1,
    justifyContent: "flex-end",
  },
  txtStyle: {
    // marginTop: 60,
    alignSelf: "center",
    color: BaseColor.whiteColor,
    fontSize: 20,
    fontWeight: FontWeight.semibbold,
    fontFamily: FontFamily.default,
    // marginEnd: 64,
  },
  row: {
    flexDirection: "row",
    width: "100%",
    // paddingTop: 35,
  },
  loginBtn: {
    flex: 1,
    marginStart: 8,
    borderWidth: 1,
    borderColor: BaseColor.cbtGradientColor,
    backgroundColor: BaseColor.cbtGradientColor,
    borderRadius: 26,
    height: 77,
  },
  signupBtn: {
    flex: 1,
    marginEnd: 8,
    borderWidth: 1,
    borderColor: BaseColor.cbtGradientColor,
    backgroundColor: BaseColor.whiteColor,
    borderRadius: 26,
    height: 77,
  },
  loginTxt: {
    color: BaseColor.whiteColor,
    fontWeight: "700",
    fontSize: 16,
    fontFamily: FontFamily.bold,
  },
  langBtn: {
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "center",
    marginTop: 8,
  },
  modalCont: {
    backgroundColor: BaseColor.whiteColor,
    position: "absolute",
    // bottom: 24,
    top: 0,
    alignSelf: "flex-start",
    borderRadius: 16,
    padding: 12,
    margin: 25,
  },
  flagDesign: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    minWidth: 140,
  },
  contentView: {
    // flex: 1,
    // position: "absolute",
    // bottom: 0,
    width: Dimensions.get("screen").width,
    height: 346,
    borderTopLeftRadius: 46,
    borderTopRightRadius: 46,
    paddingHorizontal: 34,
    // paddingVertical: 5,
    backgroundColor: BaseColor.whiteColor,
    // justifyContent: "flex-end",
  },
  titleText: {
    color: BaseColor.blackColor,
    fontSize: 24,
    // textAlign: 'center',
    alignItems: "flex-start",
    paddingBottom: 8,
    fontFamily: FontFamily.bold,
    fontWeight: "700",
    lineHeight: 32,
    marginTop: 30,
  },
  descriptionText: {
    color: BaseColor.blackColor,
    fontSize: 16,
    // textAlign: 'center',
    alignItems: "flex-start",
    fontFamily: FontFamily.default,
    lineHeight: 24,
  },
  backgroundVideo: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    height: '100%'
  },
});

export default styles;
