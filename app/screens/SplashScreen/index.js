import React, { useEffect } from "react";
import {
  BackHandler,
  Dimensions,
  Image,
  SafeAreaView,
  StatusBar,
  View,
} from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";
import { useDispatch, useSelector } from "react-redux";
import Toast from "react-native-simple-toast";
import BaseColor from "../../config/colors";
import BaseSetting from "../../config/setting";
import { getApiData } from "../../utils/apiHelper";
import AuthAction from "../../redux/reducers/auth/actions";
import GradientBack from "../../components/gradientBack";
import { sendErrorReport } from "../../utils/commonFunction";

const { height: dHeight, width: dWidth } = Dimensions.get("window");

const SplashScreen = ({ navigation }) => {
  const dispatch = useDispatch();

  const animation = useSharedValue({ width: 5000, height: 5000 });
  const walkthrough = useSelector((state) => state.auth.walkthrough);

  const accessToken = useSelector((state) => state.auth.accessToken);

  const animationStyle = useAnimatedStyle(() => ({
    width: withTiming(animation.value.width, {
      duration: 2000,
    }),

    height: withTiming(animation.value.height, {
      duration: 2000,
    }),
  }));

  useEffect(() => {
    animation.value = { width: 0, height: 0 };

    setTimeout(() => {
      if (walkthrough) {
        navigation.navigate("Walkthrough");
        return;
      } else if (accessToken) {
        navigation.navigate("DrawerNav");
      } else {
        navigation.navigate("RedirectLS");
      }
    }, 3000);
  }, [walkthrough, accessToken]);

  function handleBackButtonClick() {
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const getLanguageList = () => {
    getApiData(BaseSetting.endpoints.getLanguageList, "post", {})
      .then((response) => {
        console.log(".then -> response", response);
        if (response?.success) {
          dispatch(AuthAction.setLanguageList(response.data));
        }
      })
      .catch((err) => {
        console.log(".catch -> err", err);
        Toast.show("Something went wrong while getting lanugage list");
        sendErrorReport(err, "getting_language");
      });
  };

  useEffect(() => {
    getLanguageList();
  }, []);

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <GradientBack />
      <StatusBar
        backgroundColor="transparent"
        barStyle="dark-content"
        translucent
      />
      <Image
        source={require("../../assets/images/logo-1.png")}
        style={{ height: 80, width: 80 }}
      />
      <Animated.View
        style={[
          {
            backgroundColor: BaseColor.blue,
            position: "absolute",
            borderRadius: 5000,
          },
          animationStyle,
        ]}
      />
    </View>
  );
};

export default SplashScreen;
