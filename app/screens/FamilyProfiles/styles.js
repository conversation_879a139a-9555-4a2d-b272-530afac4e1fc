import { Dimensions, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  flatlistView: {
    marginTop: 30,
    marginBottom: 10,
  },
  addNewProfile: {
    height: 90,

    backgroundColor: BaseColor.whiteColor,
    justifyContent: "center",
    alignSelf: "center",
    marginRight: 16,
    marginLeft: 54,
    marginTop: 8,
    flex: 1,
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    borderRadius: 20,
  },
  addText: {
    textAlign: "center",
    fontSize: 20,
    color: BaseColor.blueDark,
    fontFamily: FontFamily.default,
  },
  tapText: {
    fontWeight: "400",
    fontSize: 16,
    fontFamily: FontFamily.default,
    lineHeight: 32,
    paddingVertical: 16,
  },

  checkIcon: {
    backgroundColor: BaseColor.whiteColor,
    width: 28,
    height: 28,
    borderRadius: 14,
    textAlign: "center",
    textAlignVertical: "center",
    marginRight: 10,
    justifyContent: "center",
    alignItems: "center",
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
  },

  image: {
    width: 60,
    height: 60,
    borderRadius: 12,
  },
  title: {
    color: BaseColor.whiteColor,
    fontSize: 24,
    fontWeight: "700",
    fontFamily: FontFamily.regular,
    lineHeight: 32,
  },
  connectedDevice: {
    color: "#004166",
    fontSize: 12,
    marginTop: 8,
    paddingBottom: 4,
    lineHeight: 16,
    fontFamily: FontFamily.default,
  },
});

export default styles;
