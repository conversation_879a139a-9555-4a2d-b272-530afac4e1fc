/* eslint-disable array-callback-return */
/* eslint-disable global-require */
/* eslint-disable quotes */
import React, { useEffect, useState } from "react";
import {
  FlatList,
  Text,
  View,
  Image,
  TouchableOpacity,
  BackHandler,
  RefreshControl,
  ActivityIndicator,
  Platform,
  ImageBackground,
  Alert
} from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome5";
import _, { sample } from "lodash";
import Toast from "react-native-simple-toast";
import { useDispatch, useSelector } from "react-redux";
import { useFocusEffect, useTheme } from "@react-navigation/native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";
import styles from "./styles";
import CHeader from "../../components/CHeader";
import GradientBack from "../../components/gradientBack";
import { translate } from "../../lang/Translate";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import {
  enableAnimateLinear,
  sendErrorReport,
} from "../../utils/commonFunction";
import AuthAction from "../../redux/reducers/auth/actions";
import bluetoothActions from "../../redux/reducers/bluetooth/actions";
import { FontFamily } from "../../config/typography";
import SettingsHeader from "../../components/CHeader/SettingsHeader";

let backPressed = 0;

/**
 *
 *@module FamilyProfiles
 *
 */
const FamilyProfiles = ({ navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const dispatch = useDispatch();
  const { setNotiCount } = AuthAction;
  const { setDeviceDetail } = bluetoothActions;

  const listItemAnim = useSharedValue(0);
  const opacityAnim = useSharedValue(0);

  const listItemAnimationStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(listItemAnim.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(opacityAnim.value, {
      duration: 1000,
    }),
  }));

  // useEffect(() => {
  //   opacityAnim.value = 1;
  //   listItemAnim.value = -10;
  // }, []);

  const [loader, setloader] = useState(true);

  const [refreshing, setRefreshing] = useState(false);

  const token = useSelector((state) => state.auth.accessToken);

  useFocusEffect(
    React.useCallback(() => {
      setloader(true);

      setDevices([]);
      if (token !== "") {
        getChildInfo();
        getBadgeCount();
      }

      setloader(false);
      //   opacityAnim.value = 1;
      //   listItemAnim.value = -300;
    }, [])
  );

  /** this function for get Badge Count
   * @function getBadgeCount
   * @param {object} data {}
   */
  async function getBadgeCount() {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getUserAlertCount,
        "POST",
        {},
        headers
      );

      if (response.success) {
        dispatch(setNotiCount(response));
      } else {
        console.log(
          "🚀 ~ file: index.js ~ line 15ewe ~ getBadgeCount ~ response",
          response
        );
      }
    } catch (error) {
      console.log("error for device list ===", error);
      sendErrorReport(error, "get_device_list");
    }
  }

  const onRefresh = React.useCallback(() => {
    setDevices([
      {
        type: "add",
      },
    ]);
    getChildInfo();
    setTimeout(() => {
      setloader(false);
      opacityAnim.value = 1;
      listItemAnim.value = -300;
    }, 2000);
  }, []);

  const [devices, setDevices] = useState([]);

  /** this function for get Child Information
   * @function getChildInfo
   * @param {object} data {}
   */
  const getChildInfo = () => {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    getApiData(
      BaseSetting.endpoints.getUserChild,
      "POST",
      {
        platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
      },
      headers
    )
      .then((response) => {
        if (response.success) {
          const tempArr = [];
          const childArr = response.data;
          dispatch(setDeviceDetail(response.data));
          childArr.map((item) => {
            tempArr.unshift(item);
          });
          console.log("1-- get child----", tempArr);
          setDevices(tempArr);
        } else {
          Toast.show(response.message);
        }
        setloader(false);
        setRefreshing(false);
      })
      .catch((err) => {
        // console.log("ERRR", err);
        //Toast.show("Something went wrong while getting child detail");
        sendErrorReport(err, "get_child_in_device3");
        setRefreshing(false);
      });
  };

  const img = require("../../assets/images/logo_b.png");

  const render = ({ item }) => {
    enableAnimateLinear();
    return (
      <View>
        {item.type === "add" ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => {
              navigation.navigate("ChildInfo", {
                type: "add",
              });
            }}
            style={[
              styles.addNewProfile,
              {
                backgroundColor: BaseColor.whiteColor,
                flex: 1,
                width: "78%",
                margin: 20,
              },
            ]}
          >
            <View
              style={{
                alignSelf: "center",
              }}
            >
              <Text style={[styles.addText, { color: BaseColor.blueDark }]}>
                {translate("addNew")}
              </Text>
              <Text style={[styles.tapText, { color: BaseColor.blackColor }]}>
                {translate("tapToAddNewProfile")}
              </Text>
            </View>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            activeOpacity={0.8}
            style={[
              {
                backgroundColor: BaseColor.whiteColor,
                minWidth: 181,
                marginRight: 4,
              },
            ]}
            onPress={() => {
              // navigation.navigate("CDeviceList", {
              //   deviceDetail: item.deviceDetails || [],
              //   title: item.nick_name || "",
              //   item,
              // });

              navigation.navigate("ChildInfo", {
                item: item,
                type: "edit",
                title: item.nick_name,
                backTitle: "My family",
              });
            }}
          >
            <ImageBackground
              source={require("../../assets/images/blueChildBg.png")}
              resizeMode="cover"
              style={{
                height: null,
                borderRadius: 30,
                overflow: "hidden",
              }}
            >
              <View
                style={{
                  padding: 20,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  <Image
                    style={styles.image}
                    source={{ uri: item.child_profile }}
                  />
                  <Text
                    style={{
                      fontWeight: "700",
                      fontSize: 14,
                      color: BaseColor.whiteColor,
                    }}
                  >
                    View
                  </Text>
                </View>
                <View style={{}}>
                  <Text
                    style={{
                      color: "#004166",
                      fontWeight: "700",
                      fontSize: 12,
                      marginTop: 37,
                      lineHeight: 16,
                    }}
                  >
                    {item.device_connection === "Active"
                      ? translate("activeAccount")
                      : translate("deactiveAccount") || ""}
                  </Text>
                  <Text style={[styles.title]} numberOfLines={1}>
                    {item.nick_name}
                  </Text>
                  <Text style={[styles.connectedDevice]}>
                    {`${item.device_count || 0} ${translate(
                      "deviceThreeText"
                    )}`}
                  </Text>
                </View>
              </View>
            </ImageBackground>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show("Press Again To Exit");
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  return (
    <View style={[styles.root, { backgroundColor: BaseColor.whiteColor }]}>
      {/* <GradientBack /> */}
      {/* <CHeader
        image={img}
        rightIconName="notifications-bell-button"
        leftIconName="settings-2"
        onLeftPress={() => {
          navigation.openDrawer();
        }}
        onRightPress={() => {
          navigation.navigate("Alerts");
        }}
      /> */}
      <SettingsHeader
        image
        title2={translate("My")}
        title={translate("familyProfiles")}
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
        titleTextColor={BaseColor.whiteColor}
        headerBackgroundColor={BaseColor.inputBackGroundColor}
        isViolateLogo
        backTitle="Settings"
      />
      {!loader ? (
        <View>
          <FlatList
            data={devices}
            renderItem={render}
            horizontal
            keyExtractor={(item, index) => index}
            contentContainerStyle={{
              paddingVertical: 8,
              paddingStart: 4,
            }}
            bounces
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          />
        </View>
      ) : (
        <ActivityIndicator size={42} color={BaseColor.whiteColor} style={{}} />
      )}
      <View
        style={{
          backgroundColor: BaseColor.inputBackGroundColor,
          height: 8,
          marginTop: 24,
        }}
      />
      <View>
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            navigation.navigate("ChildInfo", {
              type: "add",
            });
          }}
          style={{ paddingHorizontal: 20 }}
        >
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={[styles.tapText, { color: BaseColor.blackColor }]}>
              {translate("tapToAddNewProfile")}
            </Text>
            <FAIcon
              name="arrow-right"
              size={15}
              color={BaseColor.blackColor}
              style={{ marginRight: 0 }}
            />
          </View>
        </TouchableOpacity>
        <View
          style={{
            backgroundColor: BaseColor.inputBackGroundColor,
            height: 1,
            marginHorizontal: 20,
          }}
        />
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            navigation.navigate("QRScanner");
          }}
          style={{ paddingHorizontal: 20 }}
        >
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={[styles.tapText, { color: BaseColor.blackColor }]}>
              {translate("addNewDevice")}
            </Text>
            <FAIcon
              name="arrow-right"
              size={15}
              color={BaseColor.blackColor}
              style={{ marginRight: 0 }}
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default FamilyProfiles;
