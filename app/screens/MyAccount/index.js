/* eslint-disable max-len */
/* eslint-disable quotes */
import React, { useEffect, useRef, useState } from "react";
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  Modal,
  ScrollView,
  SafeAreaView,
  BackHandler,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
} from "react-native";
import CountryPicker from "react-native-country-picker-modal";
import LinearGradient from "react-native-linear-gradient";
import FAIcon from "react-native-vector-icons/FontAwesome";
import Toast from "react-native-simple-toast";
import { useDispatch, useSelector } from "react-redux";
import { findIndex, isArray, isEmpty, isNaN, isObject } from "lodash";
import { useTheme } from "@react-navigation/native";
import GetLocation from "react-native-get-location";
import DeviceInfo from "react-native-device-info";
import CButton from "../../components/CButton";
import CInput from "../../components/CInput/MyAccountInput";
import GradientBack from "../../components/gradientBack";
import styles from "./styles";
import { FontFamily } from "../../config/typography";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from "../../utils/commonFunction";
import AuthAction from "../../redux/reducers/auth/actions";
import { translate, initTranslate } from "../../lang/Translate";
import DropDown from "../../components/DropDown";
import BaseColors from "../../config/colors";
import { store } from "../../redux/store/configureStore";
import languageActions from "../../redux/reducers/language/actions";
import CHeader from "../../components/CHeader";
import { CustomIcon } from "../../config/LoadIcons";
import CAlert from "../../components/CAlert";
import BluetoothAction from "../../redux/reducers/bluetooth/actions";
import SettingsHeader from "../../components/CHeader/SettingsHeader";
/**
 *
 * @module MyAccount
 */
const MyAccount = ({ navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const userData = useSelector((state) => state.auth.userData);
  const brandToken = useSelector((state) => state.auth.brandToken);
  const accessToken = useSelector((state) => state.auth.accessToken);
  const langList = useSelector((state) => state.auth.langList);
  console.log("🚀 ~ file: index.js ~ line 54 ~ Signup ~ langList", langList);

  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);

  const [nameError, setNameError] = useState(false);
  const [lastNameError, setLastNameError] = useState(false);
  const [passwordError, setPasswordError] = useState(false);
  const [mailError, setMailError] = useState(false);
  const [numError, setNumError] = useState(false);
  const [langError, setLangError] = useState(false);

  const [nameErrorTxt, setnameErrorTxt] = useState("");
  const [lastNameErrorTxt, setlastNameErrorTxt] = useState("");
  const [passwordErrorTxt, setPasswordErrorTxt] = useState("");
  const [mailErrorTxt, setMailErrorTxt] = useState("");
  const [numErrorTxt, setNumErrorTxt] = useState("");
  const [langTxt, setLangTxt] = useState("");
  const [hideShow, setHideShow] = useState(true);

  const fNameRef = useRef();
  const lNameRef = useRef();
  const passwordRef = useRef();
  const emailRef = useRef();
  const pNumRef = useRef();

  const dispatch = useDispatch();
  const { setLanguage } = languageActions;

  const { setAccessToken, setUserData, setUserId, setUUid } = AuthAction;
  const { setLastDeviceId } = BluetoothAction;
  const [isEdit, setIsEdit] = useState(true);

  console.log("userdata----", userData);
  const [state, setstate] = useState({
    id: userData.id,
    fullName: userData.full_name,
    lastName: userData.last_name,
    password: userData.password,
    email: userData.email,
    pNum: userData.phone,
    country: userData.country,
    selectedCountry: userData.country_code, //"ES",
    selectedCountryName: userData.country, //"Spain",
    countryCode: userData.phone_code, // remove 91 //"34",
    agree: userData.is_verify, // false,
    // selLang: langList[0],
    // lat: null,
    // lng: null,
    state: userData.state, // "",
  });
  const [AlerModal1, setAlerModal1] = useState({
    visible: false,
    title: "",
    message: "",
  });
  const [btnLoader, setBtnLoader] = useState(false);

  useEffect(() => {
    getLocation();
  }, []);

  /** this function for get location
   * @function getLocation
   * @param {*} data {}
   */
  const getLocation = () => {
    const myApiKey = "AIzaSyCoDO-inDu4lc0k7iZeO9Y-ZzkFELknuwk";

    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 10000,
    })
      .then((location) => {
        console.log("Location====>>>>", location);

        fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?address=${location.latitude},${location.longitude}&key=${myApiKey}`
        )
          .then((response) => response.json())
          .then((responseJson) => {
            console.log(
              `ADDRESS GEOCODE is BACK!! => ${JSON.stringify(responseJson)}`
            );
            let stateV = "";
            const { address_components } = responseJson.results[0];
            address_components.map((item) => {
              if (item.types[0] === "administrative_area_level_1") {
                stateV = item.long_name;
              }
            });
            console.log(
              "bugaboo ~ file: index.js ~ line 105 ~ .then ~ state",
              stateV
            );
            setstate({
              ...state,
              lat: location.latitude,
              lng: location.longitude,
              state: stateV,
            });
          });
      })
      .catch((error) => {
        const { code, message } = error;
        console.warn(code, message);
        sendErrorReport(error, "get_location");
      });
  };

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const Validation = () => {
    // const passVal = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,15}$/;
    const numVal = /^[0-9]+$/;
    const emailVal =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    enableAnimateInEaseOut();

    if (state.fullName === "") {
      allErrorFalse();
      setNameError(true);
      // setnameErrorTxt("Please enter FullName");
      setnameErrorTxt(translate("enterName"));
    } else if (state.lastName === "") {
      allErrorFalse();
      setLastNameError(true);
      setlastNameErrorTxt(translate("enterLastName"));
      // } else if (state.password === "") {
      //   allErrorFalse();
      //   setPasswordError(true);
      //   setPasswordErrorTxt(translate("enterPasswrd"));
    } else if (state.email === "") {
      allErrorFalse();
      setMailError(true);
      setMailErrorTxt(translate("enterEmail"));
    } else if (!emailVal.test(String(state.email))) {
      allErrorFalse();
      setMailError(true);
      setMailErrorTxt(translate("enterEmailvalid"));
    } else if (state.pNum === "") {
      allErrorFalse();
      setNumError(true);
      setNumErrorTxt(translate("enterPhone"));
    } else if (
      !numVal.test(String(state.pNum)) ||
      state.pNum.length < 6 ||
      state.pNum.length > 12
    ) {
      allErrorFalse();
      setNumError(true);
      setNumErrorTxt(translate("enterPhonevalid"));
    } else if (!state.agree) {
      allErrorFalse();
      Toast.show(translate("accept"), Toast.SHORT, ["UIAlertController"]);
    } else {
      allErrorFalse();
      userUpdate();
    }
  };

  const allErrorFalse = () => {
    setNameError(false);
    setLastNameError(false);
    setNumError(false);
    setPasswordError(false);
    setMailError(false);
  };

  /** this function for user SignUp
   * @function userSignUp
   * @param {object} data full_name, password, email, phone, phone_code, country, token, language_id, latitude, longitude, state
   */
  const userUpdate = () => {
    const data = {
      user_id: state.id,
      full_name: state.fullName,
      last_name: state.lastName,
      // password: state.password,
      email: state.email,
      phone: state.pNum,
      phone_code: `${state.countryCode}`,
      country: state.selectedCountryName,
      country_code: state.selectedCountry,
      // token: brandToken,
      // language_id: state.selLang.id,
      // latitude: state.lat,
      // longitude: state.lng,
      // state: state.state,
    };
    console.log("data===obj===", data);
    getApiData(BaseSetting.endpoints.updateUser, "POST", data, {
      "Content-Type": "application/json",
      authorization: accessToken ? `Bearer ${accessToken}` : "",
    })
      .then((response) => {
        console.log(" ===response====", response);
        const uId =
          response && isObject(response.data) && response.data.id
            ? response.data.id
            : null;
        dispatch(setUserId(uId));
        if (response.success) {
          dispatch(setUserData(response?.data));
          Toast.show(response.message?.message || response.message);
          navigation.goBack();
        } else {
          Toast.show(response.message?.message || response.message);
        }
      })
      .catch((err) => {
        Toast.show("Something went wrong while update user");

        console.log("ERR====>>>>", err);
        sendErrorReport(err, "update_user");
      });
  };

  async function deleteAccount() {
    setBtnLoader(true);
    console.log("acces token" + accessToken);
    try {
      const response = await getApiData(
        BaseSetting.endpoints.deleteAccount,

        "POST",
        {},
        {
          "Content-Type": "application/json",
          authorization: accessToken ? `Bearer ${accessToken}` : "",
        }
      );
      if (response.success) {
        console.log("account removed ==>", response);
        setBtnLoader(false);
        dispatch(setAccessToken(""));
        dispatch(setUUid(""));
        navigation.closeDrawer();
        navigation.navigate("RedirectLS");
      }
    } catch (err) {
      setBtnLoader(false);
      console.log("ERRR==", err);
    }
  }

  return (
    <>
      <StatusBar barStyle="dark-content" />
      <View style={[styles.root, { backgroundColor: BaseColor.whiteColor }]}>
        <SettingsHeader
          title={translate("Account")}
          title2={"My"}
          // backBtn
          leftIconName="left-arrow"
          onLeftPress={() => {
            handleBackButtonClick();
          }}
          titleTextColor={BaseColor.whiteColor}
          headerBackgroundColor={BaseColor.inputBackGroundColor}
          isViolateLogo
          image
          backTitle="Settings"
        />
        <TouchableOpacity
          onPress={() => {
            setIsEdit(!isEdit);
            if (!isEdit) {
              if (state.id) {
                Validation();
              }
            }
          }}
          style={{ position: "absolute", right: 32, top: 96 }}
        >
          <Text style={{ color: BaseColor.whiteColor, fontWeight: "700" }}>
            {isEdit ? "Edit" : "Save"}
          </Text>
        </TouchableOpacity>
        <KeyboardAvoidingView
          behavior={Platform.OS == "ios" ? "padding" : null}
          showsVerticalScrollIndicator={false}
          style={styles.mainContainer}
        >
          <ScrollView
            contentContainerStyle={{ flexGrow: 1, padding: 24 }}
            showsVerticalScrollIndicator={false}
            bounces={false}
          >
            <View style={{ flex: 1 }}>
              <View style={styles.mainInputStyle}>
                <CInput
                  ref={fNameRef}
                  floatingPlaceholder
                  placeholder={translate("first Name")}
                  value={state.fullName}
                  onChangeText={(val) => {
                    setstate({ ...state, fullName: val });
                  }}
                  placeholderTextColor={BaseColors.textGrey}
                  // iconName="user2"
                  onSubmitEditing={() => {
                    lNameRef.current.focus();
                  }}
                  showError={nameError}
                  errorMsg={nameErrorTxt}
                  hideLeftIcon
                  iconName="pencil"
                  hideRightIcon={isEdit}
                  editable={!isEdit}
                  iconColor={"black"}
                />
                <CInput
                  ref={lNameRef}
                  floatingPlaceholder
                  placeholder={translate("last Name")}
                  value={state.lastName}
                  onChangeText={(val) => {
                    setstate({ ...state, lastName: val });
                  }}
                  placeholderTextColor={BaseColors.textGrey}
                  onSubmitEditing={() => {
                    passwordRef.current.focus();
                  }}
                  textInputWrapper={{
                    marginTop: 8,
                  }}
                  showError={lastNameError}
                  errorMsg={lastNameErrorTxt}
                  hideLeftIcon
                  iconName="pencil"
                  hideRightIcon={isEdit}
                  editable={!isEdit}
                  iconColor={"black"}
                />

                <CInput
                  ref={pNumRef}
                  floatingPlaceholder
                  placeholder={translate("forgotInput")}
                  value={state.pNum}
                  onChangeText={(val) => {
                    setstate({ ...state, pNum: val });
                  }}
                  placeholderTextColor={BaseColors.textGrey}
                  hideLeftIcon
                  textInputWrapper={{
                    marginTop: 8,
                  }}
                  keyboardType="number-pad"
                  onSubmitEditing={() => {
                    Keyboard.dismiss();
                  }}
                  showError={numError}
                  errorMsg={numErrorTxt}
                  iconName="pencil"
                  hideRightIcon={isEdit}
                  editable={!isEdit}
                  iconColor={"black"}
                />
                <CInput
                  ref={emailRef}
                  floatingPlaceholder
                  placeholder={translate("emailId")}
                  value={state.email}
                  onChangeText={(val) => {
                    setstate({ ...state, email: val });
                  }}
                  placeholderTextColor={BaseColors.textGrey}
                  hideLeftIcon
                  textInputWrapper={{
                    marginTop: 8,
                  }}
                  keyboardType="email-address"
                  onSubmitEditing={() => {
                    pNumRef.current.focus();
                  }}
                  showError={mailError}
                  errorMsg={mailErrorTxt}
                  iconName="pencil"
                  hideRightIcon={isEdit}
                  editable={!isEdit}
                  iconColor={"black"}
                />
                <View style={[styles.countryPickerStyle]}>
                  <View>
                    <View>
                      <Text
                        style={{
                          fontFamily: FontFamily.bold,
                          fontSize: 12,
                          fontWeight: "700",
                          marginTop: 2,
                          color: "black",
                        }}
                      >
                        {translate("country")}
                      </Text>
                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "space-between",
                          marginTop: -5,
                        }}
                      >
                        <CountryPicker
                          {...{
                            countryCode: state.selectedCountry || "ES",
                            withFilter: true,
                            withFlag: true,
                            // renderFlagButton: false,
                            withCountryNameButton: true,
                            withAlphaFilter: true,
                            withCallingCode: true,
                            withEmoji: true,
                            disabled: true,
                            onSelect: (val) => {
                              setstate({ ...state, pNum: "" });
                              setstate({
                                ...state,
                                selectedCountry: val.cca2,
                                selectedCountryName: val.name,
                                countryCode: val.callingCode[0],
                              });
                            },
                            theme: {
                              fontSize: 16,
                              onBackgroundTextColor: BaseColor.blackColor,
                              primaryColor: BaseColor.alertRed,
                              backgroundColor: BaseColor.whiteColor,
                              filterPlaceholderTextColor: BaseColor.blackColor,
                            },
                          }}
                          visible={state.selectedCountry}
                        />
                      </View>
                    </View>
                  </View>
                </View>
                <CInput
                  ref={passwordRef}
                  floatingPlaceholder
                  placeholder={translate("loginPassword")}
                  value={state.password}
                  onChangeText={(val) => {
                    setstate({ ...state, password: val });
                  }}
                  placeholderTextColor={BaseColors.textGrey}
                  textInputWrapper={{
                    marginTop: 8,
                  }}
                  secureTextEntry={hideShow}
                  onShowPasswordpress={() => {
                    setHideShow(!hideShow);
                  }}
                  onSubmitEditing={() => {
                    emailRef.current.focus();
                  }}
                  showError={passwordError}
                  errorMsg={passwordErrorTxt}
                  hideLeftIcon
                  hideRightIcon={false}
                  iconName={hideShow ? "eye-slash" : "eye"}
                  editable={false}
                  iconColor={"black"}
                />
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate("UpdatePassword", { type: "settings" });
                  }}
                  style={{
                    marginTop: 8,
                    marginBottom: 40,
                  }}
                >
                  <Text style={{ color: BaseColor.blackColor }}>
                    Change password
                  </Text>
                </TouchableOpacity>

                <View
                  style={{
                    borderRadius: 12,
                    height: 64,
                    //borderWidth: 1,
                    //borderColor: BaseColor.btnBorderColor,
                    //justifyContent: "center",
                  }}
                >
                  <CButton
                    titleStyle={{ color: BaseColor.whiteColor }}
                    title={translate("deleteAccount")}
                    anim
                    playAnimation={anim}
                    backAnim={backAnim}
                    onPress={() => {
                      setAlerModal1({
                        visible: true,
                        title: "Delete Account",
                        message:
                          "Are you sure you want to delete your account?",
                      });
                      const { setLeadId } = AuthAction;
                      dispatch(setLeadId(null));
                    }}
                    style={{ flex: 1, backgroundColor: BaseColor.blackColor }}
                    loader={loader}
                    // done={done}
                  />
                </View>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
        <CAlert
          visible={AlerModal1.visible}
          onRequestClose={() =>
            setAlerModal1({
              ...AlerModal1,
              visible: false,
            })
          }
          onCancelPress={() =>
            setAlerModal1({
              ...AlerModal1,
              visible: false,
            })
          }
          loader={btnLoader}
          onOkPress={async () => {
            await deleteAccount();
            dispatch(setUserData({}));
            dispatch(setUserId(""));
            dispatch(setLastDeviceId(""));
          }}
          alertTitle={AlerModal1.title}
          alertMessage={AlerModal1.message}
          agreeTxt={translate("delete")}
        />
      </View>
    </>
  );
};

export default MyAccount;
