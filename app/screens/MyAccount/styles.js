import { Dimensions, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";

const { height: dHeight, width: dWidth } = Dimensions.get("window");

const styles = StyleSheet.create({
  root: {
    flex: 1,
    // backgroundColor: BaseColor.whiteColor,
    backgroundColor: "white",
  },
  mainContainer: {
    flex: 1,
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: "flex-end",
    position: "absolute",
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    top: 34,
    right: 12,
  },
  loginBtn: {
    borderRadius: 12,
    height: 64,
    borderWidth: 1,
    borderColor: BaseColor.btnBorderColor,
  },
  mainInputStyle: {
    // flex: 1,
    // justifyContent: "center",
    // backgroundColor: Bs,
  },
  loginText: {
    fontSize: 28,
    color: BaseColor.whiteColor,
    // fontWeight: '700',
    fontFamily: FontFamily.default,
  },
  loginTextView: {
    marginTop: 40,
    marginBottom: 20,
    alignItems: "center",
    fontFamily: FontFamily.default,
  },
  rememberText: {
    fontSize: 16,
    fontWeight: "400",
    fontFamily: FontFamily.default,
    color: BaseColor.blackColor,
    marginStart: 8,
    lineHeight: 24,
  },
  countryPickerStyle: {
    height: 65,
    paddingLeft: 20,
    // borderWidth: 1,
    backgroundColor: BaseColor.inputBackGroundColor,
    // borderColor: BaseColor.whiteColor,
    borderRadius: 10,
    // paddingVertical: 8,
    justifyContent: "center",
    marginTop: 8,
  },
});

export default styles;
