/* eslint-disable quotes */
import { StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  cardRoot: {
    backgroundColor: BaseColor.whiteColor,
    marginVertical: 6,
    marginHorizontal: 8,
    borderRadius: 8,
    padding: 12,
    paddingHorizontal: 16,
  },
  imgStyle: {
    height: 50,
    width: 50,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: BaseColor.black60,
  },
  divider: {
    height: 1,
    backgroundColor: BaseColor.black40,
    marginVertical: 12,
  },
  rowStyle: {
    flexDirection: "row",
  },
  nameStyle: {
    fontFamily: FontFamily.default,
    fontWeight: "bold",
    fontSize: 16,
  },
  catStyle: {
    color: BaseColor.black70,
    fontFamily: FontFamily.default,
    fontSize: 14,
  },
  subTitle: {
    fontFamily: FontFamily.default,
    fontSize: 14,
    fontWeight: "bold",
  },
  valueStyle: {
    fontFamily: FontFamily.default,
    fontSize: 14,
  },
  emptyComponent: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
});

export default styles;
