/* eslint-disable quotes */
import { useTheme } from "@react-navigation/native";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome";
import Toast from "react-native-simple-toast";
import { useSelector } from "react-redux";
import { flattenDeep, isArray } from "lodash";
import CButton from "../../components/CButton";
import CHeader from "../../components/CHeader";
import DropDown from "../../components/DropDown";
import GradientBack from "../../components/gradientBack";
import BaseSetting from "../../config/setting";
import { FontFamily } from "../../config/typography";
import { translate } from "../../lang/Translate";
import { getApiData } from "../../utils/apiHelper";
import styles from "./styles";
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from "../../utils/commonFunction";

export default function Products({ navigation }) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const token = useSelector((state) => state.auth.accessToken);
  const brandToken = useSelector((state) => state.auth.brandToken);

  const [filterModal, setfilterModal] = useState(false);
  const [selectedCat, setselectedCat] = useState("");

  const [productList, setproductList] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  const [pageLoad, setPageLoad] = useState(true);
  const [page, setPage] = useState(1);
  const [nextPage, setNextPage] = useState(false);
  const [nextLoading, setNextLoading] = useState(false);
  const [categoryList, setCategoryList] = useState([]);

  useEffect(() => {
    setPage(1);
    setproductList([]);
    getProductList();
    getProductCategories();
  }, []);

  const getProductList = (type) => {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    // console.log(page);

    const data = {
      token: brandToken,
      per_page: 50,
      page,
      category_id: type === "reset" ? null : selectedCat?.id || null,
    };
    console.log("getProductList -> data", data);

    getApiData(BaseSetting.endpoints.productList, "POST", data, headers)
      .then((response) => {
        console.log(".then -> response", response);
        if (response.success) {
          const tempPArr = flattenDeep([
            type === "filter" || type === "reset" ? [] : productList,
            response.data,
          ]);

          setproductList(tempPArr);
          setPageLoad(false);
          if (response?.next_enable === 1) {
            setNextPage(true);
          } else {
            setNextPage(false);
          }
          setNextLoading(false);
        } else {
          Toast.show(response.message);
        }
      })
      .catch((err) => {
        console.log("ERRR", err);
        Toast.show("Something went wrong while getting product list");
        sendErrorReport(err, "get_product_list");
      });
  };

  // this function for get product categories
  async function getProductCategories() {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getProductCategories,
        "POST",
        {},
        headers
      );

      if (response.success) {
        if (isArray(response.data)) {
          setCategoryList(response.data);
        }
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      console.log("error ===", error);
      sendErrorReport(error, "get_prod_cat");
    }
  }

  const onEndReached = () => {
    setNextLoading(true);

    if (nextPage) {
      const tempPage = page + 1;

      setPage(tempPage);

      getProductList();
    }
  };

  const onRefresh = React.useCallback(() => {
    // setproductList([]);
    getProductList();
  }, []);

  const renderListFooter = () => {
    if (!nextPage) {
      return (
        <Text
          style={{
            width: "100%",
            textAlign: "center",
            textAlignVertical: "center",
            height: 30,
            color: BaseColor.whiteColor,
          }}
        >
          No more products
        </Text>
      );
    }
    if (nextLoading) {
      return (
        <ActivityIndicator
          style={{ color: BaseColor.whiteColor, height: 60 }}
          color={BaseColor.whiteColor}
        />
      );
    }
    // return <ActivityIndicator style={{ color: BaseColor.whiteColor }} />;
    return null;
  };

  async function getProductView(id, type) {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.addproductaction,
        "POST",
        {
          product_id: id,
          type,
        },
        headers
      );

      if (response.success) {
        // setPostLike(response.data);
        // Toast.show(type);
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      sendErrorReport(error, "get_feed_post");
      console.log("feed post error ===", error);
    }
  }

  const renderProducts = ({ item, index }) => (
    <TouchableOpacity
      style={[styles.cardRoot, { backgroundColor: BaseColor.whiteColor }]}
      onPress={() => {
        getProductView(item.id, "viewed");
        navigation.navigate("ProductDetail", { productDetail: item });
      }}
    >
      <View style={styles.rowStyle}>
        <Image
          source={
            item?.product_image
              ? { uri: item?.product_image }
              : require("../../assets/images/app_logo.png")
          }
          style={[styles.imgStyle, { borderColor: BaseColor.black60 }]}
        />
        <View
          style={{ marginStart: 12, justifyContent: "space-around", flex: 1 }}
        >
          <Text style={[styles.nameStyle, { color: BaseColor.blackColor }]}>
            {item?.product_name}
          </Text>
          <Text style={[styles.catStyle, { color: BaseColor.black70 }]}>
            {item?.category_name}
          </Text>
        </View>
        <FAIcon
          name="angle-right"
          size={24}
          color={BaseColor.blackColor}
          style={{ alignSelf: "center" }}
        />
      </View>
      <View style={[styles.divider, { backgroundColor: BaseColor.black40 }]} />
      <View style={{ ...styles.rowStyle, justifyContent: "space-between" }}>
        <View style={{ ...styles.rowStyle }}>
          <Text style={[styles.subTitle, { color: BaseColor.blackColor }]}>
            SKU :
          </Text>
          <Text style={[styles.valueStyle, { color: BaseColor.blackColor }]}>
            {item?.sku_code}
          </Text>
        </View>
        <View style={{ ...styles.rowStyle, alignItems: "center" }}>
          <FAIcon
            name="dollar"
            size={14}
            color={BaseColor.green}
            style={{ marginEnd: 4 }}
          />
          <Text style={{ ...styles.valueStyle, color: BaseColor.green }}>
            {item?.sell_price}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  // enableAnimateInEaseOut();

  return (
    <View style={styles.root}>
      <GradientBack />
      <View style={styles.root}>
        <CHeader
          title={translate("products")}
          backBtn
          onLeftPress={() => {
            navigation.goBack();
          }}
          rightIconName="filter"
          onRightPress={() => {
            setfilterModal(true);
          }}
        />

        <FlatList
          keyExtractor={(item, index) => index}
          data={productList}
          renderItem={renderProducts}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 24 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onEndReachedThreshold={0.4}
          onEndReached={onEndReached}
          ListFooterComponent={renderListFooter}
          ListEmptyComponent={() => (
            <View style={styles.emptyComponent}>
              {pageLoad ? (
                <ActivityIndicator color={BaseColor.whiteColor} />
              ) : (
                <Text
                  style={{
                    // padding: 8,
                    // width: "100%",
                    fontSize: 16,
                    color: BaseColor.whiteColor,
                    textAlign: "center",
                  }}
                >
                  {translate("noProducts")}
                </Text>
              )}
            </View>
          )}
        />
      </View>
      <Modal
        style={{ flex: 1 }}
        visible={filterModal}
        transparent
        animationType="slide"
        onRequestClose={() => {
          setfilterModal(false);
        }}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: BaseColor.black40,
            justifyContent: "flex-end",
          }}
          onPress={() => {
            setfilterModal(false);
          }}
        >
          <View
            style={{
              backgroundColor: BaseColor.blueDark,
              padding: 24,
              borderTopEndRadius: 16,
              borderTopStartRadius: 16,
            }}
          >
            <Text
              style={{
                color: BaseColor.whiteColor,
                fontSize: 16,
                marginVertical: 8,
                fontFamily: FontFamily.default,
                fontWeight: "bold",
              }}
            >
              FILTER
            </Text>
            <DropDown
              placeholder="Select Category"
              data={categoryList}
              style={{ borderRadius: 12, marginEnd: 4 }}
              valueProp="category_name"
              onSelect={(val) => {
                // setHeight(val);
                setselectedCat(val);
              }}
              selectedObject={selectedCat}
            />
            <View style={{ flexDirection: "row", marginBottom: 16 }}>
              <CButton
                title="SEARCH"
                style={{
                  backgroundColor: BaseColor.whiteColor,
                  borderRadius: 8,
                  marginTop: 16,
                  marginEnd: 4,
                  flex: 1,
                }}
                titleStyle={{
                  color: BaseColor.whiteColor,
                  fontWeight: "bold",
                }}
                onPress={() => {
                  // navigation.navigate('Login');
                  setfilterModal(false);
                  setproductList([]);
                  setPage(1);
                  setPageLoad(true);
                  getProductList("filter");
                }}
              />
              <CButton
                title="RESET"
                style={{
                  backgroundColor: BaseColor.orange,
                  borderRadius: 8,
                  marginTop: 16,
                  marginStart: 4,
                  flex: 1,
                }}
                titleStyle={{
                  color: BaseColor.whiteColor,
                  fontWeight: "bold",
                }}
                onPress={() => {
                  setselectedCat({});
                  setPage(1);
                  getProductList("reset");
                  // setfilterModal(false);
                  // navigation.navigate('Login');
                }}
              />
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}
