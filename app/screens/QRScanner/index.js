/* eslint-disable max-len */
/* eslint-disable no-console */
/* eslint-disable quotes */
/**
 * Sample BLE React Native App
 *
 * @format
 * @flow strict-local
 */

import React, { useState, useEffect } from "react";
import {
  View,
  NativeModules,
  NativeEventEmitter,
  Platform,
  PermissionsAndroid,
  BackHandler,
  Text,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Alert,
} from "react-native";
import QRCodeScanner from "react-native-qrcode-scanner";
import { find, isEmpty, isObject, map } from "lodash";
import BleManager from "react-native-ble-manager";
import BluetoothStateManager from "react-native-bluetooth-state-manager";
import { useDispatch, useSelector } from "react-redux";
import Toast from "react-native-simple-toast";
import styles from "./styles";
import CHeader from "../../components/CHeader";
import GradientBack from "../../components/gradientBack";
import { translate } from "../../lang/Translate";
import BluetoothAction from "../../redux/reducers/bluetooth/actions";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import { sendErrorReport } from "../../utils/commonFunction";
import { openSettings } from "react-native-permissions";

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

const QRScanner = ({ navigation }) => {
  const dispatch = useDispatch();
  const token = useSelector((state) => state.auth.accessToken);
  const [isScanning, setIsScanning] = useState(false);
  const peripherals = new Map();
  const [list, setList] = useState([]);

  // const [connnectedID, setconnnectedID] = useState("");
  const [isRefreshing, setisRefreshing] = useState(false);
  const [refresh, setrefresh] = useState(false);
  const [deviceId, setDeviceId] = useState(null);
  // const [bluetoothStatus, setbluetoothStatus] = useState(false);

  const startScan = () => {
    if (!isScanning) {
      BleManager.scan([], 3, true)
        .then((results) => {
          console.log("Scanning...", results);
          setIsScanning(true);
          setrefresh(false);
        })
        .catch((err) => {
          setrefresh(false);
          console.error(err);
          sendErrorReport(err, "scan_error");
        });
    }
  };

  useEffect(() => {
    setTimeout(() => {
      startScan();
    }, 1500);
  }, []);

  const handleStopScan = () => {
    console.log("Scan is stopped");
    setIsScanning(false);
    setisRefreshing(false);
  };

  const handleDiscoverPeripheral = (peripheral) => {
    console.log("Got ble peripheral", peripheral);
    if (!peripheral.name) {
      peripheral.name = "NO NAME";
    }
    peripherals.set(peripheral.id, peripheral);

    setList(Array.from(peripherals.values()));
  };

  BluetoothStateManager.onStateChange((bluetoothState) => {
    // console.log(
    //   "BluetoothStateManager.onStateChange -> bluetoothState",
    //   bluetoothState
    // );
    // do something...
    switch (bluetoothState) {
      case "Unknown":
      case "Resetting":
      case "Unsupported":
      case "Unauthorized":
      case "PoweredOff":
        Toast.show("Please turn on your bluetooth");
      case "PoweredOn":
        console.log("ON ==== ...... ******");
        // startScan();
        break;
      default:
        break;
    }
  }, true /*= emitCurrentState */);

  useEffect(() => {
    BluetoothStateManager.getState().then((bluetoothState) => {
      switch (bluetoothState) {
        case "Unknown":
        case "Resetting":
        case "Unsupported":
        case "Unauthorized":
        case "PoweredOff":
          if (Platform.OS == "android") {
            BluetoothStateManager.requestToEnable().then((result) => {
              console.log(
                "BluetoothStateManager.requestToEnable -> result",
                result
              );
              // result === true -> user accepted to enable bluetooth
              // result === false -> user denied to enable bluetooth
            });
          } else {
            BluetoothStateManager.openSettings();
          }
          break;
        case "PoweredOn":
          console.log("ON ==== ...... ******");
          // startScan();
          break;
        default:
          break;
      }
    });
  }, []);

  useEffect(() => {
    /* Listening to IOS Background events as per the docs - Not Tested */
    bleManagerEmitter.addListener(
      "BleManagerCentralManagerWillRestoreState",
      (data) => {
        console.log(
          "BLE ==> BleManagerCentralManagerWillRestoreState ===> ",
          data
        );
      }
    );

    bleManagerEmitter.addListener(
      "BleManagerDiscoverPeripheral",
      handleDiscoverPeripheral
    );
    bleManagerEmitter.addListener("BleManagerStopScan", handleStopScan);

    if (Platform.OS === "android" && Platform.Version >= 23) {
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      ).then((result) => {
        if (result) {
          console.log("Permission is OK");
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
          ).then((res) => {
            if (res) {
              console.log("User accept");
            } else {
              console.log("User refuse");
            }
          });
        }
      });
    }

    return () => {
      console.log("unmount");
      // bleManagerEmitter.removeListener(
      //   'BleManagerDiscoverPeripheral',
      //   handleDiscoverPeripheral
      // );
      // bleManagerEmitter.removeListener('BleManagerStopScan', handleStopScan);
      // bleManagerEmitter.removeListener(
      //   'BleManagerDisconnectPeripheral',
      //   handleDisconnectedPeripheral
      // );
      // bleManagerEmitter.removeListener(
      //   'BleManagerDidUpdateValueForCharacteristic',
      //   handleUpdateValueForCharacteristic
      // );
    };
  }, []);

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  const readData = async (item) => {
    // console.log("ddddddddd=========", item);
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getDevice,
        "POST",
        {
          device_bluetooth_name: item.name, //advertising.localName, // manndjdjfjdjfnfmansi chanfe name-------
          product_id: item?.id,
        },
        headers
      );
      console.log("get device=====", response);
      if (response.success && !isEmpty(response.data)) {
        console.log("DISPATCH 5");
        setDeviceId(response?.data?.id);
        dispatch(BluetoothAction.setConnectedDeviceDetail(response?.data));

        // dispatch(BluetoothAction.setConnectedDeviceId(response?.data?.id));
        // dispatch(BluetoothAction.setConnectedDeviceName(e.data));
        if (isObject(item) && !isEmpty(item)) {
          console.log("DISPATCH 5-2");
          dispatch(BluetoothAction.setDeviceID(""));
          setTimeout(() => {
            console.log("DISPATCH 5-3");

            dispatch(BluetoothAction.setLastDeviceId(item.id));
            dispatch(BluetoothAction.setDeviceID(item.id));
            navigation.navigate("Connect", {
              product_id: item?.id,
              device_id: response.data.id || deviceId,
              product_id: item?.id,
            });
          }, 500);
        } else {
          setIsScanning(false);

          Toast.show("Can't find any device. Please try again");
          sendErrorReport(
            JSON.stringify({
              item,
              responseFromApi: response.data,
            }),
            "on_read_qr_cant_find_2"
          );

          startScan();
        }
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      console.log("error device detail ===", error);
      sendErrorReport(error, "on_read_qr");
    }
  };

  const renderItem = ({ item }) => (
    // console.log("renderItem -> item", list);
    <View
      style={{
        padding: 12,
        borderRadius: 8,
        backgroundColor: "#fff",
        shadowColor: "#000",
        margin: 8,
        sshadowColor: "#000",
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }}
    >
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginBottom: 8,
        }}
      >
        {/* <Text style={{ fontSize: 18, fontWeight: "700" }}>NAME : </Text> */}
        <Text
          style={{
            fontSize: 18,
            marginStart: 8,
            flex: 1,
            fontWeight: "700",
          }}
        >
          {item?.name == "NO NAME" ? "N/A" : item?.name}
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor:
              item?.advertising?.isConnectable == 1 ? "green" : "green",
            padding: 8,
            borderRadius: 8,
          }}
          activeOpacity={0.7}
          onPress={() => {
            readData(item);
            // navigation.navigate('Dashboard');
          }}
        >
          <Text style={{ color: "#fff" }}>CONNECT</Text>
        </TouchableOpacity>
      </View>
      <View style={{ flexDirection: "row" }}>
        {/* <Text style={{ fontSize: 18, fontWeight: "700" }}>Sevice ID: </Text> */}
        <Text style={{ fontSize: 15, marginStart: 8, flex: 1 }}>
          {item?.id}
        </Text>
      </View>
    </View>
  );

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const onRefresh = () => {
    setisRefreshing(true);
    BleManager.stopScan().then(() => {
      // Success code
      console.log("Scan stopped");
    });
    setTimeout(() => {
      startScan();
    }, 1000);
  };

  const onReadQR = async (e) => {
    // console.log("QRRESPOSE===>>>>>>", e.data);
    // console.log("🚀 ~ file: index.js ~ line 670 ~ onReadQR ~ list", list);

    if (e.data === "") {
      Toast.show("Invalid QR Code.Please try again.");
      return;
    }
    const availableDevices = [];
    list.map((d) => availableDevices.push(d.name));

    let data = find(
      list,
      (lt) => lt.name.toLowerCase() === e.data.toLowerCase()
    );

    if (!data) {
      //! ----Check if Already Connected to same device in that case
      BleManager.getConnectedPeripherals([]).then((peripheralsArray) => {
        if (!isEmpty(peripheralsArray)) {
          console.log(
            "QR BleManager.getConnectedPeripherals ~ peripheralsArray",
            peripheralsArray
          );
          const isAlreadyConnected = find(
            peripheralsArray,
            (lt) => lt.name.toLowerCase() === e.data.toLowerCase()
          );
          console.log("QR ~ isAlreadyConnected", isAlreadyConnected);
          if (isAlreadyConnected) {
            data = isAlreadyConnected;
          }
        }
      });
    }

    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getDevice,
        "POST",
        {
          device_bluetooth_name: e.data,
        },
        headers
      );

      if (response.success && !isEmpty(response.data)) {
        console.log("DISPATCH 6 - 1");
        setDeviceId(response?.data?.id);
        dispatch(BluetoothAction.setConnectedDeviceDetail(response?.data));
        // dispatch(BluetoothAction.setConnectedDeviceId(response?.data?.id));
        // dispatch(BluetoothAction.setConnectedDeviceName(e.data));
        if (isObject(data) && !isEmpty(data)) {
          console.log("DISPATCH 6 - 2");
          dispatch(BluetoothAction.setDeviceID(""));
          setTimeout(() => {
            console.log("DISPATCH 6 - 3");
            dispatch(BluetoothAction.setLastDeviceId(data.id));
            dispatch(BluetoothAction.setDeviceID(data.id));
          }, 500);
          setTimeout(() => {
            navigation.navigate("Connect", {
              product_id: data?.id,
              device_id: response.data.id || deviceId,
            });
          }, 2000);
        } else {
          setIsScanning(false);
          Toast.show("Can't find any device. Please try again");
          sendErrorReport(
            JSON.stringify({
              data,
              responseFromApi: response.data,
              availableDevices,
            }),
            "on_read_qr_cant_find"
          );
          startScan();
        }
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      console.log("error device detail ===", error);
      sendErrorReport(error, "on_read_qr");
    }
  };

  const NoPermissionViewIos = (
    <View
      style={{
        width: Dimensions.get("window").width * 0.8,
        alignSelf: "center",
      }}
    >
      <Text style={styles.qrTextStyle}>{translate("noCameraAcces")}</Text>
      <TouchableOpacity onPress={() => openSettings()}>
        <Text style={styles.openSettingsText}>{translate("openSettings")}</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.root}>
      <GradientBack />
      <CHeader
        title={translate("addDeviceTitle")}
        backBtn
        leftIconName
        onLeftPress={() => {
          navigation.goBack();
        }}
      />

      {/* <View style={styles.mainView}> */}
      {/* <QRCodeScanner
          showMarker
          cameraProps={{ ratio: '4:3' }}
          onRead={onRead}
          customMarker={(
            <View style={{
              flex: 1, justifyContent: FORTAB ? 'flex-start' : 'center', alignItems: 'center'
            }}
            >
              <View style={{ ...styles.leftAndRightOverlay }} />
              <View style={styles.rectangle}>
                <MaterialCommunityIcons
                  name="qrcode-scan"
                  size={WIDTH * 0.45}
                  color={BaseColor.blueLight}
                />
                <Animatable.View
                  style={styles.scanBar}
                  direction="alternate-reverse"
                  iterationCount="infinite"
                  duration={500}
                  easing="linear"
                  animation={makeSlideOutTranslation(
                    'translateY',
                    WIDTH * -0.45
                  )}
                />
              </View>
              <View style={styles.leftAndRightOverlay} />
            </View>
          )}
        /> */}

      <View style={{ flex: 1 }}>
        <FlatList
          data={list}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ flexGrow: 1 }}
          onRefresh={onRefresh}
          refreshing={isRefreshing}
          ListEmptyComponent={() => (
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text style={{ fontWeight: "bold", color: "#fff" }}>
                No Device Available
              </Text>
            </View>
          )}
        />
      </View>

      {/* <View style={{ flex: 1 }}>
        <QRCodeScanner
          showMarker
          onRead={onReadQR}
          reactivate
          reactivateTimeout={5000}
          cameraProps={{ height: Dimensions.get("window").height }}
          notAuthorizedView={NoPermissionViewIos}
          // flashMode={RNCamera.Constants.FlashMode.torch}
        />
      </View> */}
      <View style={styles.bottomViewStyle}>
        {/* <View style={styles.scanDescViewStyle}>
          <Text style={styles.qrTextStyle}>{translate("scanQRText")}</Text>
        </View> */}
        <View style={styles.linkViewStyle}>
          <Text style={styles.linkTextStyle}>{translate("qrLinkText")}</Text>
        </View>
      </View>
      {/* <TouchableOpacity
          style={styles.discriptionTextView}
          onPress={() => {
            // startScan();
            onReadQR({
              data:
                Platform.OS == 'ios'
                  ? '01A87436-72E9-3036-B666-64774EA8D7B1'
                  : 'AC:67:B2:36:4F:66',
            });
          }}
        >
          <Text style={styles.discriptionText}>
            {translate('qrScannerText')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
        activeOpacity={0.7}
        style={styles.nonSPTextView}
        onPress={() => {
          readData();
        }}
        >
          <Text style={styles.nonSPText}>{translate('qrScannerLink')}</Text>
        </TouchableOpacity> */}
      {/* </View> */}
    </View>
  );
};

export default QRScanner;
