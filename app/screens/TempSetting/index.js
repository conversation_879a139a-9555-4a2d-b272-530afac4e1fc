/* eslint-disable quotes */
/* eslint-disable no-unused-expressions */
import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  BackHandler,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  StatusBar,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useTheme } from "@react-navigation/native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import AuthAction from "../../redux/reducers/auth/actions";
import CHeader from "../../components/CHeader";
import TimeTabs from "../../components/TimeTabs";
import BluetoothAction from "../../redux/reducers/bluetooth/actions";
import DropDown from "../../components/CdropDown";
import SettingsHeader from "../../components/CHeader/SettingsHeader";

/**
 *
 *@module TempSetting
 */
function TempSetting({ navigation }) {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const { setIsFarenheit } = AuthAction;
  const { setIsConvert, setHighAlert } = BluetoothAction;
  const { isFarenheit } = useSelector((state) => state.auth);
  const { isConvert } = useSelector((state) => state.bluetooth);
  const dispatch = useDispatch();
  const [selectedTab, setSelectedTab] = useState(isFarenheit ? 1 : 0);
  const categoriesTabArr = [
    { id: 1, title: "°C" },
    { id: 2, title: "°F" },
  ];
  const tempDataC = [
    { id: 1, value: "25°" },
    { id: 2, value: "30°" },
    { id: 3, value: "OFF" },
  ];
  const tempDataF = [
    { id: 1, value: "77" },
    { id: 2, value: "86" },
    { id: 3, value: "OFF" },
  ];
  const [temp, setTemp] = useState(
    isFarenheit ? { id: 1, value: "77" } : { id: 1, value: "25°" }
  );
  const onChangeIndex = (index) => {
    console.log("temp====>", isConvert);
    setSelectedTab(index);
    dispatch(setIsFarenheit(!isFarenheit));
    if (isConvert !== undefined) {
      dispatch(setIsConvert(!isConvert));
    }
    if (index === 1) {
      const found = tempDataF.find((element) => element.id === temp.id);
      setTemp(found);
    } else {
      const found = tempDataC.find((element) => element.id === temp.id);
      setTemp(found);
    }
  };
  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  return (
    <>
      <StatusBar barStyle="dark-content" />
      <View style={[styles.root, { backgroundColor: BaseColor.whiteColor }]}>
        <SettingsHeader
          title={translate("settings")}
          title2={translate("tempText")}
          leftIconName="left-arrow"
          onLeftPress={() => {
            navigation.goBack();
          }}
          image
          titleTextColor={BaseColor.whiteColor}
          headerBackgroundColor={BaseColor.inputBackGroundColor}
          isViolateLogo
          backTitle="Settings"
        />
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : null}
            style={styles.mainContainer}
          >
            <TimeTabs
              selectedIndex={selectedTab}
              tabs={categoriesTabArr}
              onChangeIndex={(val) => onChangeIndex(val)}
              bgColor={BaseColor.whiteColor}
            />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontSize: 16,
                fontWeight: "400",
                marginTop: 24,
                marginBottom: 16,
                lineHeight: 24,
              }}
            >
              {translate("tempNotifyText")}
            </Text>
            <View
              style={{
                paddingBottom: 25,
                backgroundColor: BaseColor.black30,
                borderRadius: 12,
                paddingHorizontal: 20,
              }}
            >
              <DropDown
                placeholder=""
                data={isFarenheit ? tempDataF : tempDataC}
                style={{ borderRadius: 12, paddingHorizontal: 20 }}
                valueProp="value"
                onSelect={(val) => {
                  setTemp(val);
                  console.log("cvall", val);
                  let tempV = 0;
                  if (isFarenheit) {
                    val.value === 77 ? (tempV = 25) : (tempV = 30);
                  } else tempV = val.value;
                  dispatch(setHighAlert(tempV));
                }}
                selectedObject={temp}
                editable={false}
                placeHolderColor={BaseColor.blackColor}
              />
            </View>
          </KeyboardAvoidingView>
        </ScrollView>
      </View>
    </>
  );
}

export default TempSetting;
