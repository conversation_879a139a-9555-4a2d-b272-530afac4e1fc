/* eslint-disable max-len */
/* eslint-disable react/no-array-index-key */
/* eslint-disable global-require */
import React, { useEffect, useState } from "react";
import {
  StatusBar,
  View,
  BackHandler,
  TouchableOpacity,
  Dimensions,
  Text,
  Alert
} from "react-native";
import { useDispatch } from "react-redux";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import BaseColor from "../../config/colors";
import styles from "./styles";
import CButton from "../../components/CButton";
import authActions from "../../redux/reducers/auth/actions";
import { enableAnimateInEaseOut } from "../../utils/commonFunction";
import { translate } from "../../lang/Translate";
import { FontFamily } from "../../config/typography";
import SVGImg2 from "../../assets/images/cleanAirLogoWhite.svg";


const Walkthrough = ({ navigation }) => {
  const animTime = 200;
  const [currentIndex, setcurrentIndex] = useState(0);

  const dispatch = useDispatch();

  const anim = useSharedValue(1);
  const nextImgAnim = useSharedValue(0);
  const { width: dWidth } = Dimensions.get("window");
  const slides = [
    {
      key: "one",
      image: require("../../assets/images/IntroImg1.jpg"),
      // title: 'Welcome to BugaBoo',
      title: translate("introTitle1"),
      text: `${translate("introText11")}\n${translate(
        "introText13"
      )}\n${translate("introText12")}`,
    },
    {
      key: "two",
      image: require("../../assets/images/IntroImage2.png"),
      // title: 'Connecting your device',
      title: translate("introTitle2"),
      // text:
      //   'During set-up you will need to locate\nyour personal QR code to scan with\nyou phone which will link to your\nnew BugaBoo smart device.',
      text: `${translate("introText21")} ${translate(
        "introText22"
      )} ${translate("introText23")} ${translate("introText24")}`,
    },
    {
      key: "three",
      image: require("../../assets/images/Intro-large-3-2.jpg"),
      // title: 'Your digital partner',
      title: translate("introTitle3"),
      text: `${translate("introText31")} ${translate(
        "introText32"
      )} ${translate("introText33")}`,
    },
  ];

  const animStyle = useAnimatedStyle(() => ({
    opacity: withTiming(
      anim.value,
      {
        duration: animTime,
      },
      () => {
        anim.value = 1;
      }
    ),
  }));

  const titleStyle = useAnimatedStyle(() => ({
    opacity: withTiming(
      anim.value,
      {
        duration: animTime,
      },
      () => {
        anim.value = 1;
      }
    ),
  }));

  const desStyle = useAnimatedStyle(() => ({
    opacity: withTiming(
      anim.value,
      {
        duration: animTime,
      },
      () => {
        anim.value = 1;
      }
    ),
  }));

  const { setWalkthrough } = authActions;

  function handleBackButtonClick() {
    BackHandler.exitApp();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const changeIndex = () => {
    // Alert.alert('Hello')
    if (currentIndex !== 2) {
      nextImgAnim.value = 0;
      anim.value = 0.2;
      setTimeout(() => {
        const nIndex = currentIndex + 1;
        setcurrentIndex(nIndex);
      }, animTime);
    }
    if (currentIndex === 2) {
      navigation.navigate("RedirectLS");
      dispatch(setWalkthrough(false));
    }
  };

  const preIndex = () => {
    if (currentIndex !== 0) {
      anim.value = 0.2;
      setTimeout(() => {
        const nIndex = currentIndex - 1;
        setcurrentIndex(nIndex);
      }, animTime);
    }
  };

  enableAnimateInEaseOut();
  return (
    <>
      <View style={{ flex: 1, backgroundColor: "black" }}>
        <View style={{ flex: 1 }}>
          <View style={{ flex: 1 }}>
            <StatusBar
              backgroundColor="transparent"
              barStyle="light-content"
              translucent
            />
            <Animated.Image
              style={[styles.image, animStyle]}
              source={slides[currentIndex].image}
              resizeMode="cover"
            />
            {/* <LinearGradient
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              colors={[BaseColor.whiteColor, BaseColor.whiteColor]}
              style={styles.contentView}
            > */}
            <View
              style={[
                {
                  // position: "absolute",
                  // right: dWidth * 0.45,
                  // top: "6%",
                },
              ]}
            >
              {/* <SVGImg2 width={30} height={30} /> */}
            </View>
            <View style={styles.contentView}>
              <Animated.Text style={[styles.titleText, titleStyle]}>
                {translate(slides[currentIndex].title)}
              </Animated.Text>
              <Animated.Text style={[styles.descriptionText, desStyle]}>
                {slides[currentIndex].text}
              </Animated.Text>
              <View style={styles.progress_container}>
                <View style={styles.left_container}>
                  <View>
                    <Animated.Text
                      style={{
                        fontSize: 16,
                        fontFamily: FontFamily.bold,
                        fontWeight: "700",
                      }}
                    >
                      0{currentIndex + 1} / 03
                    </Animated.Text>
                    <View style={{ flexDirection: "row" }}>
                      {slides.map((item, index) => (
                        <Animated.View
                          key={`i${index}`}
                          style={{
                            backgroundColor:
                              index === currentIndex
                                ? BaseColor.progressLight
                                : BaseColor.bDelight,
                            margin: 0,
                            marginTop: 5,
                            paddingHorizontal: 20,
                            borderRadius: 0,
                            height: 7,
                            width: index === currentIndex ? 30 : 15,
                          }}
                        />
                      ))}
                    </View>
                    <View
                      style={{
                        flex: 1,
                        position: "absolute",
                        height: "100%",
                        width: "100%",
                        flexDirection: "row",
                        backgroundColor: "#0000",
                      }}
                    >
                      <TouchableOpacity
                        style={{ flex: 1 }}
                        onPress={preIndex}
                      />
                      <TouchableOpacity
                        style={{ flex: 2 }}
                        onPress={changeIndex}
                      />
                    </View>
                  </View>
                </View>
                <View style={styles.right_container}>
                  {
                    <View style={styles.buttonCircle}>
                      <CButton
                        title="Next"
                        titleStyle={{ color: BaseColor.whiteColor }}
                        // iconname="next-1"
                        // iconsize={18}
                        // iconColor={BaseColor.blueLight}
                        style={{
                          width: 160,
                          height: 77,
                          borderRadius: 26,
                          backgroundColor: BaseColor.cbtGradientColor,
                          borderWidth: 0.2,
                        }}
                        onPress={changeIndex}
                      />
                    </View>
                  }
                </View>
              </View>
            </View>
            {/* </LinearGradient> */}
          </View>
        </View>
      </View>
    </>
  );
};

export default Walkthrough;
