import { Dimensions, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";
const DeviceHeight = Dimensions.get("screen").height;
const DeviceWidth = Dimensions.get("screen").width;
const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  image: {
    // flex: 1,
    width: Dimensions.get("screen").width,
    height: Dimensions.get("screen").height * 0.9,
    resizeMode: "cover",
  },
  contentView: {
    // flex: 1,
    position: "absolute",
    bottom: 0,
    width: Dimensions.get("screen").width,
    height: DeviceHeight * 0.45 + 30,
    borderTopLeftRadius: 46,
    borderTopRightRadius: 46,
    paddingHorizontal: 20,
    paddingVertical: 22,
    backgroundColor: BaseColor.whiteColor,
  },
  titleText: {
    color: BaseColor.blackColor,
    fontSize: 24,
    // textAlign: 'center',
    alignItems: "flex-start",
    fontWeight: "700",
    paddingBottom: 8,
    paddingTop: 14,
    fontFamily: FontFamily.bold,
    lineHeight: 32,
  },
  descriptionText: {
    color: BaseColor.blackColor,
    fontSize: 16,
    // textAlign: 'center',
    alignItems: "flex-start",
    fontFamily: FontFamily.default,
    lineHeight: 24,
  },
  buttonCircle: {
    width: 160,
    height: 77,
    backgroundColor: BaseColor.blackColor,
    borderRadius: 26,
    justifyContent: "center",
    alignItems: "center",
    // position: "absolute",
    // bottom: 40,
    // right: 20,
  },
  progress_container: {
    position: "absolute",
    bottom: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    width: DeviceWidth * 0.9,
    right: DeviceWidth * 0.05,
  },
  left_container: {
    justifyContent: "flex-end",
    marginBottom: 10,
  },
  right_container: {},
});

export default styles;
