/* eslint-disable quotes */
import React, { useEffect, useState } from "react";
import {
  FlatList,
  Text,
  View,
  Image,
  TouchableOpacity,
  BackHandler,
  RefreshControl,
  ActivityIndicator,
} from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome5";
import _ from "lodash";
import Toast from "react-native-simple-toast";
import { useSelector } from "react-redux";
import { useFocusEffect, useTheme } from "@react-navigation/native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";
import styles from "./styles";
import CHeader from "../../components/CHeader";
import GradientBack from "../../components/gradientBack";
import { translate } from "../../lang/Translate";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import {
  enableAnimateLinear,
  sendErrorReport,
} from "../../utils/commonFunction";

let backPressed = 0;

const Devices = ({ navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const listItemAnim = useSharedValue(0);
  const opacityAnim = useSharedValue(0);

  const listItemAnimationStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(listItemAnim.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(opacityAnim.value, {
      duration: 1000,
    }),
  }));

  // useEffect(() => {
  //   opacityAnim.value = 1;
  //   listItemAnim.value = -10;
  // }, []);

  const [loader, setloader] = useState(true);

  const [isActive, setIsActive] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const token = useSelector((state) => state.auth.accessToken);

  const check = (item1) => {
    const data = [...devices];
    const obj = _.findIndex(data, (item) => item.id === item1.item.id);
    data[obj].check = !data[obj].check;
  };

  useFocusEffect(
    React.useCallback(() => {
      setloader(true);

      setDevices([
        {
          type: "add",
        },
      ]);
      if (token !== "") {
        getChildInfo();
      }
      setloader(false);
      opacityAnim.value = 1;
      listItemAnim.value = -300;
    }, [])
  );

  const onRefresh = React.useCallback(() => {
    setDevices([
      {
        type: "add",
      },
    ]);
    getChildInfo();
    setTimeout(() => {
      setloader(false);
      opacityAnim.value = 1;
      listItemAnim.value = -300;
    }, 2000);
  }, []);

  const [devices, setDevices] = useState([
    {
      type: "add",
    },
  ]);
  const [profile, updateProfile] = useState([
    {
      type: "add",
    },
  ]);

  const getChildInfo = () => {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    getApiData(
      BaseSetting.endpoints.getUserChild,
      "POST",
      {
        platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
      },
      headers
    )
      .then((response) => {
        if (response.success) {
          const tempArr = [
            {
              type: "add",
            },
          ];
          const updateArr = [
            {
              type: "add",
            },
          ];
          const childArr = response.data;

          childArr.map((item) => {
            tempArr.unshift(item);
            updateArr.splice(1, 0, item);
          });
          sendErrorReport(response, "get_child_in_device_response");
          setDevices(tempArr);
          updateProfile(updateArr);
        } else {
          Toast.show(response.message);
        }
        setloader(false);
        setRefreshing(false);
      })
      .catch((err) => {
        // console.log("ERRR", err);
        Toast.show("Something went wrong while getting child detail");
        sendErrorReport(err, "get_child_in_device");
        setRefreshing(false);
      });
  };

  const img = require("../../assets/images/logo-1.png");

  const render = ({ item }) => {
    enableAnimateLinear();
    return (
      <View>
        {item.type === "add" ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => {
              navigation.navigate("ChildInfo", {
                type: "addProfile",
                items: profile,
              });
            }}
            style={[
              styles.addNewProfile,
              { backgroundColor: BaseColor.whiteColor },
            ]}
          >
            <View style={{ alignSelf: "center" }}>
              <Text style={[styles.addText, { color: BaseColor.blueDark }]}>
                {translate("addNew")}
              </Text>
              <Text style={[styles.tapText, { color: BaseColor.blackColor }]}>
                {translate("tapToAddNewProfile")}
              </Text>
            </View>
          </TouchableOpacity>
        ) : (
          <View style={styles.renderView}>
            {item.device_connection ? (
              <View
                style={[
                  styles.checkIcon,
                  { backgroundColor: BaseColor.whiteColor },
                ]}
              >
                <FAIcon
                  name="check"
                  size={10}
                  color={BaseColor.blueDark}
                  // style={styles.checkIcon}
                />
              </View>
            ) : (
              <FAIcon
                name="circle"
                size={28}
                color={BaseColor.black20}
                style={{ paddingRight: 10 }}
                solid
              />
            )}

            <TouchableOpacity
              activeOpacity={0.8}
              style={[
                styles.touchableContent,
                { backgroundColor: BaseColor.whiteColor },
              ]}
              onPress={() => {
                navigation.navigate("CDeviceList", {
                  deviceDetail: item.deviceDetails || [],
                  title: item.nick_name || "",
                  item,
                });
              }}
            >
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Image
                  style={styles.image}
                  source={{ uri: item.child_profile }}
                />
                <View style={styles.contentTextView}>
                  <Text
                    style={[styles.title, { color: BaseColor.blackColor }]}
                    numberOfLines={1}
                  >
                    {item.nick_name}
                  </Text>
                  <Text
                    style={[
                      styles.connectedDevice,
                      { color: BaseColor.textGrey },
                    ]}
                  >
                    {`${item.device_count || 0} ${translate(
                      "deviceconnected"
                    )}`}
                  </Text>
                  <Text
                    style={{
                      color: BaseColor.blueDark,
                      fontSize: 12,
                    }}
                  >
                    {item.device_connection === "Deactivated"
                      ? translate("deactivedDevice")
                      : translate("activeDevice") || ""}
                  </Text>
                </View>
              </View>
              <FAIcon
                name="chevron-right"
                size={16}
                color={BaseColor.textGrey}
                style={{ paddingHorizontal: 20 }}
              />
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show("Press Again To Exit");
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  return (
    <View style={styles.root}>
      <GradientBack />
      <CHeader
        image={img}
        rightIconName="notifications-bell-button"
        leftIconName="settings-2"
        onLeftPress={() => {
          navigation.openDrawer();
        }}
        onRightPress={() => {
          navigation.navigate("Alerts");
        }}
      />
      {!loader ? (
        <Animated.View
          style={[
            {
              flex: 1,
              top: 300,
            },
            listItemAnimationStyle,
          ]}
        >
          <FlatList
            data={devices}
            renderItem={render}
            keyExtractor={(item, index) => {
              index;
            }}
            contentContainerStyle={{ flexGrow: 1, paddingBottom: 116 }}
            bounces
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          />
        </Animated.View>
      ) : (
        <ActivityIndicator
          size={42}
          color={BaseColor.whiteColor}
          style={{ flex: 1 }}
        />
      )}

      {/* <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => {
          navigation.navigate("ChildInfo");
        }}
        style={styles.addNewProfile}
      >
        <View style={{ alignSelf: "center" }}>
          <Text style={styles.addText}>{translate("addNew")}</Text>
          <Text style={styles.tapText}>{translate("tapToAddNewProfile")}</Text>
        </View>
      </TouchableOpacity> */}
    </View>
  );
};

export default Devices;
