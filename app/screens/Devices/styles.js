import { Dimensions, StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  flatlistView: {
    marginTop: 30,
    marginBottom: 10,
  },
  addNewProfile: {
    height: 90,
    borderRadius: 10,
    backgroundColor: BaseColor.whiteColor,
    justifyContent: 'center',
    marginRight: 16,
    marginLeft: 54,
    marginTop: 8,
  },
  addText: {
    textAlign: 'center',
    fontSize: 20,
    color: BaseColor.blueDark,
    fontFamily: FontFamily.default,
  },
  tapText: {
    textAlign: 'center',
    fontSize: 12,
    paddingVertical: 4,
  },
  renderView: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  checkIcon: {
    backgroundColor: BaseColor.whiteColor,
    width: 28,
    height: 28,
    borderRadius: 14,
    textAlign: 'center',
    textAlignVertical: 'center',
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  touchableContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: BaseColor.whiteColor,
    marginVertical: 8,
    borderRadius: 10,
  },
  image: {
    width: 90,
    height: 90,
    borderRadius: 10,
  },
  title: {
    color: BaseColor.blackColor,
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: FontFamily.default,
    width: Dimensions.get('screen').width / 2 - 60,
  },
  connectedDevice: {
    color: BaseColor.textGrey,
    fontSize: 12,
    paddingVertical: 2,
    width: Dimensions.get('screen').width / 2 - 60,
  },
  contentTextView: {
    paddingHorizontal: 10,
  },
});

export default styles;
