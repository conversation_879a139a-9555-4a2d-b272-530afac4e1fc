import React, { useEffect } from "react";
import { ImageBackground } from "react-native";
import { Text } from "react-native";
import { View, Button, Image } from "react-native";
// import {
//   CarPlay,
//   ContactTemplate,
//   GridTemplate,
//   PointOfInterestTemplate,
//   VoiceControlTemplate,
// } from "../../lib/react-native-carplay";
import {
CarPlay,
ContactTemplate,
GridTemplate,
PointOfInterestTemplate,
VoiceControlTemplate
} from 'react-native-carplay';
import { useSelector } from "react-redux";

const gridItemImage = require("../../assets/images/three_front_correct.png");
const warning = require("../../assets/images/warning.png");

export function Menu({ navigation }) {
  const accessToken = useSelector((state) => state.auth.accessToken);

  useEffect(() => {
    // const noLogin = new ContactTemplate({
    //   name: "Please Login!",
    //   image: warning,
    //   subtitle:
    //     "Please login and enter you child detail after connect to device, to experiance carplay!",
    // });
    const noLogin = new GridTemplate({
      buttons: [
        {
          id: "List",
          titleVariants: [
            "Please Login from device in app,to experience carplay.",
          ],
          image: warning,
        },
      ],
      // onButtonPressed: ({ id }) => {
      //   navigation.navigate(id);
      // },
      onWillAppear: () => {
        navigation.navigate("Menu");
      },
      title: "SMART 360 IQ",
    });
    const gridTemplate = new GridTemplate({
      buttons: [
        {
          id: "List",
          titleVariants: ["Dashboard"],
          image: gridItemImage,
        },

        // {
        //   id: "Search",
        //   titleVariants: ["Search"],
        //   image: gridItemImage,
        // },
        // {
        //   id: "Information",
        //   titleVariants: ["Information"],
        //   image: gridItemImage,
        // },

        // {
        //   id: "CarplayAlert",
        //   titleVariants: ["Alert"],
        //   image: gridItemImage,
        // },
        {
          id: "ActionSheet",
          titleVariants: ["ActionSheet"],
          image: gridItemImage,
        },
      ],
      onButtonPressed: ({ id }) => {
        navigation.navigate(id);
      },
      onWillAppear: () => {
        navigation.navigate("Menu");
      },
      title: "SMART 360 IQ",
    });

    CarPlay.setRootTemplate(accessToken ? gridTemplate : noLogin);
    // CarPlay.setRootTemplate(gridTemplate);
  }, []);

  const onTabBarPress = () => navigation.navigate("TabBar");
  const onListPress = () => navigation.navigate("List");
  const onGridPress = () => navigation.navigate("Grid");
  const onMapPress = () => navigation.navigate("Map");
  const onSearchPress = () => navigation.navigate("Search");
  const onVoiceControlPress = () => navigation.navigate("VoiceControl");
  const onContactPress = () => navigation.navigate("Contact");
  const onActionSheetPress = () => navigation.navigate("ActionSheet");
  const onAlertPress = () => navigation.navigate("CarplayAlert");
  const onInformationPress = () => navigation.navigate("Information");
  const onNowPlayingPress = () => navigation.navigate("NowPlaying");
  const onPOIPress = () => navigation.navigate("POI");
  const show3Num = require("../../assets/images/three_front_correct.png");
  return (
    <View
      style={{
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      {!accessToken ? (
        <View style={{ alignItems: "center", margin: 30 }}>
          <Image source={warning} style={{ height: 50, width: 50 }}></Image>
          <Text style={{ fontWeight: "bold", fontSize: 30, margin: 20 }}>
            Please login!
          </Text>
          <Text>
            Disconnect to carplay and login in to app, create your child profile
            if you have not created and connect to device to expeirence carplay.
          </Text>
        </View>
      ) : (
        <>
          <Button title="Dashboard" onPress={onListPress} />

          {/* <Button title="Search" onPress={onSearchPress} /> */}
          {/* <Button title="Information" onPress={onInformationPress} /> */}
          <Button title="Action Sheet" onPress={onActionSheetPress} />
          {/* <Button title="CarplayAlert" onPress={onAlertPress} /> */}

          {/* <Button
        title="TabBar (Will overwrite root template)"
        onPress={onTabBarPress}
      />
      <Button title="Contact" onPress={onContactPress} />
      <Button title="Now Playing" onPress={onNowPlayingPress} />
      <Button title="Point Of Interest" onPress={onPOIPress} /> */}
        </>
      )}
    </View>
  );
}

Menu.navigationOptions = {
  headerTitle: "Car Play Example",
};
