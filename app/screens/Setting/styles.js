import { Dimensions, Platform, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";
import { store } from "../../redux/store/configureStore";
let deviceWidth = Dimensions.get("window").width;
let deviceHeight = Dimensions.get("window").height;
const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  flatListView: {
    // backgroundColor: BaseColor.whiteColor,
    marginHorizontal: 16,
    // marginVertical: 30,
    borderRadius: 16,
  },
  settingContent: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 20,
    paddingVertical: 16,
    borderBottomColor: BaseColor.inputBackGroundColor,
    borderBottomWidth: 0.5,
  },
  settingName: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  tempC: {
    width: 32,
    height: 32,
    backgroundColor: "#dd2c00",
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  tempTxt: {
    textAlign: "center",
    textAlignVertical: "center",
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.default,
    fontWeight: "bold",
  },
  settingIcon: {
    width: 40,
    height: 40,
    backgroundColor: BaseColor.whiteColor,
    textAlignVertical: "center",
    textAlign: "center",
    borderRadius: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    justifyContent: "center",
    alignItems: "center",
  },
  infoText: {
    // color: BaseColor.blackColor,
    fontSize: 16,
    paddingHorizontal: 0,
    fontFamily: FontFamily.default,
    fontWeight: "400",
    lineHeight: 32,
  },
  securityCheckView: {
    width: 50,
    alignSelf: "center",
  },
  aboutText: {
    fontSize: 16,
    // color: BaseColor.whiteColor,
    paddingBottom: 20,
    fontFamily: FontFamily.default,
    fontWeight: "bold",
  },
  // ------
  custom_navbar: {
    position: "absolute",
    height: 80,
    width: deviceWidth * 0.85,
    bottom: Platform.OS === "android" ? 30 : 50,
    alignSelf: "center",
    backgroundColor: BaseColor.blackColor,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    borderBottomRightRadius: 40,
    borderBottomLeftRadius: 40,
    justifyContent: "space-evenly",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.37,
    shadowRadius: 7.49,
    elevation: 12,
    flexDirection: "row",
    borderWidth: 0.5,
    borderColor: BaseColor.textGrey,
  },
});

export default styles;
