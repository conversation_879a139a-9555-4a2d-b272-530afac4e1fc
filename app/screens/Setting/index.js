/* eslint-disable quotes */
import React, { useEffect, useState } from "react";
import {
  Text,
  View,
  TouchableOpacity,
  FlatList,
  <PERSON>rollView,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "react-native";
import ToggleSwitch from "toggle-switch-react-native";
import FAIcon from "react-native-vector-icons/FontAwesome5";
import { findIndex, isEmpty, isObject } from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { useTheme } from "@react-navigation/native";
import { EventRegister } from "react-native-event-listeners";
import Toast from "react-native-simple-toast";
import styles from "./styles";
import { CustomIcon } from "../../config/LoadIcons";
import CHeader from "../../components/CHeader";
import GradientBack from "../../components/gradientBack";
import { translate } from "../../lang/Translate";
import AuthAction from "../../redux/reducers/auth/actions";
import BluetoothAction from "../../redux/reducers/bluetooth/actions";
import BaseSetting from "../../config/setting";
import CAlert from "../../components/CAlert";
import { getApiData } from "../../utils/apiHelper";
import DeviceInfo from "react-native-device-info";
import { sendErrorReport } from "../../utils/commonFunction";
import SettingsHeader from "../../components/CHeader/SettingsHeader";
import { FontFamily } from "../../config/typography";

// import BaseColor, { DarkBaseColor } from '../../config/colors';

const Data = [
  {
    id: 1,
    title: translate("myFamilyProfiles"),
    icon1: "chevron-right",
  },
  {
    id: 2,
    title: translate("MyQRcode"),
    icon1: "chevron-right",
  },
  {
    id: 3,
    icon: "thermometer-three-quarters",
    title: translate("tempText"),
    temp: "F",
    icon1: "chevron-right",
  },
  {
    id: 4,
    Cicon: "notifications-bell-button",
    title: translate("pushText"),
    mode: translate("modeOff"),
    modeOn: translate("modeOn"),
  },
  // {
  //   id: 5,
  //   Cicon: "view-2",
  //   title: translate("darkmodeText"),
  //   mode: translate("modeOff"),
  //   modeOn: translate("modeOn"),
  // },
  {
    id: 6,
    icon: "comment-dots",
    title: translate("customerText"),
    icon1: "chevron-right",
  },
  {
    id: 7,
    icon: "shopping-cart",
    title: translate("products"),
    icon1: "chevron-right",
  },
  {
    id: 8,
    title: translate("userManual"),
    icon1: "chevron-right",
  },
];

const Setting = ({ navigation }) => {
  const {
    setAccessToken,
    setUserData,
    setUserId,
    setUUid,
    setBaseColor,
    setIsFarenheit,
    setDarkmode,
  } = AuthAction;

  const { setLastDeviceId } = BluetoothAction;

  const dispatch = useDispatch();

  const colors = useTheme();
  const BaseColor = colors.colors;

  const { uuid, accessToken, darkmode, userData, isFarenheit } = useSelector(
    (state) => state.auth
  );
  const socketData = useSelector((state) => state.socket.socketData);

  // const [isPush, setIsPush] = useState(userData?.push_notification_sent);
  // const [isDarkMode, setIsDarkMode] = useState(darkmode);
  const isPush = userData?.push_notification_sent;

  const [btnLoader, setBtnLoader] = useState(false);

  const [AlerModal, setAlerModal] = useState({
    visible: false,
    title: "",
    message: "",
  });

  // this function for update notification status
  async function updateNotificationStatus() {
    const headers = {
      "Content-Type": "application/json",
      authorization: accessToken ? `Bearer ${accessToken}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.notificationStatus,
        "POST",
        {},
        headers
      );

      if (response.success) {
        // setIsPush(!isPush);
        if (isObject(response.data) && !isEmpty(response.data)) {
          dispatch(setUserData(response.data));
        }
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      console.log("notification error ===", error);
      sendErrorReport(error, "update_noti_status");
    }
  }

  const check = async (item1) => {
    const data = [...Data];
    const obj = findIndex(data, (item) => item.id === item1.id);
    const darkN = !darkmode;
    switch (obj) {
      case 0:
        navigation.navigate("FamilyProfiles");
        break;
      case 1:
        navigation.navigate("MyQRcode");
        break;
      case 3:
        updateNotificationStatus();
        break;
      // case 4:
      //   // setIsDarkMode(darkN);
      //   EventRegister.emit("changeAppTheme", darkN);
      //   dispatch(setDarkmode(darkN));
      //   // setTimeout(() => {
      //   //   ReactNativeRestart.Restart();
      //   // }, 100);
      //   break;
      // done
      case 2:
        navigation.navigate("TempSetting");
        // navigation.navigate("ChatScreen");
        // dispatch(setIsFarenheit(!isFarenheit));
        break;
      case 4:
        navigation.navigate("ChatScreen");
        break;
      case 5:
        navigation.navigate("Products");
        break;
      case 6:
        navigation.navigate("UserManual");
        break;
      case 7:
        navigation.navigate("MyAccount");
        break;
      default:
        break;
    }
  };

  const render = ({ item, index }) => (
    <View style={[styles.settingContent]}>
      {/* <View
        style={[styles.settingIcon, { backgroundColor: BaseColor.whiteColor }]}
      >
        {item.Cicon ? (
          <CustomIcon name={item.Cicon} size={20} color={BaseColor.blueLight} />
        ) : (
          <FAIcon name={item.icon} size={20} color={BaseColor.blueLight} />
        )}
      </View> */}

      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => {
          check(item);
        }}
        style={styles.settingName}
      >
        <View style={{ flexDirection: "column" }}>
          <Text style={[styles.infoText, { color: BaseColor.blackColor }]}>
            {item.id === 1
              ? translate("myFamilyProfiles")
              : item.id === 2
              ? `${translate("MyQRcode")} / ${translate("shareMyDevices")}`
              : item.id === 3
              ? translate("tempText")
              : item.id === 4
              ? translate("pushText")
              : item.id === 5
              ? translate("darkmodeText")
              : item.id === 6
              ? translate("customerText")
              : item.id === 7
              ? translate("products")
              : item.id === 8
              ? translate("userManual")
              : translate("products")}
          </Text>
          {item.mode ? (
            <Text
              style={{
                ...styles.infoText,
                fontSize: 14,
                color:
                  (item.id == 4 && isPush) || (item.id == 5 && darkmode)
                    ? BaseColor.blueDark
                    : "red",
              }}
            >
              {(item.id == 4 && isPush) || (item.id == 5 && darkmode)
                ? translate("modeOn")
                : translate("modeOff")}
            </Text>
          ) : null}
        </View>

        {item.temp ? (
          <View
            style={[
              styles.tempC,
              { backgroundColor: isFarenheit ? "#dd2c00" : "#cddc39" },
            ]}
          >
            <Text style={[styles.tempTxt, { color: BaseColor.whiteColor }]}>
              {!isFarenheit ? translate("tempC") : translate("tempF")}
            </Text>
          </View>
        ) : null}

        {item.mode ? (
          <ToggleSwitch
            size="medium"
            isOn={Boolean((item.id === 4 && isPush) || (item.id === 5 && darkmode) || false)}
            onToggle={(val) => {
              if (item.id === 4) {
                updateNotificationStatus();
              } else {
                // setIsDarkMode(val);
                EventRegister.emit("changeAppTheme", val);
                dispatch(setDarkmode(val));
              }
            }}
            onColor={BaseColor.whiteColor}
            thumbOnStyle={{ backgroundColor: BaseColor.blueLight }}
            thumbOffStyle={{ backgroundColor: BaseColor.whiteColor }}
            trackOnStyle={{
              borderWidth: 1,
              borderColor: BaseColor.blueLight,
            }}
            trackOffStyle={{ borderWidth: 1, borderColor: "#ecf0f1" }}
          />
        ) : null}

        {item.icon1 ? (
          <FAIcon
            name="arrow-right"
            size={15}
            color={BaseColor.blackColor}
            style={{ marginRight: 0 }}
          />
        ) : null}
      </TouchableOpacity>
    </View>
  );

  function handleBackButtonClick() {
    navigation.closeDrawer();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  async function removeToken(token) {
    setBtnLoader(true);
    const data = {
      token,
      socket_id: socketData?.socket_id,
      user_id: userData.id,
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.deleteToken,
        "POST",
        data,
        {
          "Content-Type": "application/json",
          authorization: accessToken ? `Bearer ${accessToken}` : "",
        }
      );
      setBtnLoader(false);
      dispatch(setAccessToken(""));
      dispatch(setUUid(""));
      navigation.navigate("RedirectLS");
      navigation.closeDrawer();
    } catch (err) {
      console.log("ERRR==", err);
      sendErrorReport(err, "remove_token");
    }
  }

  return (
    <>
      <View style={{ flex: 1, backgroundColor: BaseColor.whiteColor }}>
        <SettingsHeader
          image
          title2={translate("smartcar")}
          title={translate("settingsText")}
          leftIconName="left-arrow"
          onLeftPress={handleBackButtonClick}
          titleTextColor={BaseColor.whiteColor}
          headerBackgroundColor={BaseColor.blackColor}
          isViolateLogo
        />
        {/* <ScrollView
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }}
          bounces={false}
        > */}
          <View style={styles.root}>
            {/* <GradientBack /> */}
            {/* <CHeader
              title={translate("settingScreen")}
              backBtn
              leftIconName
              onLeftPress={() => {
                navigation.closeDrawer();
              }}
            /> */}
            <View
              style={[
                styles.flatListView,
                { backgroundColor: BaseColor.whiteColor },
              ]}
            >
              <FlatList
                data={Data}
                renderItem={render}
                keyExtractor={(item) => item.id}
                bounces={false}
              />
            </View>
            <TouchableOpacity
              onPress={() => navigation.navigate("MyAccount")}
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "85%",
                alignSelf: "center",
                paddingLeft: 8,
                marginTop: 16,
              }}
            >
              <Text
                style={{
                  color: BaseColor.blackColor,
                  fontSize: 16,
                  paddingHorizontal: 0,
                  fontFamily: FontFamily.default,
                  fontWeight: "400",
                  lineHeight: 32,
                }}
              >
                {translate("MyAccount")}
              </Text>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => {
                  setAlerModal({
                    visible: true,
                    title: translate("alertlogout"),
                    message: translate("youwanttologout"),
                  });
                  const { setLeadId } = AuthAction;
                  dispatch(setLeadId(null));
                  // navigation.closeDrawer();
                  // dispatch(setAccessToken(''));
                  // dispatch(setUserData({}));
                  // dispatch(setUserId(''));
                  // dispatch(setUUid(''));
                  // removeToken(uuid);
                  // navigation.navigate('RedirectLS');
                }}
              >
                <Text
                  style={[styles.aboutText, { color: BaseColor.blackColor }]}
                >
                  {translate("logout")}
                </Text>
              </TouchableOpacity>
            </TouchableOpacity>
            <View style={{ paddingHorizontal: 16 }}>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => {
                  Linking.openURL("https://www.chillbabytechnologies.com/");
                }}
              >
                <Text
                  style={[styles.aboutText, { color: BaseColor.whiteColor }]}
                >
                  {translate("about")}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => {
                  navigation.navigate("FAQScreen");
                }}
              >
                <Text
                  style={[styles.aboutText, { color: BaseColor.whiteColor }]}
                >
                  {translate("support")}
                </Text>
              </TouchableOpacity>
              <Text
                style={{
                  ...styles.aboutText,
                  fontSize: 10,
                  color: BaseColor.whiteColor,
                }}
              >
                {/* {translate("version")} */}
                Version {DeviceInfo.getVersion()}
              </Text>
            </View>
          </View>
        {/* </ScrollView> */}
        {/* <View style={styles.custom_navbar}>
          <TouchableOpacity
            onPress={() => navigation.navigate(translate("home"))}
          >
            <CustomIcon name={"home"} size={20} color={"#fff"} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate(translate("dashboard"))}
          >
            <CustomIcon name={"user-3"} size={20} color={"#fff"} />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => navigation.openDrawer()}>
            <CustomIcon
              name={"settings-2"}
              size={20}
              color={BaseColor.blueLight}
            />
          </TouchableOpacity>
        </View> */}
        <CAlert
          visible={AlerModal.visible}
          onRequestClose={() =>
            setAlerModal({
              ...AlerModal,
              visible: false,
            })
          }
          onCancelPress={() =>
            setAlerModal({
              ...AlerModal,
              visible: false,
            })
          }
          loader={btnLoader}
          onOkPress={() => {
            dispatch(setUserData({}));
            dispatch(setUserId(""));
            dispatch(setLastDeviceId(""));
            removeToken(uuid);
            // navigation.navigate("RedirectLS");
          }}
          alertTitle={AlerModal.title}
          alertMessage={AlerModal.message}
          agreeTxt={translate("agree")}
        />
      </View>
    </>
  );
};

export default Setting;
