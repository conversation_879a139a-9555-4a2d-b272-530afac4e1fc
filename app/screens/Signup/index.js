import React, { useEffect, useRef, useState } from "react";
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  SafeAreaView,
  BackHandler,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import CountryPicker from "react-native-country-picker-modal";
import LinearGradient from "react-native-linear-gradient";
import FAIcon from "react-native-vector-icons/FontAwesome";
import Toast from "react-native-simple-toast";
import { useDispatch, useSelector } from "react-redux";
import { isArray, isEmpty, isNaN, isObject } from "lodash";
import { useTheme } from "@react-navigation/native";
import GetLocation from "react-native-get-location";
import CButton from "../../components/CButton";
import CInput from "../../components/CInput";
import GradientBack from "../../components/gradientBack";
import styles from "./styles";
import { FontFamily } from "../../config/typography";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from "../../utils/commonFunction";
import AuthAction from "../../redux/reducers/auth/actions";
import { translate } from "../../lang/Translate";
import DropDown from "../../components/DropDown";
import DeviceInfo from "react-native-device-info";

const Signup = ({ navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const brandToken = useSelector((state) => state.auth.brandToken);
  const langList = useSelector((state) => state.auth.langList);

  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);

  const [nameError, setNameError] = useState(false);
  const [passwordError, setPasswordError] = useState(false);
  const [mailError, setMailError] = useState(false);
  const [numError, setNumError] = useState(false);
  const [langError, setLangError] = useState(false);

  const [nameErrorTxt, setnameErrorTxt] = useState("");
  const [passwordErrorTxt, setPasswordErrorTxt] = useState("");
  const [mailErrorTxt, setMailErrorTxt] = useState("");
  const [numErrorTxt, setNumErrorTxt] = useState("");
  const [langTxt, setLangTxt] = useState("");
  const [hideShow, setHideShow] = useState(true);
  const [showCheckList, setShowCheckList] = useState(false);
  const [isUpperCase, setIsUpperCase] = useState(false);
  const [isLowerCase, setIsLowerCase] = useState(false);
  const [isNu, setIsNumber] = useState(false);
  const [isSpecial, setIsSp] = useState(false);
  const [isTwelve, setIsTwelve] = useState(false);
  const fNameRef = useRef();
  const passwordRef = useRef();
  const emailRef = useRef();
  const pNumRef = useRef();

  const dispatch = useDispatch();
  const { setUserId } = AuthAction;

  const [state, setstate] = useState({
    fullName: "",
    password: "",
    email: "",
    pNum: "",
    country: "",
    selectedCountry: "US",
    selectedCountryName: "United States",
    countryCode: "1",
    agree: false,
    selLang: langList[1],
    lat: null,
    lng: null,
    state: "",
  });

  useEffect(() => {
    getLocation();
  }, []);

  const getLocation = () => {
    const myApiKey = "AIzaSyCoDO-inDu4lc0k7iZeO9Y-ZzkFELknuwk";

    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
    })
      .then((location) => {
        console.log("Location====>>>>", location);

        fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?address=${location.latitude},${location.longitude}&key=${myApiKey}`
        )
          .then((response) => response.json())
          .then((responseJson) => {
            console.log(
              `ADDRESS GEOCODE is BACK!! => ${JSON.stringify(responseJson)}`
            );
            var stateV = "";
            const { address_components } = responseJson.results[0];
            console.log(address_components);
            address_components.map((item) => {
              if (item.types[0] === "administrative_area_level_1") {
                stateV = item.long_name;
              }
            });
            console.log(
              "chillbaby ~ file: index.js ~ line 105 ~ .then ~ state",
              stateV
            );
            setstate({
              ...state,
              lat: location.latitude,
              lng: location.longitude,
              state: stateV,
            });
          });
      })
      .catch((error) => {
        const { code, message } = error;
        console.warn(code, message);
        sendErrorReport(error, "get_location");
      });
  };

  function handleBackButtonClick() {
    navigation.navigate("RedirectLS");
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const Validation = () => {
    userSignUp();

    const passVal = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    const numVal = /^[0-9]+$/;
    const emailVal = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    enableAnimateInEaseOut();

    // if (state.fullName == "") {
    //   allErrorFalse();
    //   setNameError(true);
    //   setnameErrorTxt(translate("enterfullname"));
    // } else if (state.email == "") {
    //   allErrorFalse();
    //   setMailError(true);
    //   setMailErrorTxt(translate("enteremail"));
    // } else if (!emailVal.test(String(state.email))) {
    //   allErrorFalse();
    //   setMailError(true);
    //   setMailErrorTxt(translate("entervalidemail"));
    // } else if (state.password == "") {
    //   allErrorFalse();
    //   setPasswordError(true);
    //   setPasswordErrorTxt(translate("enterpassword"));
    // } else if (!passVal.test(String(state.password))) {
    //   allErrorFalse();
    //   setPasswordError(true);
    //   setPasswordErrorTxt(translate("entervalidpassword"));
    // } else if (state.pNum == "") {
    //   allErrorFalse();
    //   setNumError(true);
    //   setNumErrorTxt(translate("enterphone"));
    // } else if (
    //   !numVal.test(String(state.pNum)) ||
    //   state.pNum.length < 6 ||
    //   state.pNum.length > 12
    // ) {
    //   allErrorFalse();
    //   setNumError(true);
    //   setNumErrorTxt(translate("entervalidphone"));
    // } else if (!state.agree) {
    //   allErrorFalse();
    //   Toast.show(translate("accepttermsandcondition"), Toast.SHORT, [
    //     "UIAlertController",
    //   ]);
    // } else {
    //   allErrorFalse();
    //   userSignUp();
    // }
  };

  const allErrorFalse = () => {
    setNameError(false);
    setNumError(false);
    setPasswordError(false);
    setMailError(false);
  };

  const userSignUp = () => {
    setloader(true);
    setanim(true);
    setBackAnim(false);
    const data = {
      full_name: state.fullName,
      email: state.email,
      password: state.password,
      phone: state.pNum,
      phone_code: `+${state.countryCode}`,
      country: state.selectedCountryName,
      token: brandToken,
      language_id: state.selLang.id,
      latitude: state.lat,
      longitude: state.lng,
      state: state.state,
    };

    getApiData(BaseSetting.endpoints.signUp, "POST", data)
      .then((response) => {
        const uId =
          response && isObject(response.data) && response.data.id
            ? response.data.id
            : null;
        dispatch(setUserId(uId));
        if (response.success) {
          setTimeout(() => {
            setloader(false);
            setdone(true);
          }, 2000);
          setTimeout(() => {
            navigation.navigate("Otp", { type: "Signup" });
          }, 3000);
          // Toast.shokenw(response.message);
        } else {
          Toast.show(response.message?.message || response.message);
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch((err) => {
        Toast.show("Something went wrong while signup");
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log("ERR====>>>>", err);
        sendErrorReport(err, "sign_up");
      });
  };

  function isUpper(str) {
    return /^(?=.*?[A-Z])/.test(str);
  }
  function isLower(str) {
    return /^(?=.*?[a-z])/.test(str);
  }
  function isNumericCheck(str) {
    return /^(?=.*?[0-9])/.test(str);
  }
  function isSpCheck(str) {
    return /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(str);
  }
  function isTw(str) {
    if (str.length >= 12) {
      return true;
    } else {
      return false;
    }
  }
  return (
    <>
      <View style={styles.root}>
        <GradientBack />
        <KeyboardAvoidingView
          behavior={Platform.OS == "ios" ? "padding" : null}
          showsVerticalScrollIndicator={false}
          style={styles.mainContainer}
        >
          <ScrollView
            contentContainerStyle={{ flexGrow: 1, padding: 24 }}
            showsVerticalScrollIndicator={false}
            bounces={false}
          >
            <View style={{ justifyContent: "center", flex: 1 }}>
              <View style={styles.mainInputStyle}>
                <View style={styles.logoImg}>
                  <Image
                    source={require("../../assets/images/logo-1.png")}
                    style={{ height: "100%", width: "100%" }}
                  />
                </View>
                <View style={styles.loginTextView}>
                  <Text
                    style={[styles.loginText, { color: BaseColor.whiteColor }]}
                  >
                    {translate("loginToSignup")}
                  </Text>
                </View>
                <CInput
                  ref={fNameRef}
                  placeholder={translate("fullName")}
                  value={state.fullName}
                  onChangeText={(val) => {
                    setstate({ ...state, fullName: val });
                  }}
                  placeholderTextColor={BaseColor.placeHolderColor}
                  iconName="user-2"
                  onSubmitEditing={() => {
                    emailRef.current.focus();
                  }}
                  showError={nameError}
                  errorMsg={nameErrorTxt}
                />
                <CInput
                  ref={emailRef}
                  placeholder={translate("emailId")}
                  value={state.email}
                  onChangeText={(val) => {
                    setstate({ ...state, email: val });
                  }}
                  placeholderTextColor={BaseColor.placeHolderColor}
                  iconName="envelope-2"
                  textInputWrapper={{
                    marginTop: 12,
                  }}
                  keyboardType="email-address"
                  onSubmitEditing={() => {
                    passwordRef.current.focus();
                  }}
                  showError={mailError}
                  errorMsg={mailErrorTxt}
                />
                <View style={styles.pass_container}>
                  <CInput
                    ref={passwordRef}
                    placeholder={translate("loginPassword")}
                    value={state.password}
                    // onChangeText={(val) => {
                    //   setstate({ ...state, password: val });
                    // }}
                    hideLeftIcon={false}
                    onChangeText={(val) => {
                      setstate({ ...state, password: val });
                      // const lower = isLower(val);
                      // if (lower) {
                      //   setIsLowerCase(true);
                      // } else {
                      //   setIsLowerCase(false);
                      // }
                      // const upper = isUpper(val);
                      // if (upper) {
                      //   setIsUpperCase(true);
                      // } else {
                      //   setIsUpperCase(false);
                      // }
                      // const num = isNumericCheck(val);
                      // if (num) {
                      //   setIsNumber(true);
                      // } else {
                      //   setIsNumber(false);
                      // }

                      // const sp = isSpCheck(val);
                      // if (sp) {
                      //   setIsSp(true);
                      // } else {
                      //   setIsSp(false);
                      // }
                      // const tw = isTw(val);
                      // if (tw) {
                      //   setIsTwelve(true);
                      // } else {
                      //   setIsTwelve(false);
                      // }

                      // if (val.length <= 0) {
                      //   setIsUpperCase(false);
                      //   setIsLowerCase(false);
                      //   setIsNumber(false);
                      //   setIsTwelve(false);
                      //   setIsSp(false);
                      // }
                      // if (
                      //   isUpper(val) &&
                      //   isLower(val) &&
                      //   isNumericCheck(val) &&
                      //   isSpCheck(val) &&
                      //   isTw(val)
                      // ) {
                      //   setShowCheckList(false);
                      // } else {
                      //   setShowCheckList(true);
                      // }
                    }}
                    placeholderTextColor={BaseColor.placeHolderColor}
                    iconName="unlocked-padlock"
                    textInputWrapper={{
                      marginTop: 12,
                    }}
                    // secureTextEntry
                    onSubmitEditing={() => {
                      pNumRef.current.focus();
                    }}
                    onFocus={() => {
                      setShowCheckList(true);
                    }}
                    onBlur={() => {
                      setShowCheckList(false);
                    }}
                    showError={passwordError}
                    errorMsg={passwordErrorTxt}
                    // hideRightIcon={true}
                    secureTextEntry={hideShow}
                  />
                  <TouchableOpacity
                    style={styles.eye_btn}
                    onPress={() => setHideShow(!hideShow)}
                  >
                    <FAIcon
                      name={hideShow ? "eye-slash" : "eye"}
                      style={styles.eye_icon}
                    />
                  </TouchableOpacity>
                </View>
                {showCheckList && (
                  <View
                    style={{
                      marginTop: 6,
                      marginHorizontal: 10,
                      padding: 20,
                      borderRadius: 10,
                      shadowColor: "purple",
                      height: 150,
                      width: "90%",
                      backgroundColor: "white",
                      justifyContent: "center",
                      elevation: 10,
                      shadowOffset: { width: 1, height: 1 },
                      shadowRadius: 3,
                      shadowOpacity: 0.5,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 16,
                        fontFamily: FontFamily.bold,
                        fontWeight: "700",
                      }}
                    >
                      {translate("pswdReq")}
                    </Text>
                    <View style={{ flexDirection: "row" }}>
                      <FAIcon
                        name={isLowerCase ? "check" : "remove"}
                        size={18}
                        color={
                          isLowerCase ? BaseColor.green : BaseColor.alertRed
                        }
                      />
                      <Text style={{ marginLeft: 5 }}>
                        {translate("oneLowerCase")}
                      </Text>
                    </View>

                    <View style={{ flexDirection: "row" }}>
                      <FAIcon
                        name={isUpperCase ? "check" : "remove"}
                        size={18}
                        color={
                          isUpperCase ? BaseColor.green : BaseColor.alertRed
                        }
                      />
                      <Text style={{ marginLeft: 5 }}>
                        {translate("oneUpperCase")}
                      </Text>
                    </View>

                    <View style={{ flexDirection: "row" }}>
                      <FAIcon
                        name={isNu ? "check" : "remove"}
                        size={18}
                        color={isNu ? BaseColor.green : BaseColor.alertRed}
                      />
                      <Text style={{ marginLeft: 5 }}>
                        {translate("oneNumber")}
                      </Text>
                    </View>

                    <View style={{ flexDirection: "row" }}>
                      <FAIcon
                        name={isSpecial ? "check" : "remove"}
                        size={18}
                        color={isSpecial ? BaseColor.green : BaseColor.alertRed}
                      />
                      <Text style={{ marginLeft: 5 }}>
                        {translate("oneSp")}
                      </Text>
                    </View>
                    <View style={{ flexDirection: "row" }}>
                      <FAIcon
                        name={isTwelve ? "check" : "remove"}
                        size={18}
                        color={isTwelve ? BaseColor.green : BaseColor.alertRed}
                      />
                      <Text style={{ marginLeft: 5 }}>
                        {translate("twLong")}
                      </Text>
                    </View>
                  </View>
                )}

                <CInput
                  ref={pNumRef}
                  placeholder={translate("forgotInput")}
                  value={state.pNum}
                  onChangeText={(val) => {
                    setstate({ ...state, pNum: val });
                  }}
                  placeholderTextColor={BaseColor.placeHolderColor}
                  iconName="smartphone"
                  textInputWrapper={{
                    marginTop: 12,
                  }}
                  keyboardType="number-pad"
                  onSubmitEditing={() => {
                    Keyboard.dismiss();
                  }}
                  showError={numError}
                  errorMsg={numErrorTxt}
                />
                <LinearGradient
                  start={{ x: 0, y: 0 }}
                  end={{ x: 0, y: 1 }}
                  colors={["#0000", BaseColor.white20]}
                  style={[
                    styles.countryPickerStyle,
                    {
                      backgroundColor: BaseColor.white40,
                      borderColor: BaseColor.whiteColor,
                    },
                  ]}
                >
                  <View>
                    <View>
                      <CountryPicker
                        {...{
                          countryCode: state.selectedCountry || "GB",
                          withFilter: true,
                          withFlag: true,
                          // renderFlagButton: true,
                          withCountryNameButton: true,
                          withAlphaFilter: true,
                          withCallingCode: true,
                          withEmoji: true,
                          disabled: true,
                          onSelect: (val) => {
                            setstate({
                              ...state,
                              selectedCountry: val.cca2,
                              selectedCountryName: val.name,
                              countryCode: val.callingCode[0],
                            });
                          },
                          theme: {
                            fontSize: 16,
                            onBackgroundTextColor: BaseColor.whiteColor,
                            primaryColor: BaseColor.alertRed,
                            backgroundColor: BaseColor.blueDark,
                            filterPlaceholderTextColor: BaseColor.white80,
                          },
                          // onOpen: null,
                          // onClose: () => {
                          //   // onClose();
                          // },
                        }}
                        visible={state.selectedCountry}
                      />
                    </View>
                  </View>
                </LinearGradient>
                <DropDown
                  placeholder={translate("selectLang")}
                  data={langList}
                  style={{ marginTop: 16, flex: 1, marginEnd: 4 }}
                  paddingStart={13}
                  valueProp="lang_name"
                  onSelect={(val) => {
                    // setHeight(val);
                    console.log("selLang==>>", val);
                    setstate({ ...state, selLang: val });
                  }}
                  selectedObject={state.selLang}
                  svgProp="flag"
                  lang
                />
                <TouchableOpacity
                  style={{
                    marginTop: 16,
                    flexDirection: "row",
                    alignItems: "center",
                    paddingHorizontal: 26,
                    marginBottom: 32,
                  }}
                  activeOpacity={0.7}
                  onPress={() => {
                    setstate({ ...state, agree: !state.agree });
                  }}
                >
                  <FAIcon
                    name={state.agree ? "circle" : "circle-o"}
                    size={18}
                    color={BaseColor.whiteColor}
                  />
                  <Text
                    style={[
                      styles.rememberText,
                      { color: BaseColor.whiteColor },
                    ]}
                  >
                    {translate("agreeTo")}
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.rememberText,
                      {
                        color: BaseColor.whiteColor,
                        borderBottomWidth: 1,
                        borderBottomColor: BaseColor.white90,
                        paddingVertical: 2,
                      },
                    ]}
                  >
                    <Text
                      onPress={() => {}}
                      style={{
                        fontSize: 12,
                        fontFamily: FontFamily.default,
                        color: BaseColor.whiteColor,
                        // textDecorationLine: "underline",
                      }}
                    >
                      {translate("termNCondition")}
                    </Text>
                  </TouchableOpacity>
                </TouchableOpacity>
                <View style={styles.inputWrapper}>
                  <CButton
                    style={styles.loginBtn}
                    title={translate("signup")}
                    anim
                    playAnimation={anim}
                    backAnim={backAnim}
                    onPress={() => {
                      // setanim(true);
                      Validation();
                      // setloader(true);
                      // setTimeout(() => {
                      //   setloader(false);
                      //   setdone(true);
                      // }, 2000);
                      // setTimeout(() => {
                      //   navigation.navigate("Otp");
                      //   // Validation();
                      // }, 3000);
                    }}
                    loader={loader}
                    done={done}
                  />
                </View>
              </View>
            </View>
            <View style={{ alignItems: "center", marginTop: 30 }}>
              <Text style={{ fontSize: 10, color: "white" }}>
                Version {DeviceInfo.getVersion()}
              </Text>
            </View>
          </ScrollView>
          <CButton
            iconname="cancel"
            iconStyle={{ alignItems: "flex-end" }}
            iconsize={14}
            iconColor={BaseColor.whiteColor}
            style={[styles.closeBtn, { backgroundColor: BaseColor.blue }]}
            onPress={() => {
              navigation.navigate("RedirectLS");
            }}
          />
        </KeyboardAvoidingView>
      </View>
    </>
  );
};

export default Signup;
