import { Dimensions, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";
import { Colors } from "react-native/Libraries/NewAppScreen";

const { height: dHeight, width: dWidth } = Dimensions.get("window");

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: "flex-end",
    position: "absolute",
    top: 34,
    right: 12,
    elevation: 9,
    shadowColor: "#fff",
  },
  loginBtn: {
    alignSelf: "center",
    fontFamily: FontFamily.default,
  },
  mainInputStyle: {
    // flex: 1,
    justifyContent: "center",
  },
  loginText: {
    fontSize: 28,
    color: BaseColor.whiteColor,
    fontWeight: "700",
    fontFamily: FontFamily.default,
  },
  loginTextView: {
    marginTop: 20,
    marginBottom: 20,
    alignItems: "center",
    fontFamily: FontFamily.default,
  },
  rememberText: {
    fontSize: 12,
    fontFamily: FontFamily.default,
    color: BaseColor.whiteColor,
    marginStart: 8,
  },
  countryPickerStyle: {
    height: 50,
    paddingLeft: 20,
    borderWidth: 1,
    backgroundColor: BaseColor.white60,
    borderColor: BaseColor.whiteColor,
    borderRadius: 30,
    paddingVertical: 8,
    justifyContent: "center",
    marginTop: 12,
  },
  logoImg: {
    height: 60,
    width: 60,
    alignSelf: "center",
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 7,
    // },
    // shadowOpacity: 0.41,
    // shadowRadius: 9.11,
    // elevation: 14,
  },
  pass_container: {},
  eye_btn: {
    position: "absolute",
    bottom: 18,
    right: 24,
  },
  eye_icon: {
    fontSize: 18,
    color: BaseColor.blackColor,
  },
});

export default styles;
