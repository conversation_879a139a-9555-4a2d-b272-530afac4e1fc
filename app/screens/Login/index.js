/* eslint-disable eqeqeq */
/* eslint-disable quotes */
import React, { useEffect, useRef, useState } from "react";
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  BackHandler,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  NativeModules,
} from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome";
import Toast from "react-native-simple-toast";
import { useTheme } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import CButton from "../../components/CButton";
import CInput from "../../components/CInput";
import GradientBack from "../../components/gradientBack";
import styles from "./styles";
import { FontFamily } from "../../config/typography";
import { translate } from "../../lang/Translate";
import { getApiData } from "../../utils/apiHelper";
// import Bugsnag from "@bugsnag/react-native";
import BaseSetting from "../../config/setting";
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from "../../utils/commonFunction";
import AuthAction from "../../redux/reducers/auth/actions";
import DeviceInfo from "react-native-device-info";

const Login = ({ navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const dispatch = useDispatch();
  const { setUserData, setAccessToken } = AuthAction;
  const brandToken = useSelector((state) => state.auth.brandToken);

  const [state, setstate] = useState({
    // email: "<EMAIL>",
    // password: "Jason2013!",
    email: "",
    password: "",
    rememberMe: false,
  });
  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);

  const [emailError, setEmailError] = useState(false);
  const [passwordError, setPasswordError] = useState(false);

  const [emailErrorTxt, setEmailErrorTxt] = useState("");
  const [passwordErrorTxt, setPasswordErrorTxt] = useState("");

  const userRef = useRef();
  const passwordRef = useRef();

  function handleBackButtonClick() {
    navigation.navigate("RedirectLS");
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const Validation = () => {
    // const passVal = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

    enableAnimateInEaseOut();
    if (state.email == "") {
      allErrorFalse();
      setEmailError(true);
      setEmailErrorTxt(translate("enterusername"));
    } else if (state.password == "") {
      allErrorFalse();
      setPasswordError(true);
      setPasswordErrorTxt(translate("enterpassword"));
    }
    // else if (!passVal.test(String(state.password))) {
    //   allErrorFalse();
    //   setPasswordError(true);
    //   setPasswordErrorTxt('Please enter valid Password');
    // }
    else {
      allErrorFalse();
      userLogin();
    }
  };

  const allErrorFalse = () => {
    setEmailError(false);
    setPasswordError(false);
  };

  const userLogin = () => {
    setloader(true);
    setanim(true);
    setBackAnim(false);
    const data = {
      username: state.email,
      password: state.password,
      token: brandToken,
    };
    // Toast.show("End Point", BaseSetting.endpoints.login);
    // alert(BaseSetting.endpoints.login);
    getApiData(BaseSetting.endpoints.login, "POST", data)
      .then((response) => {
        // alert(response);
        // Toast.show(response);
        // Toast.show("response", response);
        if (response.success) {
          dispatch(setUserData(response?.data?.user));
          dispatch(setAccessToken(response?.data?.token));
          // dispatch(setUserId(response?.data?.user?.id));
          // setTimeout(() => {
          //   setTimeout(() => {
          //     setloader(false);
          //     setdone(true);
          //   }, 1000);
          //   navigation.navigate("DrawerNav");
          // }, 4000);
          if (response?.data?.user?.status === "inactive") {
            sendOTP(response?.data?.user?.id);
          } else {
            setTimeout(() => {
              setTimeout(() => {
                setloader(false);
                setdone(true);
              }, 2000);
              navigation.navigate("DrawerNav");
            }, 3000);
          }
        } else {
          Toast.show(response.message || "Something went wrong");
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch((err) => {
        // alert(err);
        Toast.show("Something went wrong while trying to login");
        sendErrorReport(err, "login_api");
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log("ERRR", err);
      });
  };

  const sendOTP = (userID) => {
    setloader(true);
    const data = {
      user_id: userID,
    };

    getApiData(BaseSetting.endpoints.sendOtp, "POST", data)
      .then((response) => {
        if (response.success) {
          setTimeout(() => {
            setloader(false);
            setdone(true);
            // }, 2000);
            // setTimeout(() => {
            navigation.navigate("Otp", {
              type: "LoginInactive",
              userId: userID,
            });
          }, 2000);
        } else {
          Toast.show(response.message);
          //BUGSNAG TEST
          // Bugsnag.notify(response.message, function (report) {
          //   report.metadata = {
          //     data: {
          //       error: response.message,
          //     },
          //   };
          // });
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch((err) => {
        Toast.show("Something went wrong while sending otp request");
        sendErrorReport(err, "send_otp");
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log("ERRR", err);
      });
  };

  return (
    <View style={styles.root}>
      <GradientBack />
      {/* <View style={styles.mainContainer}> */}
      <ScrollView
        contentContainerStyle={{ flexGrow: 1, padding: 16 }}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS == "ios" ? "padding" : null}
          style={{
            justifyContent: "center",
            flex: 1,
          }}
        >
          <View style={styles.mainInputStyle}>
            <View style={styles.logoImg}>
              <Image
                source={require("../../assets/images/logo-1.png")}
                style={{ height: "100%", width: "100%" }}
              />
            </View>
            <Text style={[styles.loginText, { color: BaseColor.whiteColor }]}>
              {translate("loginscreen")}
            </Text>
            <CInput
              ref={userRef}
              placeholder={translate("loginUser")}
              keyboardType="email-address"
              value={state.email}
              onChangeText={(val) => {
                setstate({ ...state, email: val });
              }}
              placeholderTextColor={BaseColor.placeHolderColor}
              iconName="user-2"
              onSubmitEditing={() => {
                passwordRef.current.focus();
              }}
              showError={emailError}
              errorMsg={emailErrorTxt}
            />
            <CInput
              ref={passwordRef}
              placeholder={translate("loginPassword")}
              value={state.password}
              secureTextEntry
              onChangeText={(val) => {
                setstate({ ...state, password: val });
              }}
              placeholderTextColor={BaseColor.placeHolderColor}
              iconName="unlocked-padlock"
              textInputWrapper={{
                marginTop: 12,
              }}
              showError={passwordError}
              errorMsg={passwordErrorTxt}
              onSubmitEditing={() => {
                Validation();
              }}
            />
          </View>
          <TouchableOpacity
            style={{
              marginTop: 12,
              flexDirection: "row",
              alignItems: "center",
              paddingHorizontal: 26,
            }}
            activeOpacity={0.7}
            onPress={() => {
              setstate({ ...state, rememberMe: !state.rememberMe });
            }}
          >
            <FAIcon
              name={state.rememberMe ? "circle" : "circle-o"}
              size={18}
              color={BaseColor.whiteColor}
            />
            <Text style={[styles.rememberText, { color: BaseColor.white90 }]}>
              {translate("loginRemember")}
            </Text>
          </TouchableOpacity>
          <CButton
            style={styles.loginBtn}
            anim
            playAnimation={anim}
            backAnim={backAnim}
            onPress={() => {
              Validation();
              // NativeModules.MaxRCTCarPlayNotificationManager.sendCarPlayNotification('Samrez', 'Ikram');
            }}
            title={translate("loginBtn")}
            loader={loader}
            done={done}
          />
          <TouchableOpacity
            onPress={() => {
              navigation.navigate("ForgotPassword");
            }}
            style={{ alignItems: "center", marginTop: 24 }}
            activeOpacity={0.7}
          >
            <Text
              style={{
                fontSize: 15,
                color: BaseColor.whiteColor,
                fontWeight: "600",
                textAlign: "left",
              }}
            >
              {translate("loginForgot")}
            </Text>
          </TouchableOpacity>

          <View style={{ marginTop: 36, alignItems: "center" }}>
            <Text
              style={{
                fontSize: 14,
                color: BaseColor.whiteColor,
                textAlign: "left",
                letterSpacing: 0.8,
                fontFamily: FontFamily.default,
              }}
            >
              {translate("loginAccount")}
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate("Signup");
            }}
            style={{ paddingVertical: 5, alignItems: "center" }}
          >
            <Text
              style={{
                fontSize: 14,
                color: BaseColor.whiteColor,
                fontWeight: "bold",
                textAlign: "left",
                letterSpacing: 1,
                borderBottomWidth: 1.5,
                fontFamily: FontFamily.default,
                borderBottomColor: BaseColor.whiteColor,
              }}
            >
              {translate("loginToSignup")}
            </Text>
          </TouchableOpacity>
        </KeyboardAvoidingView>
        <View style={{ alignItems: "center" }}>
          <Text style={{ fontSize: 10, color: "white" }}>
            Version {DeviceInfo.getVersion()}
          </Text>
        </View>
      </ScrollView>
      <CButton
        iconname="cancel"
        iconsize={14}
        iconColor={BaseColor.whiteColor}
        style={[styles.closeBtn, { backgroundColor: BaseColor.blue }]}
        onPress={() => {
          navigation.navigate("RedirectLS");
        }}
      />
      {/* </View> */}
    </View>
  );
};

export default Login;
