/* eslint-disable indent */
/* eslint-disable quotes */
/* eslint-disable react/jsx-indent */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  // ImagePickerIOS,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import ActionButton from "react-native-action-button";
import IoIcon from "react-native-vector-icons/Ionicons";
import { isEmpty, isObject, isString } from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { useTheme } from "@react-navigation/native";
// import ImagePicker from "react-native-image-crop-picker";
import moment from "moment";
import Toast from "react-native-simple-toast";
import CHeader from "../../components/CHeader";
import GradientBack from "../../components/gradientBack";
import { CustomIcon } from "../../config/LoadIcons";
import { FontFamily } from "../../config/typography";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import SocketAction from "../../redux/reducers/socket/actions";
import BaseSetting from "../../config/setting";
import { getApiData, getApiDataProgress } from "../../utils/apiHelper";
import { openInAppBrowser, sendErrorReport } from "../../utils/commonFunction";
import socketAction from "../../redux/reducers/socket/actions";
import AuthAction from "../../redux/reducers/auth/actions";

const ChatScreen = ({ navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const { onReceive, emit } = socketAction;
  const { setLeadId } = AuthAction;

  const dispatch = useDispatch();
  const accessToken = useSelector((state) => state.auth.accessToken);
  const darkmode = useSelector((state) => state.auth.darkmode);

  const [msgtext, setmsgtext] = useState("");
  const [chatArr, setchatArr] = useState([]);
  const [uploadLoader, setUploadLoader] = useState(false);
  const [inputFocus, setInputFocus] = useState(false);
  const [chatClosed, setChatClosed] = useState(false);
  const chatFlatlistRef = useRef();
  const msgInputRef = useRef();

  const { chatData, typing, typingData } = useSelector((state) => state.socket);
  const userData = useSelector((state) => state.auth.userData);

  const leadId = useSelector((state) => state.auth.leadId);

  useEffect(() => {
    // {"close": true}
    const tempArr = chatArr;
    setChatClosed(false);
    if (chatData.close) {
      setChatClosed(true);
      dispatch(setLeadId(null));
    } else {
      setChatClosed(false);
      chatArr.push(chatData);
    }
  }, [chatData]);

  useEffect(() => {
    console.log("TYPING****", typing);
    console.log("typingData", typingData);
  }, [typingData]);

  const userID = userData.id;

  const renderData = ({ item, index }) => (
    <View style={{ marginVertical: 12, paddingHorizontal: 12 }}>
      {/* {item.type !== 'msg' ? ( */}
      <View
        style={{
          flexDirection: item?.sender_id == userID ? "row-reverse" : "row",
        }}
      >
        {item?.sender_id != userID ? (
          <Image
            source={
              item?.user?.avatar
                ? item?.user?.avatar
                : require("../../assets/images/6.jpg")
            }
            style={{ height: 40, width: 40, borderRadius: 40 }}
          />
        ) : null}
        <View
          style={{
            width: "60%",
          }}
        >
          {item?.sender_id === userID ? (
            <Text
              style={{
                alignSelf: "flex-end",
                marginEnd: 16,
                marginBottom: 4,
                color: BaseColor.whiteColor,
                fontFamily: FontFamily.default,
                fontWeight: "bold",
              }}
            >
              {userData.full_name}
            </Text>
          ) : null}
          <View
            style={{
              padding: 16,
              backgroundColor: BaseColor.whiteColor,
              marginHorizontal: 16,
              borderRadius: 16,
              borderBottomStartRadius: item?.sender_id === userID ? 16 : 0,
              borderBottomEndRadius: item?.sender_id === userID ? 0 : 16,
            }}
          >
            {item?.type === "text" ? (
              <Text
                style={{
                  fontFamily: FontFamily.default,
                  fontSize: 12,
                  color: BaseColor.blackColor,
                }}
              >
                {item?.message}
              </Text>
            ) : item?.fileType &&
              isString(item?.fileType) &&
              !item?.fileType.includes("image") ? (
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Text
                  style={{
                    fontFamily: FontFamily.default,
                    fontSize: 16,
                    color: BaseColor.blackColor,
                    flex: 1,
                    fontWeight: "bold",
                    marginEnd: 4,
                  }}
                >
                  {item?.file_name}
                </Text>
                <TouchableOpacity
                  style={{
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                  onPress={() => {
                    if (item?.file_url) {
                      openInAppBrowser(item?.file_url);
                    } else {
                      Toast.show("Can't open file");
                    }
                  }}
                >
                  <IoIcon
                    name="download-outline"
                    size={22}
                    color={BaseColor.blueDark}
                  />
                </TouchableOpacity>
              </View>
            ) : (
              <TouchableOpacity
                onPress={() => {
                  if (item?.file_url) {
                    openInAppBrowser(item?.file_url);
                  } else {
                    Toast.show("Can't open file");
                  }
                }}
                style={{
                  margin: -16,
                  borderRadius: 16,
                  borderBottomStartRadius: item?.sender_id === userID ? 16 : 0,
                  borderBottomEndRadius: item?.sender_id === userID ? 0 : 16,
                  overflow: "hidden",
                }}
              >
                <Image
                  source={
                    item?.file_url
                      ? { uri: item?.file_url }
                      : require("../../assets/images/6.jpg")
                  }
                  style={{ height: 120, width: "100%" }}
                />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
      {/* {item.type !== "msg" ? null : (
          <View
            style={{
              alignItems: "center",
              justifyContent: "center",
              flexDirection: "row",
            }}
          >
            <View
              style={{
                height: 2,
                flex: 1,
                backgroundColor: BaseColor.white40,
                marginHorizontal: 32,
              }}
            />
            <Text
              style={{
                backgroundColor: BaseColor.white50,
                color: BaseColor.whiteColor,
                borderRadius: 16,
                borderWidth: 1,
                borderColor: BaseColor.whiteColor,
                fontSize: 12,
                padding: 2,
                paddingHorizontal: 8,
                overflow: "hidden",
              }}
            >
              {item.time}
            </Text>
            <View
              style={{
                height: 2,
                flex: 1,
                backgroundColor: BaseColor.white40,
                marginHorizontal: 32,
              }}
            />
          </View>
        )} */}
    </View>
  );

  useEffect(() => {
    setchatArr([]);
    getChat();
  }, []);

  const getChat = () => {
    const data = leadId
      ? {
          lead_id: leadId,
          page: 1,
          per_page: 150,
        }
      : {
          page: 1,
          per_page: 150,
        };
    const headers = {
      // 'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : "",
    };

    getApiData(BaseSetting.endpoints.getAppChat, "get", data, headers)
      .then((respose) => {
        console.log("sendAttachment Response ====>>>>", respose);
        if (respose?.data) {
          setchatArr(respose?.data);
        } else {
          setchatArr([]);
        }
      })
      .catch((err) => {
        sendErrorReport(err, "get_chat");
        console.log("sendAttachment ERROR", err);
      });
  };

  const sendTyping = (stop) => {
    const data = leadId
      ? {
          lead_id: leadId,
          receiver_id: userData.brand_id,
          stop,
        }
      : {
          receiver_id: userData.brand_id,
          stop,
        };
    dispatch(
      emit("/api/chat/chat-typing", data, (callBackD) => {
        const callBackData = callBackD;
        console.log("chat-typing ===", callBackData);
      })
    );
  };

  const sendMsg = () => {
    Keyboard.dismiss();
    if (!isEmpty(msgtext)) {
      const data = {
        message: msgtext,
        receiver_id: userData.brand_id,
      };
      console.log("sendMsg -> data", data);

      dispatch(
        emit("/api/chat/send-message", data, (callBackD) => {
          const callBackData = callBackD;
          console.log("callBackData ===", callBackData);
          dispatch(setLeadId(callBackData?.data?.lead_id));
          // const tempArr = chatArr;
          chatArr.push(callBackData.data);
          // console.log(
          //   "chillbaby ~ file: index.js ~ line 282 ~ emit ~ tempArr",
          //   tempArr
          // );
          setmsgtext("");
        })
      );
    }
  };

  function handleBackButtonClick() {
    if (chatData.close) {
      dispatch(onReceive({}));
    }
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const clickImage = () => {
    // ImagePicker.openCamera({
    //   width: 200,
    //   height: 200,
    //   compressImageQuality: 0.1,
    //   cropping: true,
    // }).then((image) => {
    //   sendAttachment(image);
    //   console.log("image uri:", image);
    // });
  };

  const pickImage = () => {
    // ImagePicker.openPicker({
    //   cropping: true,
    // }).then((image) => {
    //   sendAttachment(image);
    // });
  };

  const pickDocument = async () => {};

  async function uploadAttachment(response) {
    if (response && response.success) {
      const resParamsData = {
        receiver_id: userData.brand_id,
        // sender_id: userID,
        ...response,
      };

      const headers = {
        "Content-Type": "application/json",
        authorization: `Bearer ${accessToken}`,
      };

      try {
        const paramRes = await getApiData(
          BaseSetting.endpoints.insertAttachment,
          "post",
          resParamsData,
          headers
        );
        if (paramRes && paramRes.success) {
          getChat();
          setUploadLoader(false);
        } else {
          // notification('error', 'Unable to upload file');
        }
      } catch (error) {
        console.log("error upload attach ===", error);
        sendErrorReport(error, "unable_upload_attachment");
      }
    } else {
      console.log("Unable to upload file");
      sendErrorReport(
        JSON.stringify({ err: "unable_upload_attachment" }),
        "unable_upload_attachment_else"
      );
    }
  }

  const sendAttachment = async (image) => {
    setUploadLoader(true);
    const name =
      isObject(image) && image.path
        ? image.path.substring(image.path.lastIndexOf("/") + 1)
        : "";

    const formdata = new FormData();

    // const data = {
    //   lead_id: 96,
    //   sender_id: userID,
    //   u_id: userID,
    //   uid: fUri,
    //   file_name: fName,
    //   size: fSize,
    //   fileType: fType,
    //   type: 'file',
    //   loading: true,
    // };

    formdata.append("attachment", {
      uri: image.path,
      name,
      type: image.mime,
    });

    try {
      // const formData = new FormData();
      // formData.append('attachment', file);

      fetch(BaseSetting.api + BaseSetting.endpoints.uploadChatAttachment, {
        method: "post",
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `Bearer ${accessToken}`,
        },
        body: formdata,
      })
        .then((response) => response.json())
        .then((responseJson) => {
          uploadAttachment(responseJson);
        })
        .catch((err) => {
          sendErrorReport(err, "upload_attachment");
          console.log("err ===", err);
        });

      // if (response && response.success && msgIndex > -1) {
      //   const resParamsData = {
      //     lead_id: selectedRoom.lead_id,
      //     receiver_id: selectedRoom.u_id,
      //     sender_id: uId,
      //     ...response,
      //   };
      //   const paramRes = await getApiData(
      //     'insertAttachment',
      //     resParamsData,
      //     'POST',
      //   );
      //   if (paramRes && paramRes.success) {
      //     msgData.file_url = response.fullUrl ? response.fullUrl : '';
      //   } else {
      //     notification('error', 'Unable to upload file');
      //   }
      //   msgData.loading = false;
      //   msg[msgIndex] = msgData;
      //   dispatch(setMessages(msg));
      // } else {
      //   msgData.loading = false;
      //   msgData.error = true;
      //   notification('error', 'Unable to upload file');
      //   dispatch(setMessages(msg));
      // }
    } catch (err) {
      console.log("error", err);
      sendErrorReport(err, "send_attach_catch");
    }
  };

  return (
    <>
      <StatusBar barStyle={darkmode ? "dark-content" : "light-content"} />
      <View style={[styles.root, { backgroundColor: BaseColor.blackColor }]}>
        <CHeader
          title="Customer Services"
          leftBackColor={BaseColor.backColor}
          backBtn
          leftIconName
          onLeftPress={() => {
            if (chatData.close) {
              dispatch(onReceive({}));
            }
            navigation.goBack();
          }}
        />
        <KeyboardAvoidingView
          style={{
            borderTopEndRadius: 64,
            flex: 1,
            overflow: "hidden",
          }}
          behavior={Platform.OS === "ios" ? "height" : null}
        >
          <GradientBack />
          <FlatList
            ref={chatFlatlistRef}
            data={chatArr}
            keyExtractor={(item, index) => index}
            renderItem={renderData}
            contentContainerStyle={{
              // backgroundColor: "red",
              flexGrow: 1,
              justifyContent: "flex-end",
            }}
            ListEmptyComponent={() => (
              <View
                style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: "bold",
                    fontFamily: FontFamily.default,
                    color: BaseColor.whiteColor,
                  }}
                >
                  Send Your Query Here
                </Text>
              </View>
            )}
            showsVerticalScrollIndicator={false}
            onContentSizeChange={() =>
              chatFlatlistRef.current.scrollToEnd({ animated: true })
            }
            onLayout={() =>
              chatFlatlistRef.current.scrollToEnd({ animated: true })
            }
          />
          {uploadLoader ? (
            <View
              style={{
                width: "62%",
                alignSelf: "flex-end",
                marginVertical: 12,
                paddingHorizontal: 12,
              }}
            >
              <View
                style={{
                  padding: 16,
                  backgroundColor: BaseColor.whiteColor,
                  marginHorizontal: 16,
                  borderRadius: 16,
                  borderBottomStartRadius: 16,
                  borderBottomEndRadius: 0,
                  flexDirection: "row",
                }}
              >
                <Text>{"Sending...    "}</Text>
                <ActivityIndicator size={16} color={BaseColor.blueDark} />
              </View>
            </View>
          ) : null}
          {!isEmpty(typingData) && !typingData.stop ? (
            <Text
              style={{
                color: BaseColor.whiteColor,
                alignSelf: "flex-end",
                marginBottom: 8,
                marginEnd: 24,
              }}
            >
              Typing...
            </Text>
          ) : null}
          {!chatClosed ? (
            <>
              <View
                style={{
                  flexDirection: "row",
                  padding: 8,
                  paddingHorizontal: 16,
                  backgroundColor: BaseColor.whiteColor,
                  alignItems: "center",
                  borderTopEndRadius: 48,
                  paddingTop: 22,
                }}
              >
                <View
                  style={{
                    height: "100%",
                    flex: 1,
                    marginStart: 48,
                  }}
                >
                  <TextInput
                    ref={msgInputRef}
                    onFocus={() => {
                      setInputFocus(true);
                    }}
                    onBlur={() => {
                      setInputFocus(false);
                    }}
                    style={{
                      marginHorizontal: 16,
                      fontFamily: FontFamily.default,
                      fontSize: 14,
                      flex: 1,
                      maxHeight: 120,
                      height: "100%",
                      marginTop: -12,
                      color: BaseColor.blackColor,
                    }}
                    placeholder={translate("chatInputText")}
                    // returnKeyType="none"
                    placeholderTextColor={BaseColor.black50}
                    multiline
                    value={msgtext}
                    onChangeText={(val) => {
                      setmsgtext(val);
                      sendTyping(false);

                      setTimeout(() => {
                        sendTyping(true);
                      }, 700);
                    }}
                  />
                </View>

                <TouchableOpacity
                  activeOpacity={0.7}
                  onPress={sendMsg}
                  style={{
                    alignSelf: "flex-end",
                    backgroundColor: BaseColor.blueDark,
                    borderRadius: 40,
                    justifyContent: "center",
                    alignItems: "center",
                    marginBottom: 8,
                  }}
                >
                  <CustomIcon
                    name="send-2"
                    color={BaseColor.whiteColor}
                    size={16}
                    style={{
                      padding: 12,
                    }}
                  />
                </TouchableOpacity>
              </View>
              {!uploadLoader ? (
                <ActionButton
                  buttonColor={BaseColor.black50}
                  size={32}
                  position="left"
                  offsetX={12}
                  offsetY={22}
                >
                  <ActionButton.Item
                    buttonColor={BaseColor.blackColor}
                    onPress={clickImage}
                  >
                    <IoIcon
                      name="camera-outline"
                      color={BaseColor.whiteColor}
                      size={18}
                    />
                  </ActionButton.Item>
                  <ActionButton.Item
                    buttonColor={BaseColor.blackColor}
                    onPress={pickImage}
                  >
                    <IoIcon
                      name="images-outline"
                      color={BaseColor.whiteColor}
                      size={18}
                    />
                  </ActionButton.Item>
                  <ActionButton.Item
                    buttonColor={BaseColor.blackColor}
                    onPress={pickDocument}
                  >
                    <IoIcon
                      name="md-documents-outline"
                      color={BaseColor.whiteColor}
                      size={18}
                    />
                  </ActionButton.Item>
                </ActionButton>
              ) : (
                <ActivityIndicator
                  style={{ position: "absolute", bottom: 24, left: 12 }}
                  color={BaseColor.alertRed}
                  size={32}
                />
              )}
            </>
          ) : (
            <View
              style={{
                flexDirection: "row",
                padding: 8,
                paddingHorizontal: 16,
                backgroundColor: BaseColor.whiteColor,
                alignItems: "center",
                borderTopEndRadius: 48,
                paddingTop: 22,
              }}
            >
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: FontFamily.default,
                  textAlign: "center",
                  textAlignVertical: "center",
                  flex: 1,
                  marginHorizontal: 16,
                  fontWeight: "bold",
                  marginBottom: 8,
                }}
              >
                {translate("ticketClosed")}
              </Text>
            </View>
          )}
        </KeyboardAvoidingView>
      </View>
    </>
  );
};

export default ChatScreen;
