
/*
Based on <PERSON> example for IDF: https://github.com/nkolban/esp32-snippets/blob/master/cpp_utils/tests/BLE%20Tests/SampleServer.cpp
Ported to <PERSON><PERSON><PERSON>o ESP32 by <PERSON><PERSON><PERSON>
updates by <PERSON><PERSON><PERSON>
*/



#include <BLEDevice.h>
#include <BLEUtils.h>
#include <BLEServer.h>


// See the following for generating UUIDs:
// https://www.uuidgenerator.net/

#define SERVICE_UUID "7474a640-7149-11ec-90d6-0242ac120003"
#define CHARACTERISTIC_UUID "beb5483e-36e1-4688-b7f5-ea07361b26a8"
BLECharacteristic *pCharacteristic;
int i = 0;
//a#include <HX711_ADC.h>
#include <EEPROM.h>
//pins:
const int HX711_dout = 5; //mcu > HX711 dout pin
const int HX711_sck = 2; //mcu > HX711 sck pin

//HX711 constructor:
//HX711_ADC LoadCell(HX711_dout, HX711_sck);
////////////////////////////////////////////////////temperature sensor
//#include "DHT.h"
//#define DHTPIN 4 // Digital pin connected to the DHT sensor
//#define DHTTYPE DHT11 // DHT 11
//#define DHTTYPE DHT22 // DHT 22 (AM2302), AM2321
//#define DHTTYPE DHT21 // DHT 21 (AM2301)
//DHT dht(DHTPIN, DHTTYPE);




const int calVal_eepromAdress = 0;
long t;

long scaleTick, httpTick;

float utt=0;
int lef_sensor=39;
int bat_s=36;
int green=25;
int red=26;
int blue=27;
unsigned int left_sensor_v=0;

int sen1=0;
int sen2=0;
int sen3=0;
int sen4=0;
int sen5=0;
int sen6=0;
int sen7=0;
int sen8=0;
int sen9=0;
float sen10=0;
float sen11=0;
float battery_voltage=0;
int fan=18;
int heater=19;




void setup() {
Serial.begin(115200);
Serial.println("Starting BLE work!");
pinMode(lef_sensor,INPUT);
pinMode(34,INPUT);
pinMode(35,INPUT);
pinMode(32,INPUT);

pinMode(fan,OUTPUT);
pinMode(heater,OUTPUT);

pinMode(red,OUTPUT);
pinMode(green,OUTPUT);
pinMode(blue,OUTPUT);
delay(500);
//LoadCell.begin();
//float calibrationValue; // calibration value (see example file "Calibration.ino")
//calibrationValue = 696.0; // uncomment this if you want to set the calibration value in the sketch
#if defined(ESP8266)|| defined(ESP32)
//EEPROM.begin(512); // uncomment this if you use ESP8266/ESP32 and want to fetch the calibration value from eeprom
#endif
//EEPROM.get(calVal_eepromAdress, calibrationValue); // uncomment this if you want to fetch the calibration value from eeprom

long stabilizingtime = 2000; // preciscion right after power-up can be improved by adding a few seconds of stabilizing time
boolean _tare = true; //set this to false if you don't want tare to be performed in the next step

delay(500);
BLEDevice::init("CBTBA3bfc6");
BLEServer *pServer = BLEDevice::createServer();
BLEService *pService = pServer->createService(SERVICE_UUID);

/* To notify the connected clients that new update is available - Was missing by Firmware devs */
pCharacteristic = pService->createCharacteristic(
CHARACTERISTIC_UUID,
BLECharacteristic::PROPERTY_READ |
BLECharacteristic::PROPERTY_WRITE |
BLECharacteristic::PROPERTY_NOTIFY          // Added by Krunal to fix
);

pCharacteristic->setValue("{\"s1\":sensor_1,\"s2\":sensor_2,\"s3\":sensor_3,\"s4\":sensor_4,\"s5\":sensor_5,\"s6\":sensor_6,\"s7\":sensor_7,\"s8\":sensor_8,\"s9\":sensor_9,\"TEMP\":int(sensor_10),\"HUMIDITY\":int(sensor_11),\"bv\":battery_voltage}");

pService->start();
// BLEAdvertising *pAdvertising = pServer->getAdvertising(); // this still is working for backward compatibility
BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
pAdvertising->addServiceUUID(SERVICE_UUID);
pAdvertising->setScanResponse(true);
pAdvertising->setMinPreferred(0x06); // functions that help with iPhone connections issue
pAdvertising->setMinPreferred(0x12);
BLEDevice::startAdvertising();
Serial.println("Characteristic defined! Now you can read it in your phone!");
Serial.println("Starting...");
delay(30);


}

long sc=0;
long us=0;


float temp=0;
float hum=0;
String str = "";
String str2 = "";
float weight=0.001;

void loop() {
    // Generate realistic random sensor values
    int sen2  = random(0, 2);
    int sen3  = random(0, 2);
    int sen4  = random(0, 2);
    int sen5  = random(0, 2);
    int sen6  = random(0, 2);
    int sen7  = random(0, 2);
    int sen8  = random(0, 2);
    int sen9  = random(0, 2);
  
    float sen10 = random(250, 400) / 10.0; // TEMP: 25.0 to 40.0
    float sen11 = random(400, 800) / 10.0; // HUMIDITY: 40.0 to 80.0
    float battery_voltage = random(360, 430) / 100.0; // 3.60 to 4.30
  
    int sen12 = random(80, 100);  // Harness angle
    int sen13 = random(0, 10);    // Height (Pot)
    int sen14 = random(0, 5);     // Harness tension
    int sen18 = random(30, 50);   // Child height
  
    float temp = sen10;
    float hum = sen11;
  
    send_to_server(sen2, sen3, sen4, sen5, sen6, sen7, sen8, sen9,
                   sen10, sen11, battery_voltage, sen12, sen13, sen14, sen18,
                   temp, hum);
  
    delay(3000);
  }


void send_to_server(
    int sensor_2, int sensor_3, int sensor_4, int sensor_5, int sensor_6,
    int sensor_7, int sensor_8, int sensor_9,
    float sensor_10, float sensor_11, float bv,
    int sensor_12, int sensor_13, int sensor_14, int sensor_18,
    float temp, float hum
  ) {
    str = "{";
    str += "\"s2\":" + String(sensor_2) + ",";
    str += "\"s3\":" + String(sensor_3) + ",";
    str += "\"s4\":" + String(sensor_4) + ",";
    str += "\"s5\":" + String(sensor_5) + ",";
    str += "\"s6\":" + String(sensor_6) + ",";
    str += "\"s7\":" + String(sensor_7) + ",";
    str += "\"s8\":" + String(sensor_8) + ",";
    str += "\"s9\":" + String(sensor_9) + ",";
    str += "\"s10\":" + String(sensor_10, 1) + ",";
    str += "\"s11\":" + String(sensor_11, 1) + ",";
    str += "\"s12\":" + String(sensor_12) + ",";
    str += "\"s13\":" + String(sensor_13) + ",";
    str += "\"s14\":" + String(sensor_14) + ",";
    str += "\"s18\":" + String(sensor_18) + ",";
    str += "\"TEMP\":" + String(temp, 1) + ",";
    str += "\"HUMIDITY\":" + String(hum, 1) + ",";
    str += "\"bv\":" + String(bv, 2);
    str += "}";
//int(sensor_10),\"HUMIDITY\":int(sensor_11),\"bv\":battery_voltage}"

//str2="{\"s1\":"+sensor_1+",\"s2\":"+sensor_2+",\"s3\":"+sensor_3+",\"s4\":"+sensor_4+",\"s5\":"+sensor_5+",\"s6\":"+sensor_6+",\"s7\":"+sensor_7+",\"s8\":"+sensor_8+",\"s9\":"+sensor_9+",\"TEMP\":"+int(sensor_10)+",\"HUMIDITY\":"+int(sensor_11)+",\"bv\":"+battery_voltage}";

Serial.print("Data Jason String-->");Serial.println(str);
/*
{
"s1": sensor_1,
"s2": sensor_22,
"s3": sensor_3,
"s4": sensor_4,
"s5": sensor_5,
"s6": sensor_6,
"s7": sensor_7,
"s8": sensor_8,
"s9": sensor_9,
"TEMP": int(sensor_10),
"HUMIDITY": int(sensor_11),
"bv": battery_voltage
}
*/
pCharacteristic->setValue(str.c_str());
/* To notify the connected clients that new update is available - Was missing by Firmware devs */
pCharacteristic->notify();

}